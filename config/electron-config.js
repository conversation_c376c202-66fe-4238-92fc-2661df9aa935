/**
 * 🚀 LOUNA AI ELECTRON - CONFIGURATION COMPLÈTE
 * Configuration centralisée pour l'application Electron
 */

module.exports = {
    // 🖥️ CONFIGURATION ELECTRON
    electron: {
        window: {
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            backgroundColor: '#1a1a2e',
            title: '🧠 LOUNA AI Ultra-Autonome - DeepSeek R1 8B'
        },
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false,
            allowRunningInsecureContent: true,
            experimentalFeatures: true
        }
    },

    // 🌐 CONFIGURATION SERVEUR
    server: {
        port: 52796,
        host: 'localhost',
        timeout: 30000,
        maxRetries: 3
    },

    // 🤖 CONFIGURATION DEEPSEEK R1 8B
    deepseek: {
        model: 'deepseek-r1:8b',
        host: '127.0.0.1',
        port: 11434,
        timeout: 30000,
        maxTokens: 2048,
        temperature: 0.8,
        topP: 0.9,
        maxRetries: 3,
        connectionStability: 'ultra-robust'
    },

    // 🧠 CONFIGURATION MÉMOIRE THERMIQUE
    thermalMemory: {
        zones: 6,
        maxEntriesPerZone: 50000,
        temperatureRange: {
            min: 35.0,
            max: 42.0,
            optimal: 37.0
        },
        backupInterval: 300000, // 5 minutes
        compressionEnabled: true,
        persistenceEnabled: true
    },

    // 🌀 CONFIGURATION SYSTÈME MÖBIUS
    mobius: {
        phases: ['exploration', 'analysis', 'synthesis', 'reflection', 'integration', 'transformation', 'emergence', 'convergence'],
        cycleSpeed: 0.001,
        chaosLevel: 0.3,
        authenticReflectionRate: 0.8, // 80% de vraies réflexions DeepSeek
        thoughtGenerationInterval: 15000, // 15 secondes
        maxThoughts: 40
    },

    // 🧮 CONFIGURATION NEURONES ET QI
    brain: {
        baseNeurons: 1064000,
        neurogenesisRate: 2, // neurones par seconde
        maxNeurons: 10000000,
        synapseMultiplier: 7,
        qiCalculation: {
            baseIQ: 100,
            memoryBonus: 0.1,
            neuronBonus: 0.0001,
            maxIQ: 1000
        }
    },

    // 🛡️ CONFIGURATION SÉCURITÉ
    security: {
        guardian: {
            enabled: true,
            monitoringInterval: 5000,
            maxResponseTime: 30000,
            emergencyDisconnectThreshold: 60000,
            securityLevel: 'MAXIMUM'
        },
        internet: {
            antiVirusEnabled: true,
            realTimeProtection: true,
            maxRequestSize: 2097152, // 2MB
            timeout: 15000,
            securityLevel: 'MAXIMUM'
        }
    },

    // 🌐 CONFIGURATION MCP (ChatGPT)
    mcp: {
        enabled: true,
        browser: 'opera',
        chatgptUrl: 'https://chatgpt.com',
        conversationName: 'mémoire résiliante expliquer',
        autoDialogue: true,
        questionInterval: 30000, // 30 secondes
        maxQuestions: 10
    },

    // 🎨 CONFIGURATION INTERFACE
    ui: {
        theme: {
            primary: '#ff6b9d',
            secondary: '#00d4aa',
            background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%)',
            accent: '#f39c12'
        },
        animations: {
            enabled: true,
            duration: 300,
            easing: 'ease-in-out'
        },
        voice: {
            enabled: true,
            language: 'fr-FR',
            rate: 0.8,
            pitch: 1.1,
            volume: 0.7
        }
    },

    // 📊 CONFIGURATION MÉTRIQUES
    metrics: {
        updateInterval: 2000, // 2 secondes
        historyLength: 100,
        realTimeEnabled: true,
        exportEnabled: true
    },

    // 🔧 CONFIGURATION DÉVELOPPEMENT
    development: {
        debug: process.env.NODE_ENV === 'development',
        devTools: process.env.ELECTRON_IS_DEV === '1',
        hotReload: true,
        logging: {
            level: 'info',
            file: 'logs/louna-ai.log',
            maxSize: '10MB',
            maxFiles: 5
        }
    },

    // 📁 CONFIGURATION CHEMINS
    paths: {
        data: './data',
        logs: './logs',
        config: './config',
        modules: './modules',
        public: './public',
        thermal: './data/thermal-memory',
        backups: './data/backups'
    },

    // 🚀 CONFIGURATION PERFORMANCE
    performance: {
        maxMemoryUsage: 2048, // MB
        gcInterval: 300000, // 5 minutes
        optimizeImages: true,
        cacheEnabled: true,
        compressionEnabled: true
    },

    // 🔄 CONFIGURATION MISE À JOUR
    updates: {
        autoCheck: true,
        checkInterval: 86400000, // 24 heures
        autoDownload: false,
        notifyUser: true
    },

    // 📱 CONFIGURATION FONCTIONNALITÉS
    features: {
        chat: true,
        voice: true,
        camera: true,
        fileScanning: true,
        codeExecution: true,
        mediaGeneration: true,
        brainVisualization: true,
        thermalDashboard: true,
        mcpMode: true,
        secureInternet: true
    },

    // 🌡️ CONFIGURATION SANTÉ SYSTÈME
    health: {
        monitoring: {
            enabled: true,
            interval: 10000, // 10 secondes
            alerts: true
        },
        thresholds: {
            cpu: 80, // %
            memory: 85, // %
            temperature: 41.0, // °C
            responseTime: 5000 // ms
        },
        recovery: {
            autoRestart: true,
            maxRestarts: 3,
            cooldownPeriod: 60000 // 1 minute
        }
    },

    // 📡 CONFIGURATION API
    api: {
        rateLimit: {
            windowMs: 60000, // 1 minute
            max: 100 // requêtes par minute
        },
        cors: {
            origin: ['http://localhost:52796', 'http://127.0.0.1:52796'],
            credentials: true
        },
        compression: true,
        helmet: true
    }
};
