{"timestamp": 1749529623646, "summary": {"averageCPU": 0.14193958333333337, "averageMemory": 97.6580301920573, "averageFPS": 55.604166666666664, "uptime": 5342}, "current": {"cpu": {"usage": 0.027400000000000004, "cores": 10, "model": "Apple M4", "speed": 24}, "memory": {"total": 17179869184, "free": 200261632, "used": 16979607552, "percentage": 98.83432388305664, "process": {"rss": 24576000, "heapTotal": 15630336, "heapUsed": 13591488, "external": 3912151, "arrayBuffers": 0}}, "disk": {"total": 1000000000000, "free": 500000000000, "used": 500000000000, "percentage": 50}, "network": {"bytesReceived": 995775, "bytesSent": 728369, "packetsReceived": 663, "packetsSent": 277}, "system": {"uptime": 5342, "loadAverage": [4.587890625, 7.91455078125, 7.24267578125], "platform": "darwin", "arch": "arm64", "nodeVersion": "v18.15.0", "electronVersion": "25.9.8"}, "performance": {"fps": 53, "renderTime": 2.1417422731451152, "scriptTime": 4.024314381329312, "gcTime": 0.04593775704321246}}, "history": [{"timestamp": 1749529525263, "cpu": 0.0355, "memory": 98.45495223999023, "disk": 50, "fps": 53}, {"timestamp": 1749529527268, "cpu": 0.0203, "memory": 97.7625846862793, "disk": 50, "fps": 51}, {"timestamp": 1749529530421, "cpu": 1.8800999999999999, "memory": 98.21414947509766, "disk": 50, "fps": 58}, {"timestamp": 1749529532427, "cpu": 0.08940000000000001, "memory": 98.98176193237305, "disk": 50, "fps": 60}, {"timestamp": 1749529534428, "cpu": 0.0524, "memory": 98.26879501342773, "disk": 50, "fps": 53}, {"timestamp": 1749529536431, "cpu": 0.062, "memory": 98.39258193969727, "disk": 50, "fps": 56}, {"timestamp": 1749529538435, "cpu": 0.0718, "memory": 98.05898666381836, "disk": 50, "fps": 54}, {"timestamp": 1749529540441, "cpu": 0.0494, "memory": 97.53551483154297, "disk": 50, "fps": 56}, {"timestamp": 1749529542446, "cpu": 0.0523, "memory": 96.64163589477539, "disk": 50, "fps": 60}, {"timestamp": 1749529544450, "cpu": 0.0771, "memory": 98.36101531982422, "disk": 50, "fps": 51}, {"timestamp": 1749529546456, "cpu": 0.13369999999999999, "memory": 97.01452255249023, "disk": 50, "fps": 51}, {"timestamp": 1749529548460, "cpu": 0.0415, "memory": 98.72150421142578, "disk": 50, "fps": 51}, {"timestamp": 1749529550465, "cpu": 0.0661, "memory": 98.40326309204102, "disk": 50, "fps": 60}, {"timestamp": 1749529552471, "cpu": 0.0874, "memory": 96.66023254394531, "disk": 50, "fps": 59}, {"timestamp": 1749529554474, "cpu": 0.045, "memory": 98.17380905151367, "disk": 50, "fps": 53}, {"timestamp": 1749529556479, "cpu": 0.0441, "memory": 98.67963790893555, "disk": 50, "fps": 51}, {"timestamp": 1749529558482, "cpu": 0.05859999999999999, "memory": 95.81995010375977, "disk": 50, "fps": 56}, {"timestamp": 1749529560484, "cpu": 0.0893, "memory": 98.48108291625977, "disk": 50, "fps": 60}, {"timestamp": 1749529562486, "cpu": 0.0571, "memory": 96.07791900634766, "disk": 50, "fps": 60}, {"timestamp": 1749529564487, "cpu": 0.0531, "memory": 98.34604263305664, "disk": 50, "fps": 53}, {"timestamp": 1749529566492, "cpu": 0.029799999999999997, "memory": 96.9198226928711, "disk": 50, "fps": 57}, {"timestamp": 1749529568497, "cpu": 0.063, "memory": 98.27051162719727, "disk": 50, "fps": 52}, {"timestamp": 1749529570503, "cpu": 0.0397, "memory": 97.93519973754883, "disk": 50, "fps": 54}, {"timestamp": 1749529572508, "cpu": 0.04970000000000001, "memory": 97.5886344909668, "disk": 50, "fps": 51}, {"timestamp": 1749529574513, "cpu": 0.053700000000000005, "memory": 99.17612075805664, "disk": 50, "fps": 52}, {"timestamp": 1749529576518, "cpu": 0.05759999999999999, "memory": 98.51398468017578, "disk": 50, "fps": 51}, {"timestamp": 1749529578523, "cpu": 0.3699, "memory": 95.05319595336914, "disk": 50, "fps": 56}, {"timestamp": 1749529580529, "cpu": 0.0396, "memory": 96.6670036315918, "disk": 50, "fps": 60}, {"timestamp": 1749529582534, "cpu": 0.0373, "memory": 98.58522415161133, "disk": 50, "fps": 59}, {"timestamp": 1749529584538, "cpu": 0.0777, "memory": 97.23663330078125, "disk": 50, "fps": 54}, {"timestamp": 1749529586544, "cpu": 0.1851, "memory": 98.77424240112305, "disk": 50, "fps": 58}, {"timestamp": 1749529588547, "cpu": 0.0381, "memory": 98.5921859741211, "disk": 50, "fps": 60}, {"timestamp": 1749529590553, "cpu": 0.0758, "memory": 97.26238250732422, "disk": 50, "fps": 57}, {"timestamp": 1749529593531, "cpu": 0.7727999999999999, "memory": 82.24868774414062, "disk": 50, "fps": 58}, {"timestamp": 1749529595531, "cpu": 0.1981, "memory": 98.32010269165039, "disk": 50, "fps": 58}, {"timestamp": 1749529597531, "cpu": 0.0431, "memory": 95.90520858764648, "disk": 50, "fps": 54}, {"timestamp": 1749529599532, "cpu": 0.066, "memory": 97.33161926269531, "disk": 50, "fps": 56}, {"timestamp": 1749529601535, "cpu": 0.0433, "memory": 99.16553497314453, "disk": 50, "fps": 56}, {"timestamp": 1749529603536, "cpu": 0.3029, "memory": 98.68125915527344, "disk": 50, "fps": 60}, {"timestamp": 1749529605538, "cpu": 0.048299999999999996, "memory": 98.90632629394531, "disk": 50, "fps": 52}, {"timestamp": 1749529607542, "cpu": 0.0193, "memory": 99.34883117675781, "disk": 50, "fps": 59}, {"timestamp": 1749529609547, "cpu": 0.0823, "memory": 99.1765022277832, "disk": 50, "fps": 56}, {"timestamp": 1749529611552, "cpu": 0.0711, "memory": 98.25773239135742, "disk": 50, "fps": 52}, {"timestamp": 1749529613557, "cpu": 0.8349000000000001, "memory": 99.05805587768555, "disk": 50, "fps": 59}, {"timestamp": 1749529615558, "cpu": 0.0333, "memory": 97.67026901245117, "disk": 50, "fps": 59}, {"timestamp": 1749529617564, "cpu": 0.059199999999999996, "memory": 98.84071350097656, "disk": 50, "fps": 55}, {"timestamp": 1749529619566, "cpu": 0.0279, "memory": 98.2151985168457, "disk": 50, "fps": 55}, {"timestamp": 1749529621572, "cpu": 0.027400000000000004, "memory": 98.83432388305664, "disk": 50, "fps": 53}], "thresholds": {"cpu": {"warning": 70, "critical": 85}, "memory": {"warning": 75, "critical": 90}, "temperature": {"warning": 40, "critical": 45}, "disk": {"warning": 80, "critical": 95}, "network": {"warning": 80, "critical": 95}}, "optimizations": {"memoryCleanup": true, "cpuThrottling": true, "diskCleanup": true, "networkOptimization": true}}