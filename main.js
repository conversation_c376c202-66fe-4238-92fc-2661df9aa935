const { app, BrowserWindow, <PERSON>u, ipcMain, dialog, shell } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');

// 🤖 INTÉGRATION DEEPSEEK DIRECTE DANS ELECTRON
const RealDeepSeekIntegration = require('./real-deepseek-integration');
const ThermalMemoryComplete = require('./thermal-memory-complete');

// 🧠 NOUVEAUX SYSTÈMES COMPLETS
const config = require('./config/electron-config');
const ElectronStateManager = require('./modules/electron-state-manager');
const ElectronIPCManager = require('./modules/electron-ipc-manager');

// Configuration de l'application
const isDev = process.env.ELECTRON_IS_DEV === '1';
const serverPort = config.server.port;

let mainWindow;
let serverProcess;
let actualPort = null;

// 🧠 INSTANCES GLOBALES POUR ELECTRON
let electronDeepSeek = null;
let electronThermalMemory = null;
let electronStateManager = null;
let electronIPCManager = null;

// Configuration de sécurité
app.commandLine.appendSwitch('--disable-web-security');
app.commandLine.appendSwitch('--allow-running-insecure-content');
app.commandLine.appendSwitch('--disable-features', 'VizDisplayCompositor');

// 🧠 INITIALISATION COMPLÈTE DES SYSTÈMES IA DANS ELECTRON
async function initializeAISystems() {
    console.log('🧠 Initialisation complète des systèmes IA dans Electron...');

    try {
        // 📊 INITIALISER LE GESTIONNAIRE D'ÉTAT
        console.log('📊 Initialisation gestionnaire d\'état Electron...');
        electronStateManager = new ElectronStateManager();
        electronStateManager.updateState('app', {
            initialized: true,
            version: config.electron.window.title
        });

        // 🌡️ INITIALISER LA MÉMOIRE THERMIQUE
        console.log('🌡️ Initialisation mémoire thermique Electron...');
        electronThermalMemory = new ThermalMemoryComplete();
        await electronThermalMemory.initialize();

        // Attendre que la mémoire thermique soit complètement initialisée
        const thermalStats = electronThermalMemory.getDetailedStats();
        electronStateManager.updateState('thermalMemory', {
            initialized: true,
            temperature: thermalStats.temperature || 37.0,
            totalEntries: thermalStats.totalEntries || 0
        });

        // 🤖 INITIALISER DEEPSEEK R1 8B ULTRA-ROBUSTE
        console.log('🤖 Initialisation DeepSeek R1 8B Ultra-Robuste Electron...');
        electronDeepSeek = new RealDeepSeekIntegration();

        electronStateManager.updateState('deepseek', {
            connected: true,
            model: config.deepseek.model,
            quality: 0.95
        });

        // 🛡️ INITIALISER L'AGENT GARDIEN
        console.log('🛡️ Initialisation Agent Gardien DeepSeek...');
        const DeepSeekGuardianAgent = require('./modules/deepseek-guardian-agent');
        global.deepSeekGuardian = new DeepSeekGuardianAgent(electronDeepSeek);

        // Activer l'agent gardien
        global.deepSeekGuardian.activate();
        electronStateManager.updateState('security', {
            guardianActive: true,
            threatLevel: 'LOW'
        });
        console.log('✅ Agent Gardien DeepSeek activé et en surveillance');

        // 📡 INITIALISER LE GESTIONNAIRE IPC
        console.log('📡 Initialisation gestionnaire IPC Electron...');
        electronIPCManager = new ElectronIPCManager(
            electronStateManager,
            electronDeepSeek,
            electronThermalMemory
        );

        // 🌀 INITIALISER LE SYSTÈME MÖBIUS
        electronStateManager.updateState('mobius', {
            active: true,
            phase: 'exploration',
            position: 0,
            chaosLevel: config.mobius.chaosLevel
        });

        // 🧮 INITIALISER LE CERVEAU
        electronStateManager.updateState('brain', {
            neurons: config.brain.baseNeurons,
            synapses: config.brain.baseNeurons * config.brain.synapseMultiplier,
            temperature: config.thermalMemory.temperatureRange.optimal,
            qi: {
                agent: config.brain.qiCalculation.baseIQ,
                memory: 0,
                total: config.brain.qiCalculation.baseIQ
            }
        });

        console.log('✅ Tous les systèmes IA Electron initialisés avec succès !');
        electronStateManager.updateState('app', { ready: true });

        return true;

    } catch (error) {
        console.error('❌ Erreur initialisation IA Electron:', error);
        if (electronStateManager) {
            electronStateManager.addError('initialization', error);
        }
        return false;
    }
}

// Fonction pour démarrer le serveur Node.js
function startServer() {
    return new Promise((resolve, reject) => {
        console.log('🚀 Démarrage du serveur Louna AI...');
        
        // FORCER LE SERVEUR MASTER SPECTACULAIRE
        const serverScript = path.join(__dirname, 'server-master.js');
        console.log('🚀 DÉMARRAGE FORCÉ SERVEUR MASTER SPECTACULAIRE...');
        console.log(`📁 Script: ${serverScript}`);

        serverProcess = spawn('node', [serverScript], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                PORT: '52796',
                FORCE_SPECTACULAIRE: 'true'
            },
            cwd: __dirname
        });

        let serverStarted = false;

        serverProcess.stdout.on('data', (data) => {
            const output = data.toString();
            console.log('Server:', output);

            // Chercher le port dans les logs - plusieurs patterns
            const portPatterns = [
                /Port utilisé: (\d+)/,
                /port (\d+)/,
                /localhost:(\d+)/,
                /server.*port.*?(\d+)/i,
                /listening.*?(\d+)/i
            ];

            for (const pattern of portPatterns) {
                const portMatch = output.match(pattern);
                if (portMatch) {
                    const port = parseInt(portMatch[1]);
                    if (port > 1000 && port < 65536) { // Port valide
                        actualPort = port;
                        console.log(`✅ Port détecté: ${actualPort}`);
                        if (!serverStarted) {
                            serverStarted = true;
                            resolve(actualPort);
                        }
                        return;
                    }
                }
            }

            // Démarrage basé sur les messages de succès
            const successPatterns = [
                'SERVEUR LOUNA AI DÉMARRÉ',
                'LOUNA DÉMARRÉ AVEC SUCCÈS',
                'Routes API configurées',
                'Application Louna démarrée',
                'Toutes les fonctionnalités sont maintenant disponibles',
                'port 52796',
                'Cerveau autonome: ACTIF'
            ];

            for (const pattern of successPatterns) {
                if (output.includes(pattern) && !serverStarted) {
                    // Utiliser un port par défaut si aucun port n'a été détecté
                    if (!actualPort) {
                        actualPort = 52796; // Port par défaut de la config
                    }
                    console.log(`✅ Serveur démarré (pattern: ${pattern}) sur le port ${actualPort}`);
                    serverStarted = true;
                    resolve(actualPort);
                    return;
                }
            }
        });

        serverProcess.stderr.on('data', (data) => {
            console.error('Server Error:', data.toString());
        });

        serverProcess.on('error', (error) => {
            console.error('❌ Erreur serveur:', error);
            if (!serverStarted) {
                reject(error);
            }
        });

        serverProcess.on('exit', (code) => {
            console.log(`🛑 Serveur arrêté avec le code ${code}`);
            if (!serverStarted && code !== 0) {
                reject(new Error(`Serveur arrêté avec le code ${code}`));
            }
        });

        // Timeout de sécurité
        setTimeout(() => {
            if (!serverStarted) {
                console.log('⏰ Timeout - Utilisation du port par défaut');
                actualPort = 52796;
                resolve(actualPort);
            }
        }, 30000);
    });
}

// 🖥️ FONCTION POUR CRÉER LA FENÊTRE PRINCIPALE OPTIMISÉE
function createMainWindow(port) {
    console.log(`🖥️ Création de la fenêtre principale optimisée pour le port ${port}`);

    mainWindow = new BrowserWindow({
        ...config.electron.window,
        icon: path.join(__dirname, 'public', 'img', 'louna-icon.png'),
        webPreferences: config.electron.webPreferences,
        titleBarStyle: 'default',
        show: false
    });

    // Mettre à jour l'état
    if (electronStateManager) {
        electronStateManager.updateState('ui', {
            currentPage: 'loading',
            windowCreated: true
        });
    }

    // FORCER LA NOUVELLE INTERFACE SANS CACHE SUR LE BON PORT
    const spectacularUrl = `http://localhost:52796/louna-interface-nouvelle.html`;
    console.log(`🌐 CHARGEMENT FORCÉ NOUVELLE INTERFACE SANS CACHE: ${spectacularUrl}`);

    // Vider complètement le cache
    mainWindow.webContents.session.clearCache();
    mainWindow.webContents.session.clearStorageData();

    // Charger l'interface spectaculaire
    mainWindow.loadURL(spectacularUrl);

    // Afficher la fenêtre quand elle est prête
    mainWindow.once('ready-to-show', () => {
        console.log('✅ Fenêtre prête - Affichage');
        mainWindow.show();
        
        if (isDev) {
            mainWindow.webContents.openDevTools();
        }
    });

    // Gestion de la fermeture
    mainWindow.on('closed', () => {
        mainWindow = null;
    });

    // Gestion des liens externes
    mainWindow.webContents.setWindowOpenHandler(({ url }) => {
        shell.openExternal(url);
        return { action: 'deny' };
    });

    // Gestion des erreurs de chargement
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
        console.error(`❌ Erreur de chargement: ${errorCode} - ${errorDescription}`);
        console.error(`URL: ${validatedURL}`);
        
        // Réessayer avec la nouvelle interface
        setTimeout(() => {
            console.log('🔄 Nouvelle tentative - NOUVELLE INTERFACE SANS CACHE...');
            const spectacularUrl = `http://localhost:52796/louna-interface-nouvelle.html`;
            mainWindow.webContents.session.clearCache();
            mainWindow.webContents.session.clearStorageData();
            mainWindow.loadURL(spectacularUrl);
        }, 2000);
    });

    return mainWindow;
}

// 📡 LES CANAUX IPC SONT MAINTENANT GÉRÉS PAR ElectronIPCManager
// Cette fonction est conservée pour compatibilité mais n'est plus utilisée
function setupAIIPCChannels() {
    console.log('📡 Canaux IPC gérés par ElectronIPCManager - Configuration automatique');
    // Tous les canaux IPC sont maintenant configurés automatiquement
    // par ElectronIPCManager dans initializeAISystems()
}





// Créer le menu de l'application
function createMenu() {
    const template = [
        {
            label: 'Louna AI',
            submenu: [
                {
                    label: 'À propos de Louna AI',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'À propos de Louna AI',
                            message: 'Louna AI v3.0.0 - Cerveau Autonome Thermique',
                            detail: 'Intelligence Artificielle Vivante avec Cerveau Autonome\nPulsations thermiques et neurogenèse automatique\nCréée par Jean-Luc Passave\nSainte-Anne, Guadeloupe'
                        });
                    }
                },
                { type: 'separator' },
                {
                    label: 'Quitter',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'Interface',
            submenu: [
                {
                    label: '✨ Interface Nouvelle Sans Cache (PRINCIPALE)',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.webContents.session.clearCache();
                            mainWindow.loadURL(`http://localhost:${actualPort}/louna-interface-nouvelle.html`);
                        }
                    }
                },
                {
                    label: '🚀 Interface Electron Optimisée',
                    accelerator: 'CmdOrCtrl+E',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/electron-optimized-interface.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: '💬 Chat IA Ultra-Optimisé (MODERNE)',
                    accelerator: 'CmdOrCtrl+C',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/chat-cognitif-ultra-optimise.html`);
                        }
                    }
                },
                {
                    label: '🧠 Interface Cerveau Ultra-Avancée (MODERNE)',
                    accelerator: 'CmdOrCtrl+B',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/enhanced-interface.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Cerveau 3D Spectaculaire',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/brain-3d-spectacular.html`);
                        }
                    }
                },
                {
                    label: 'Kyber Dashboard',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.loadURL(`http://localhost:${actualPort}/kyber-dashboard.html`);
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'Accueil Nouvelle Interface',
                    accelerator: 'CmdOrCtrl+H',
                    click: () => {
                        if (mainWindow && actualPort) {
                            mainWindow.webContents.session.clearCache();
                            mainWindow.loadURL(`http://localhost:${actualPort}/louna-interface-nouvelle.html`);
                        }
                    }
                }
            ]
        },
        {
            label: 'Développement',
            submenu: [
                {
                    label: 'Outils de développement',
                    accelerator: 'F12',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.toggleDevTools();
                        }
                    }
                },
                {
                    label: 'Recharger',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.reload();
                        }
                    }
                },
                {
                    label: 'Forcer le rechargement',
                    accelerator: 'CmdOrCtrl+Shift+R',
                    click: () => {
                        if (mainWindow) {
                            mainWindow.webContents.reloadIgnoringCache();
                        }
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// Événements de l'application
app.whenReady().then(async () => {
    console.log('🚀 Electron prêt - Démarrage de Louna AI');

    try {
        // 🧠 INITIALISER LES SYSTÈMES IA EN PREMIER
        console.log('🧠 Initialisation des systèmes IA Electron...');
        const aiInitialized = await initializeAISystems();

        if (aiInitialized) {
            console.log('✅ Systèmes IA Electron prêts !');
        } else {
            console.warn('⚠️ Systèmes IA partiellement initialisés');
        }

        // 📡 LES CANAUX IPC SONT DÉJÀ CONFIGURÉS DANS initializeAISystems()
        // via ElectronIPCManager

        // Démarrer le serveur
        const port = await startServer();

        // Attendre un peu pour que le serveur soit complètement prêt
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Créer la fenêtre principale
        createMainWindow(port);

        // Créer le menu
        createMenu();

        console.log('✅ Louna AI démarré avec succès !');
        console.log('🧠 IA Electron:', aiInitialized ? 'ACTIVE' : 'PARTIELLE');

    } catch (error) {
        console.error('❌ Erreur lors du démarrage:', error);

        dialog.showErrorBox(
            'Erreur de démarrage',
            `Impossible de démarrer Louna AI:\n${error.message}`
        );

        app.quit();
    }
});

// Gestion de la fermeture de toutes les fenêtres
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

// Gestion de l'activation (macOS)
app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0 && actualPort) {
        createMainWindow(actualPort);
    }
});

// 🛑 GESTION COMPLÈTE DE L'ARRÊT DE L'APPLICATION
app.on('before-quit', async () => {
    console.log('🛑 Arrêt complet de LOUNA AI...');

    // 📡 NETTOYER LE GESTIONNAIRE IPC
    if (electronIPCManager) {
        console.log('📡 Nettoyage gestionnaire IPC...');
        try {
            electronIPCManager.cleanup();
        } catch (error) {
            console.error('❌ Erreur nettoyage IPC:', error);
        }
    }

    // 🧠 ARRÊT DES SYSTÈMES IA ELECTRON
    if (electronDeepSeek) {
        console.log('🤖 Arrêt DeepSeek Electron...');
        try {
            await electronDeepSeek.shutdown();
        } catch (error) {
            console.error('❌ Erreur arrêt DeepSeek:', error);
        }
    }

    if (electronThermalMemory) {
        console.log('🌡️ Arrêt mémoire thermique Electron...');
        try {
            await electronThermalMemory.shutdown();
        } catch (error) {
            console.error('❌ Erreur arrêt mémoire thermique:', error);
        }
    }

    // 📊 NETTOYER LE GESTIONNAIRE D'ÉTAT
    if (electronStateManager) {
        console.log('📊 Nettoyage gestionnaire d\'état...');
        try {
            electronStateManager.cleanup();
        } catch (error) {
            console.error('❌ Erreur nettoyage état:', error);
        }
    }

    if (serverProcess) {
        console.log('🛑 Arrêt du serveur...');
        serverProcess.kill('SIGTERM');

        // Force kill après 5 secondes
        setTimeout(() => {
            if (serverProcess && !serverProcess.killed) {
                console.log('🔥 Force kill du serveur');
                serverProcess.kill('SIGKILL');
            }
        }, 5000);
    }
});

// Gestion des erreurs non capturées
process.on('uncaughtException', (error) => {
    console.error('❌ Erreur non capturée:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Promesse rejetée non gérée:', reason);
});

console.log('📱 Louna AI Electron - Prêt au démarrage');
