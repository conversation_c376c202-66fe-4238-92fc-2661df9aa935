/**
 * 🛡️ AGENT GARDIEN POUR DEEPSEEK R1 8B
 * Surveillance et contrôle sécurisé de la connexion DeepSeek
 */

class DeepSeekGuardianAgent {
    constructor(deepSeekInstance) {
        this.deepSeek = deepSeekInstance;
        this.isActive = false;
        this.monitoringInterval = null;
        
        this.guardianConfig = {
            maxResponseTime: 60000, // 1 minute max
            maxConsecutiveErrors: 5,
            suspiciousPatterns: [
                /hack|exploit|bypass|override/i,
                /delete|destroy|remove|kill/i,
                /password|secret|token|key/i
            ],
            monitoringFrequency: 10000, // 10 secondes
            autoDisconnectEnabled: true,
            alertThresholds: {
                responseTime: 30000,
                errorRate: 20, // 20%
                memoryUsage: 80 // 80%
            }
        };
        
        this.securityMetrics = {
            totalRequests: 0,
            blockedRequests: 0,
            suspiciousActivity: 0,
            emergencyStops: 0,
            lastAlert: null,
            startTime: Date.now()
        };
        
        this.alerts = [];
        
        console.log('🛡️ Agent <PERSON><PERSON>ien <PERSON>Seek initialisé');
    }

    /**
     * Activer la surveillance
     */
    activate() {
        if (this.isActive) {
            console.log('🛡️ Agent <PERSON>ien déjà actif');
            return;
        }
        
        this.isActive = true;
        console.log('🛡️ ACTIVATION AGENT GARDIEN DEEPSEEK');
        
        // Enregistrer les callbacks de surveillance
        this.deepSeek.onDisconnection((type, reason) => {
            this.logSecurityEvent('disconnection', { type, reason });
        });
        
        // Démarrer la surveillance continue
        this.startContinuousMonitoring();
        
        console.log('✅ Agent Gardien actif et en surveillance');
    }

    /**
     * Désactiver la surveillance
     */
    deactivate() {
        if (!this.isActive) return;
        
        this.isActive = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        console.log('🛡️ Agent Gardien désactivé');
    }

    /**
     * Surveillance continue
     */
    startContinuousMonitoring() {
        this.monitoringInterval = setInterval(() => {
            this.performSecurityCheck();
        }, this.guardianConfig.monitoringFrequency);
    }

    /**
     * Vérification de sécurité
     */
    async performSecurityCheck() {
        if (!this.isActive) return;
        
        try {
            const securityStatus = this.deepSeek.getSecurityStatus();
            const performanceMetrics = this.deepSeek.getPerformanceMetrics();
            
            // Vérifier les métriques de performance
            this.checkPerformanceMetrics(performanceMetrics);
            
            // Vérifier la santé de la connexion
            this.checkConnectionHealth(securityStatus.connectionHealth);
            
            // Vérifier les patterns suspects
            this.checkSuspiciousActivity();
            
        } catch (error) {
            console.error('❌ Erreur vérification sécurité:', error);
            this.logSecurityEvent('monitoring_error', { error: error.message });
        }
    }

    /**
     * Vérifier les métriques de performance
     */
    checkPerformanceMetrics(metrics) {
        // Temps de réponse excessif
        if (metrics.averageResponseTime > this.guardianConfig.alertThresholds.responseTime) {
            this.triggerAlert('performance', 'Temps de réponse élevé', {
                averageResponseTime: metrics.averageResponseTime,
                threshold: this.guardianConfig.alertThresholds.responseTime
            });
        }
        
        // Taux d'erreur élevé
        const errorRate = (metrics.errors / metrics.totalQueries) * 100;
        if (errorRate > this.guardianConfig.alertThresholds.errorRate) {
            this.triggerAlert('reliability', 'Taux d\'erreur élevé', {
                errorRate: errorRate,
                threshold: this.guardianConfig.alertThresholds.errorRate
            });
            
            if (this.guardianConfig.autoDisconnectEnabled) {
                this.emergencyDisconnect('Taux d\'erreur critique détecté');
            }
        }
    }

    /**
     * Vérifier la santé de la connexion
     */
    checkConnectionHealth(health) {
        if (health.consecutiveFailures >= this.guardianConfig.maxConsecutiveErrors) {
            this.emergencyDisconnect(`Trop d'échecs consécutifs: ${health.consecutiveFailures}`);
        }
    }

    /**
     * Vérifier l'activité suspecte
     */
    checkSuspiciousActivity() {
        // Cette méthode peut être étendue pour analyser les logs de requêtes
        // et détecter des patterns suspects
    }

    /**
     * Analyser une requête avant traitement
     */
    analyzeRequest(prompt, options = {}) {
        this.securityMetrics.totalRequests++;
        
        // Vérifier les patterns suspects
        for (const pattern of this.guardianConfig.suspiciousPatterns) {
            if (pattern.test(prompt)) {
                this.securityMetrics.blockedRequests++;
                this.securityMetrics.suspiciousActivity++;
                
                this.triggerAlert('security', 'Pattern suspect détecté', {
                    prompt: prompt.substring(0, 100),
                    pattern: pattern.toString()
                });
                
                if (this.guardianConfig.autoDisconnectEnabled) {
                    this.emergencyDisconnect('Activité suspecte détectée dans la requête');
                }
                
                return false; // Bloquer la requête
            }
        }
        
        return true; // Autoriser la requête
    }

    /**
     * Déconnexion d'urgence
     */
    emergencyDisconnect(reason) {
        console.log('🚨 AGENT GARDIEN - DÉCONNEXION D\'URGENCE:', reason);
        
        this.securityMetrics.emergencyStops++;
        this.logSecurityEvent('emergency_disconnect', { reason });
        
        // Utiliser la méthode de déconnexion gardien
        this.deepSeek.guardianDisconnect(reason);
        
        this.triggerAlert('emergency', 'Déconnexion d\'urgence', { reason });
    }

    /**
     * Déconnexion manuelle par l'agent gardien
     */
    manualDisconnect(reason = 'Déconnexion manuelle par agent gardien') {
        console.log('🛡️ AGENT GARDIEN - DÉCONNEXION MANUELLE:', reason);
        
        this.logSecurityEvent('manual_disconnect', { reason });
        return this.deepSeek.guardianDisconnect(reason);
    }

    /**
     * Déclencher une alerte
     */
    triggerAlert(type, message, data = {}) {
        const alert = {
            timestamp: Date.now(),
            type: type,
            message: message,
            data: data,
            severity: this.getAlertSeverity(type)
        };
        
        this.alerts.push(alert);
        this.securityMetrics.lastAlert = alert.timestamp;
        
        // Garder seulement les 100 dernières alertes
        if (this.alerts.length > 100) {
            this.alerts = this.alerts.slice(-100);
        }
        
        console.log(`🚨 ALERTE GARDIEN [${type.toUpperCase()}]: ${message}`, data);
    }

    /**
     * Obtenir la sévérité d'une alerte
     */
    getAlertSeverity(type) {
        const severityMap = {
            'emergency': 'CRITICAL',
            'security': 'HIGH',
            'reliability': 'MEDIUM',
            'performance': 'LOW'
        };
        
        return severityMap[type] || 'LOW';
    }

    /**
     * Enregistrer un événement de sécurité
     */
    logSecurityEvent(event, data = {}) {
        const logEntry = {
            timestamp: Date.now(),
            event: event,
            data: data
        };
        
        console.log('📝 ÉVÉNEMENT SÉCURITÉ:', logEntry);
        
        // Ici on pourrait sauvegarder dans un fichier de log sécurisé
    }

    /**
     * Obtenir le rapport de sécurité
     */
    getSecurityReport() {
        const uptime = Date.now() - this.securityMetrics.startTime;
        const recentAlerts = this.alerts.slice(-10);
        
        return {
            guardianStatus: {
                active: this.isActive,
                uptime: uptime,
                config: this.guardianConfig
            },
            metrics: this.securityMetrics,
            recentAlerts: recentAlerts,
            connectionStatus: this.deepSeek.getSecurityStatus(),
            recommendations: this.generateRecommendations()
        };
    }

    /**
     * Générer des recommandations de sécurité
     */
    generateRecommendations() {
        const recommendations = [];
        
        if (this.securityMetrics.suspiciousActivity > 0) {
            recommendations.push('Surveiller de près l\'activité suspecte détectée');
        }
        
        if (this.securityMetrics.emergencyStops > 3) {
            recommendations.push('Investiguer les causes des arrêts d\'urgence fréquents');
        }
        
        const errorRate = (this.securityMetrics.blockedRequests / this.securityMetrics.totalRequests) * 100;
        if (errorRate > 10) {
            recommendations.push('Taux de blocage élevé - Vérifier la configuration des patterns');
        }
        
        return recommendations;
    }

    /**
     * Configurer l'agent gardien
     */
    configure(newConfig) {
        this.guardianConfig = { ...this.guardianConfig, ...newConfig };
        console.log('🔧 Configuration Agent Gardien mise à jour');
    }
}

module.exports = DeepSeekGuardianAgent;
