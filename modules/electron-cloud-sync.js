/**
 * ☁️ LOUNA AI ELECTRON - SYSTÈME DE SYNCHRONISATION CLOUD
 * Sauvegarde et synchronisation intelligente des données LOUNA AI
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const https = require('https');
const { app } = require('electron');
const logger = require('./electron-logger');

class ElectronCloudSync {
    constructor(stateManager, notificationSystem) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.isEnabled = false;
        this.syncInterval = null;
        this.lastSync = null;
        this.syncInProgress = false;
        
        // Configuration cloud
        this.cloudConfig = {
            provider: 'louna-cloud', // louna-cloud, aws, google, azure
            endpoint: 'https://cloud.louna-ai.com/api/v1',
            apiKey: null,
            userId: null,
            deviceId: this.generateDeviceId(),
            encryption: true,
            compression: true,
            autoSync: true,
            syncInterval: 300000, // 5 minutes
            maxFileSize: 100 * 1024 * 1024, // 100MB
            retryAttempts: 3,
            timeout: 30000
        };
        
        // Données à synchroniser
        this.syncTargets = {
            neuralMemory: {
                path: 'data/neurons_continuous.json',
                priority: 'high',
                encrypted: true,
                lastSync: null,
                size: 0
            },
            thermalMemory: {
                path: 'data/thermal-memory-backup.json',
                priority: 'high',
                encrypted: true,
                lastSync: null,
                size: 0
            },
            userSettings: {
                path: 'config/user-settings.json',
                priority: 'medium',
                encrypted: false,
                lastSync: null,
                size: 0
            },
            customThemes: {
                path: 'data/custom-themes/',
                priority: 'low',
                encrypted: false,
                lastSync: null,
                size: 0
            },
            userPlugins: {
                path: 'data/user-plugins/',
                priority: 'medium',
                encrypted: false,
                lastSync: null,
                size: 0
            },
            logs: {
                path: 'logs/',
                priority: 'low',
                encrypted: false,
                lastSync: null,
                size: 0,
                exclude: ['*.tmp', '*.lock']
            }
        };
        
        // État de synchronisation
        this.syncState = {
            status: 'idle', // idle, syncing, error, success
            progress: 0,
            currentFile: null,
            totalFiles: 0,
            uploadedFiles: 0,
            downloadedFiles: 0,
            errors: [],
            lastError: null,
            bytesUploaded: 0,
            bytesDownloaded: 0,
            startTime: null,
            endTime: null
        };
        
        // Répertoires
        this.syncDir = path.join(__dirname, '../data/cloud-sync');
        this.cacheDir = path.join(this.syncDir, 'cache');
        this.tempDir = path.join(this.syncDir, 'temp');
        
        logger.system('Système de synchronisation cloud initialisé');
        this.initializeCloudSync();
    }

    // 🔧 INITIALISER LA SYNCHRONISATION CLOUD
    async initializeCloudSync() {
        try {
            // Créer les répertoires
            this.ensureDirectories();
            
            // Charger la configuration
            await this.loadConfiguration();
            
            // Vérifier la connectivité
            await this.checkConnectivity();
            
            // Démarrer la synchronisation automatique si activée
            if (this.cloudConfig.autoSync && this.isEnabled) {
                this.startAutoSync();
            }
            
            logger.success('CLOUD_SYNC', 'Système de synchronisation cloud initialisé');
        } catch (error) {
            logger.error('CLOUD_SYNC', 'Erreur initialisation cloud sync', { error: error.message });
        }
    }

    // 📁 CRÉER LES RÉPERTOIRES
    ensureDirectories() {
        [this.syncDir, this.cacheDir, this.tempDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug('CLOUD_SYNC', `Répertoire créé: ${dir}`);
            }
        });
    }

    // ⚙️ CHARGER LA CONFIGURATION
    async loadConfiguration() {
        const configPath = path.join(__dirname, '../config/cloud-sync-config.json');
        
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                this.cloudConfig = { ...this.cloudConfig, ...config };
                this.isEnabled = config.enabled || false;
                
                logger.debug('CLOUD_SYNC', 'Configuration cloud chargée', { enabled: this.isEnabled });
            } catch (error) {
                logger.warn('CLOUD_SYNC', 'Erreur chargement configuration', { error: error.message });
            }
        }
    }

    // 🌐 VÉRIFIER LA CONNECTIVITÉ
    async checkConnectivity() {
        if (!this.isEnabled) {
            return false;
        }

        try {
            const response = await this.makeRequest('GET', '/health');
            if (response.status === 'ok') {
                logger.success('CLOUD_SYNC', 'Connectivité cloud vérifiée');
                return true;
            }
        } catch (error) {
            logger.warn('CLOUD_SYNC', 'Connectivité cloud indisponible', { error: error.message });
        }
        
        return false;
    }

    // 🔄 DÉMARRER LA SYNCHRONISATION AUTOMATIQUE
    startAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }

        this.syncInterval = setInterval(() => {
            this.performSync();
        }, this.cloudConfig.syncInterval);

        logger.info('CLOUD_SYNC', `Synchronisation automatique démarrée (${this.cloudConfig.syncInterval / 1000}s)`);
    }

    // ⏹️ ARRÊTER LA SYNCHRONISATION AUTOMATIQUE
    stopAutoSync() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        logger.info('CLOUD_SYNC', 'Synchronisation automatique arrêtée');
    }

    // 🔄 EFFECTUER LA SYNCHRONISATION
    async performSync(manual = false) {
        if (this.syncInProgress || !this.isEnabled) {
            return;
        }

        this.syncInProgress = true;
        this.syncState.status = 'syncing';
        this.syncState.startTime = Date.now();
        this.syncState.progress = 0;
        this.syncState.errors = [];

        try {
            logger.info('CLOUD_SYNC', 'Début de la synchronisation');

            // Notification de début
            if (this.notificationSystem && manual) {
                this.notificationSystem.createNotification(
                    'info',
                    '☁️ Synchronisation Cloud',
                    'Synchronisation en cours...',
                    { duration: 3000 }
                );
            }

            // Analyser les fichiers à synchroniser
            await this.analyzeSyncTargets();

            // Synchroniser chaque cible
            for (const [name, target] of Object.entries(this.syncTargets)) {
                if (target.needsSync) {
                    await this.syncTarget(name, target);
                }
            }

            this.syncState.status = 'success';
            this.lastSync = Date.now();
            
            logger.success('CLOUD_SYNC', 'Synchronisation terminée avec succès');

            // Notification de succès
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'success',
                    '✅ Synchronisation Réussie',
                    `${this.syncState.uploadedFiles} fichiers synchronisés`,
                    { duration: 3000 }
                );
            }

        } catch (error) {
            this.syncState.status = 'error';
            this.syncState.lastError = error.message;
            this.syncState.errors.push(error.message);
            
            logger.error('CLOUD_SYNC', 'Erreur synchronisation', { error: error.message });

            // Notification d'erreur
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'error',
                    '❌ Erreur Synchronisation',
                    error.message,
                    { duration: 5000 }
                );
            }
        } finally {
            this.syncInProgress = false;
            this.syncState.endTime = Date.now();
            
            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('cloudSync', {
                    lastSync: this.lastSync,
                    status: this.syncState.status,
                    progress: 100
                });
            }
        }
    }

    // 📊 ANALYSER LES CIBLES DE SYNCHRONISATION
    async analyzeSyncTargets() {
        this.syncState.totalFiles = 0;
        
        for (const [name, target] of Object.entries(this.syncTargets)) {
            const fullPath = path.join(__dirname, '..', target.path);
            
            if (fs.existsSync(fullPath)) {
                const stats = fs.statSync(fullPath);
                target.size = stats.size;
                target.lastModified = stats.mtime.getTime();
                
                // Vérifier si la synchronisation est nécessaire
                target.needsSync = !target.lastSync || target.lastModified > target.lastSync;
                
                if (target.needsSync) {
                    this.syncState.totalFiles++;
                }
            }
        }
        
        logger.debug('CLOUD_SYNC', `${this.syncState.totalFiles} fichiers à synchroniser`);
    }

    // 🎯 SYNCHRONISER UNE CIBLE
    async syncTarget(name, target) {
        try {
            this.syncState.currentFile = name;
            
            const fullPath = path.join(__dirname, '..', target.path);
            
            if (fs.existsSync(fullPath)) {
                // Lire le fichier
                let data = fs.readFileSync(fullPath);
                
                // Chiffrer si nécessaire
                if (target.encrypted) {
                    data = this.encryptData(data);
                }
                
                // Compresser si activé
                if (this.cloudConfig.compression) {
                    data = this.compressData(data);
                }
                
                // Uploader vers le cloud
                await this.uploadFile(name, data, target);
                
                target.lastSync = Date.now();
                this.syncState.uploadedFiles++;
                this.syncState.bytesUploaded += data.length;
                
                logger.debug('CLOUD_SYNC', `Fichier synchronisé: ${name}`);
            }
            
            // Mettre à jour le progrès
            this.syncState.progress = (this.syncState.uploadedFiles / this.syncState.totalFiles) * 100;
            
        } catch (error) {
            logger.error('CLOUD_SYNC', `Erreur synchronisation ${name}`, { error: error.message });
            this.syncState.errors.push(`${name}: ${error.message}`);
        }
    }

    // 📤 UPLOADER UN FICHIER
    async uploadFile(name, data, target) {
        const metadata = {
            name,
            size: data.length,
            priority: target.priority,
            encrypted: target.encrypted,
            compressed: this.cloudConfig.compression,
            timestamp: Date.now(),
            deviceId: this.cloudConfig.deviceId,
            checksum: this.calculateChecksum(data)
        };

        const response = await this.makeRequest('POST', `/sync/upload/${name}`, {
            metadata,
            data: data.toString('base64')
        });

        if (!response.success) {
            throw new Error(response.error || 'Erreur upload');
        }

        return response;
    }

    // 📥 TÉLÉCHARGER UN FICHIER
    async downloadFile(name) {
        try {
            const response = await this.makeRequest('GET', `/sync/download/${name}`);
            
            if (!response.success) {
                throw new Error(response.error || 'Erreur download');
            }

            let data = Buffer.from(response.data, 'base64');
            
            // Décompresser si nécessaire
            if (response.metadata.compressed) {
                data = this.decompressData(data);
            }
            
            // Déchiffrer si nécessaire
            if (response.metadata.encrypted) {
                data = this.decryptData(data);
            }
            
            // Vérifier l'intégrité
            const checksum = this.calculateChecksum(data);
            if (checksum !== response.metadata.checksum) {
                throw new Error('Erreur intégrité fichier');
            }
            
            this.syncState.downloadedFiles++;
            this.syncState.bytesDownloaded += data.length;
            
            return { data, metadata: response.metadata };
            
        } catch (error) {
            logger.error('CLOUD_SYNC', `Erreur téléchargement ${name}`, { error: error.message });
            throw error;
        }
    }

    // 🔐 CHIFFRER LES DONNÉES
    encryptData(data) {
        const algorithm = 'aes-256-gcm';
        const key = this.getEncryptionKey();
        const iv = crypto.randomBytes(16);
        
        const cipher = crypto.createCipher(algorithm, key);
        cipher.setAAD(Buffer.from('louna-ai-data'));
        
        let encrypted = cipher.update(data);
        encrypted = Buffer.concat([encrypted, cipher.final()]);
        
        const authTag = cipher.getAuthTag();
        
        return Buffer.concat([iv, authTag, encrypted]);
    }

    // 🔓 DÉCHIFFRER LES DONNÉES
    decryptData(encryptedData) {
        const algorithm = 'aes-256-gcm';
        const key = this.getEncryptionKey();
        
        const iv = encryptedData.slice(0, 16);
        const authTag = encryptedData.slice(16, 32);
        const encrypted = encryptedData.slice(32);
        
        const decipher = crypto.createDecipher(algorithm, key);
        decipher.setAAD(Buffer.from('louna-ai-data'));
        decipher.setAuthTag(authTag);
        
        let decrypted = decipher.update(encrypted);
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        
        return decrypted;
    }

    // 🗜️ COMPRESSER LES DONNÉES
    compressData(data) {
        const zlib = require('zlib');
        return zlib.gzipSync(data);
    }

    // 📦 DÉCOMPRESSER LES DONNÉES
    decompressData(compressedData) {
        const zlib = require('zlib');
        return zlib.gunzipSync(compressedData);
    }

    // 🔑 OBTENIR LA CLÉ DE CHIFFREMENT
    getEncryptionKey() {
        // Générer une clé basée sur l'ID de l'appareil et un secret
        const secret = 'louna-ai-encryption-secret-2024';
        return crypto.pbkdf2Sync(this.cloudConfig.deviceId, secret, 10000, 32, 'sha256');
    }

    // 🔢 CALCULER LA SOMME DE CONTRÔLE
    calculateChecksum(data) {
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    // 🆔 GÉNÉRER L'ID DE L'APPAREIL
    generateDeviceId() {
        const os = require('os');
        const hostname = os.hostname();
        const platform = os.platform();
        const arch = os.arch();
        const userInfo = os.userInfo();
        
        const deviceString = `${hostname}-${platform}-${arch}-${userInfo.username}`;
        return crypto.createHash('md5').update(deviceString).digest('hex');
    }

    // 🌐 FAIRE UNE REQUÊTE HTTP
    async makeRequest(method, endpoint, data = null) {
        return new Promise((resolve, reject) => {
            const url = new URL(endpoint, this.cloudConfig.endpoint);
            
            const options = {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.cloudConfig.apiKey}`,
                    'User-Agent': `LOUNA-AI/${app.getVersion()}`,
                    'X-Device-ID': this.cloudConfig.deviceId
                },
                timeout: this.cloudConfig.timeout
            };

            const req = https.request(url, options, (res) => {
                let responseData = '';
                
                res.on('data', (chunk) => {
                    responseData += chunk;
                });
                
                res.on('end', () => {
                    try {
                        const response = JSON.parse(responseData);
                        resolve(response);
                    } catch (error) {
                        reject(new Error('Réponse invalide du serveur'));
                    }
                });
            });

            req.on('error', (error) => {
                reject(error);
            });

            req.on('timeout', () => {
                req.destroy();
                reject(new Error('Timeout de la requête'));
            });

            if (data) {
                req.write(JSON.stringify(data));
            }

            req.end();
        });
    }

    // ⚙️ CONFIGURER LE CLOUD
    configure(config) {
        this.cloudConfig = { ...this.cloudConfig, ...config };
        this.isEnabled = config.enabled !== undefined ? config.enabled : this.isEnabled;
        
        // Sauvegarder la configuration
        this.saveConfiguration();
        
        // Redémarrer la synchronisation automatique si nécessaire
        if (this.isEnabled && this.cloudConfig.autoSync) {
            this.startAutoSync();
        } else {
            this.stopAutoSync();
        }
        
        logger.info('CLOUD_SYNC', 'Configuration cloud mise à jour', config);
    }

    // 💾 SAUVEGARDER LA CONFIGURATION
    saveConfiguration() {
        const config = {
            ...this.cloudConfig,
            enabled: this.isEnabled
        };
        
        const configPath = path.join(__dirname, '../config/cloud-sync-config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }

    // 📊 OBTENIR L'ÉTAT
    getState() {
        return {
            enabled: this.isEnabled,
            status: this.syncState.status,
            lastSync: this.lastSync,
            progress: this.syncState.progress,
            totalFiles: this.syncState.totalFiles,
            uploadedFiles: this.syncState.uploadedFiles,
            downloadedFiles: this.syncState.downloadedFiles,
            bytesUploaded: this.syncState.bytesUploaded,
            bytesDownloaded: this.syncState.bytesDownloaded,
            errors: this.syncState.errors,
            config: this.cloudConfig
        };
    }

    // 📋 OBTENIR L'HISTORIQUE
    getSyncHistory() {
        const historyPath = path.join(this.syncDir, 'sync-history.json');
        
        if (fs.existsSync(historyPath)) {
            try {
                return JSON.parse(fs.readFileSync(historyPath, 'utf8'));
            } catch (error) {
                logger.warn('CLOUD_SYNC', 'Erreur lecture historique', { error: error.message });
            }
        }
        
        return [];
    }

    // 📝 AJOUTER À L'HISTORIQUE
    addToHistory(syncResult) {
        const history = this.getSyncHistory();
        history.unshift({
            ...syncResult,
            timestamp: Date.now()
        });
        
        // Garder seulement les 50 dernières synchronisations
        const limitedHistory = history.slice(0, 50);
        
        const historyPath = path.join(this.syncDir, 'sync-history.json');
        fs.writeFileSync(historyPath, JSON.stringify(limitedHistory, null, 2));
    }

    // 🧹 NETTOYAGE
    cleanup() {
        this.stopAutoSync();
        
        // Sauvegarder l'historique final
        if (this.lastSync) {
            this.addToHistory({
                status: this.syncState.status,
                files: this.syncState.uploadedFiles,
                bytes: this.syncState.bytesUploaded,
                duration: this.syncState.endTime - this.syncState.startTime
            });
        }
        
        logger.info('CLOUD_SYNC', 'Système de synchronisation cloud nettoyé');
    }
}

module.exports = ElectronCloudSync;
