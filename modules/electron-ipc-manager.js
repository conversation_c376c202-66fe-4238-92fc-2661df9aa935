/**
 * 📡 LOUNA AI ELECTRON - GESTIONNAIRE IPC AVANCÉ
 * Communication Inter-Processus optimisée pour l'application Electron
 */

const { ipcMain } = require('electron');
const config = require('../config/electron-config');

class ElectronIPCManager {
    constructor(stateManager, deepSeekIntegration, thermalMemory) {
        this.stateManager = stateManager;
        this.deepSeek = deepSeekIntegration;
        this.thermalMemory = thermalMemory;
        this.channels = new Map();
        this.middleware = [];
        
        console.log('📡 Gestionnaire IPC Electron initialisé');
        this.setupChannels();
    }

    // 🔧 CONFIGURER TOUS LES CANAUX IPC
    setupChannels() {
        // 🤖 CANAUX DEEPSEEK
        this.registerChannel('deepseek-chat', this.handleDeepSeekChat.bind(this));
        this.registerChannel('deepseek-status', this.handleDeepSeekStatus.bind(this));
        this.registerChannel('deepseek-disconnect', this.handleDeepSeekDisconnect.bind(this));
        this.registerChannel('deepseek-reconnect', this.handleDeepSeekReconnect.bind(this));

        // 🧠 CANAUX MÉMOIRE THERMIQUE
        this.registerChannel('thermal-memory-add', this.handleThermalMemoryAdd.bind(this));
        this.registerChannel('thermal-memory-get', this.handleThermalMemoryGet.bind(this));
        this.registerChannel('thermal-memory-stats', this.handleThermalMemoryStats.bind(this));
        this.registerChannel('thermal-memory-backup', this.handleThermalMemoryBackup.bind(this));

        // 📊 CANAUX MÉTRIQUES ET ÉTAT
        this.registerChannel('get-state', this.handleGetState.bind(this));
        this.registerChannel('get-metrics', this.handleGetMetrics.bind(this));
        this.registerChannel('get-statistics', this.handleGetStatistics.bind(this));
        this.registerChannel('update-state', this.handleUpdateState.bind(this));

        // 🌀 CANAUX SYSTÈME MÖBIUS
        this.registerChannel('mobius-status', this.handleMobiusStatus.bind(this));
        this.registerChannel('mobius-control', this.handleMobiusControl.bind(this));
        this.registerChannel('get-thoughts', this.handleGetThoughts.bind(this));

        // 🛡️ CANAUX SÉCURITÉ
        this.registerChannel('security-status', this.handleSecurityStatus.bind(this));
        this.registerChannel('security-scan', this.handleSecurityScan.bind(this));
        this.registerChannel('guardian-control', this.handleGuardianControl.bind(this));

        // 🌐 CANAUX MCP
        this.registerChannel('mcp-start-dialogue', this.handleMCPStartDialogue.bind(this));
        this.registerChannel('mcp-status', this.handleMCPStatus.bind(this));
        this.registerChannel('mcp-submit-response', this.handleMCPSubmitResponse.bind(this));

        // 🎨 CANAUX INTERFACE
        this.registerChannel('ui-notification', this.handleUINotification.bind(this));
        this.registerChannel('ui-theme', this.handleUITheme.bind(this));
        this.registerChannel('voice-control', this.handleVoiceControl.bind(this));

        // 🔧 CANAUX SYSTÈME
        this.registerChannel('system-health', this.handleSystemHealth.bind(this));
        this.registerChannel('system-restart', this.handleSystemRestart.bind(this));
        this.registerChannel('system-shutdown', this.handleSystemShutdown.bind(this));
        this.registerChannel('reactivate-all-systems', this.handleReactivateAllSystems.bind(this));
        this.registerChannel('emergency-shutdown-all', this.handleEmergencyShutdownAll.bind(this));
        this.registerChannel('optimize-memory', this.handleOptimizeMemory.bind(this));
        this.registerChannel('memory-stats', this.handleMemoryStats.bind(this));
        this.registerChannel('save-qi-results', this.handleSaveQIResults.bind(this));

        console.log(`✅ ${this.channels.size} canaux IPC configurés`);
    }

    // 📝 ENREGISTRER UN CANAL IPC
    registerChannel(channel, handler) {
        if (this.channels.has(channel)) {
            console.warn(`⚠️ Canal IPC déjà enregistré: ${channel}`);
            return;
        }

        const wrappedHandler = async (event, ...args) => {
            const startTime = Date.now();
            
            try {
                // Appliquer les middlewares
                for (const middleware of this.middleware) {
                    await middleware(channel, event, args);
                }

                // Exécuter le handler
                const result = await handler(event, ...args);
                
                // Mesurer les performances
                const responseTime = Date.now() - startTime;
                this.updateChannelStats(channel, true, responseTime);
                
                return result;

            } catch (error) {
                const responseTime = Date.now() - startTime;
                this.updateChannelStats(channel, false, responseTime);
                
                console.error(`❌ Erreur canal IPC [${channel}]:`, error);
                
                return {
                    success: false,
                    error: error.message,
                    timestamp: Date.now()
                };
            }
        };

        ipcMain.handle(channel, wrappedHandler);
        this.channels.set(channel, {
            handler: wrappedHandler,
            calls: 0,
            errors: 0,
            totalResponseTime: 0,
            lastCall: null
        });

        console.log(`📡 Canal IPC enregistré: ${channel}`);
    }

    // 📊 METTRE À JOUR LES STATISTIQUES DU CANAL
    updateChannelStats(channel, success, responseTime) {
        const stats = this.channels.get(channel);
        if (!stats) return;

        stats.calls++;
        stats.totalResponseTime += responseTime;
        stats.lastCall = Date.now();
        
        if (!success) {
            stats.errors++;
        }

        // Mettre à jour les métriques globales
        if (this.stateManager) {
            this.stateManager.updateState('metrics', {
                averageResponseTime: stats.totalResponseTime / stats.calls,
                totalInteractions: this.stateManager.getState('metrics').totalInteractions + 1
            });
        }
    }

    // 🤖 HANDLER CHAT DEEPSEEK
    async handleDeepSeekChat(event, message) {
        if (!this.deepSeek) {
            throw new Error('DeepSeek non initialisé');
        }

        const thermalContext = this.thermalMemory ? {
            temperature: this.thermalMemory.getCurrentTemperature(),
            recentMemories: this.thermalMemory.getRecentEntries(5),
            stats: this.thermalMemory.getStats()
        } : {};

        const result = await this.deepSeek.generateContextualResponse(message, thermalContext);

        // Stocker dans la mémoire thermique
        if (this.thermalMemory) {
            this.thermalMemory.add('electron_chat', {
                question: message,
                response: result.response.substring(0, 500),
                quality: result.quality,
                timestamp: Date.now()
            }, 0.9, 'electron_interactions');
        }

        return {
            success: true,
            response: result.response,
            quality: result.quality,
            context: thermalContext,
            timestamp: Date.now()
        };
    }

    // 📊 HANDLER STATUT DEEPSEEK
    async handleDeepSeekStatus(event) {
        if (!this.deepSeek) {
            return { success: false, error: 'DeepSeek non initialisé' };
        }

        const stats = this.deepSeek.getAdvancedStats();
        const state = this.stateManager?.getState('deepseek') || {};

        return {
            success: true,
            status: {
                connected: state.connected || false,
                model: state.model || 'deepseek-r1:8b',
                responseTime: state.responseTime || 0,
                totalRequests: state.totalRequests || 0,
                errors: state.errors || 0,
                quality: state.quality || 0.95,
                ...stats
            },
            timestamp: Date.now()
        };
    }

    // 🧠 HANDLER AJOUT MÉMOIRE THERMIQUE
    async handleThermalMemoryAdd(event, data) {
        if (!this.thermalMemory) {
            throw new Error('Mémoire thermique non initialisée');
        }

        const entryId = this.thermalMemory.add(
            data.type || 'electron_input',
            data.content,
            data.importance || 0.7,
            data.category || 'user_interaction'
        );

        return {
            success: true,
            entryId,
            timestamp: Date.now()
        };
    }

    // 📊 HANDLER STATISTIQUES MÉMOIRE THERMIQUE
    async handleThermalMemoryStats(event) {
        if (!this.thermalMemory) {
            return { success: false, error: 'Mémoire thermique non initialisée' };
        }

        const stats = this.thermalMemory.getDetailedStats();
        
        return {
            success: true,
            stats,
            timestamp: Date.now()
        };
    }

    // 📊 HANDLER OBTENIR ÉTAT
    async handleGetState(event, category) {
        if (!this.stateManager) {
            return { success: false, error: 'Gestionnaire d\'état non initialisé' };
        }

        const state = this.stateManager.getState(category);
        
        return {
            success: true,
            state,
            timestamp: Date.now()
        };
    }

    // 📊 HANDLER OBTENIR MÉTRIQUES
    async handleGetMetrics(event) {
        if (!this.stateManager) {
            return { success: false, error: 'Gestionnaire d\'état non initialisé' };
        }

        const metrics = this.stateManager.getMetrics();
        
        return {
            success: true,
            metrics,
            timestamp: Date.now()
        };
    }

    // 🌀 HANDLER STATUT MÖBIUS
    async handleMobiusStatus(event) {
        try {
            // Récupérer les pensées du serveur
            const fetch = require('node-fetch');
            const response = await fetch(`http://localhost:${config.server.port}/api/thoughts/continuous`);
            
            if (response.ok) {
                const data = await response.json();
                return {
                    success: true,
                    mobius: data.mobiusState || {},
                    thoughts: data.thoughts || [],
                    timestamp: Date.now()
                };
            } else {
                throw new Error('Impossible de récupérer le statut Möbius');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🛡️ HANDLER STATUT SÉCURITÉ
    async handleSecurityStatus(event) {
        const securityState = this.stateManager?.getState('security') || {};
        
        return {
            success: true,
            security: {
                guardianActive: securityState.guardianActive || false,
                threatLevel: securityState.threatLevel || 'LOW',
                lastScan: securityState.lastScan,
                blockedRequests: securityState.blockedRequests || 0,
                quarantinedItems: securityState.quarantinedItems || 0
            },
            timestamp: Date.now()
        };
    }

    // 🌐 HANDLER DÉMARRER DIALOGUE MCP
    async handleMCPStartDialogue(event) {
        try {
            const fetch = require('node-fetch');
            const response = await fetch(`http://localhost:${config.server.port}/api/start-chatgpt-dialogue`, {
                method: 'POST'
            });
            
            if (response.ok) {
                const data = await response.json();
                return {
                    success: true,
                    dialogue: data,
                    timestamp: Date.now()
                };
            } else {
                throw new Error('Impossible de démarrer le dialogue MCP');
            }
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🎨 HANDLER NOTIFICATION UI
    async handleUINotification(event, type, message, data) {
        if (this.stateManager) {
            this.stateManager.addNotification(type, message, data);
        }
        
        return {
            success: true,
            timestamp: Date.now()
        };
    }

    // 🔧 HANDLER SANTÉ SYSTÈME
    async handleSystemHealth(event) {
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        return {
            success: true,
            health: {
                memory: {
                    used: memoryUsage.heapUsed,
                    total: memoryUsage.heapTotal,
                    external: memoryUsage.external,
                    percentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
                },
                uptime: {
                    seconds: uptime,
                    formatted: this.formatUptime(uptime * 1000)
                },
                channels: {
                    total: this.channels.size,
                    stats: this.getChannelStats()
                }
            },
            timestamp: Date.now()
        };
    }

    // 📊 OBTENIR LES STATISTIQUES DES CANAUX
    getChannelStats() {
        const stats = {};
        
        for (const [channel, data] of this.channels) {
            stats[channel] = {
                calls: data.calls,
                errors: data.errors,
                averageResponseTime: data.calls > 0 ? data.totalResponseTime / data.calls : 0,
                lastCall: data.lastCall,
                successRate: data.calls > 0 ? ((data.calls - data.errors) / data.calls) * 100 : 100
            };
        }
        
        return stats;
    }

    // ⏱️ FORMATER LE TEMPS DE FONCTIONNEMENT
    formatUptime(uptime) {
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        
        return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    }

    // 🔌 HANDLERS MANQUANTS (STUBS POUR ÉVITER LES ERREURS)

    async handleDeepSeekDisconnect(event, reason) {
        try {
            if (global.deepSeekGuardian) {
                const result = global.deepSeekGuardian.manualDisconnect(reason || 'Déconnexion demandée via interface');
                return { success: true, disconnected: result };
            }
            return { success: false, error: 'Agent gardien non disponible' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async handleDeepSeekReconnect(event) {
        try {
            if (this.deepSeek) {
                const reactivated = await this.deepSeek.reactivateConnection();
                return { success: true, reactivated };
            }
            return { success: false, error: 'DeepSeek non disponible' };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    async handleThermalMemoryGet(event, query) {
        return { success: true, data: 'Méthode non implémentée', timestamp: Date.now() };
    }

    async handleThermalMemoryBackup(event) {
        return { success: true, backup: 'Sauvegarde simulée', timestamp: Date.now() };
    }

    async handleGetStatistics(event) {
        return { success: true, statistics: {}, timestamp: Date.now() };
    }

    async handleUpdateState(event, category, updates) {
        if (this.stateManager) {
            this.stateManager.updateState(category, updates);
            return { success: true, timestamp: Date.now() };
        }
        return { success: false, error: 'Gestionnaire d\'état non disponible' };
    }

    async handleMobiusControl(event, action) {
        return { success: true, action: action, timestamp: Date.now() };
    }

    async handleGetThoughts(event) {
        return { success: true, thoughts: [], timestamp: Date.now() };
    }

    async handleSecurityScan(event) {
        return { success: true, scan: 'Scan simulé - Aucune menace', timestamp: Date.now() };
    }

    async handleGuardianControl(event, action) {
        return { success: true, action: action, timestamp: Date.now() };
    }

    async handleMCPStatus(event) {
        return { success: true, status: 'MCP inactif', timestamp: Date.now() };
    }

    async handleMCPSubmitResponse(event, response) {
        return { success: true, submitted: true, timestamp: Date.now() };
    }

    async handleUITheme(event, theme) {
        return { success: true, theme: theme, timestamp: Date.now() };
    }

    async handleVoiceControl(event, action) {
        return { success: true, voice: action, timestamp: Date.now() };
    }

    async handleSystemRestart(event) {
        return { success: true, restart: 'Redémarrage simulé', timestamp: Date.now() };
    }

    async handleSystemShutdown(event) {
        return { success: true, shutdown: 'Arrêt simulé', timestamp: Date.now() };
    }

    // 🔄 HANDLER RÉACTIVER TOUS LES SYSTÈMES
    async handleReactivateAllSystems(event) {
        try {
            console.log('🔄 Réactivation de tous les systèmes...');

            const results = {
                deepseek: false,
                memory: false,
                voice: false,
                plugins: false,
                optimization: false
            };

            // Réactiver DeepSeek
            try {
                if (this.deepSeek) {
                    results.deepseek = await this.deepSeek.reactivateConnection();
                }
            } catch (error) {
                console.warn('⚠️ Erreur réactivation DeepSeek:', error.message);
            }

            // Optimiser la mémoire
            try {
                if (global.electronMemoryOptimizer) {
                    await global.electronMemoryOptimizer.performOptimization('normal');
                    results.optimization = true;
                }
            } catch (error) {
                console.warn('⚠️ Erreur optimisation mémoire:', error.message);
            }

            // Réactiver la mémoire thermique
            try {
                if (this.thermalMemory) {
                    this.thermalMemory.reactivate();
                    results.memory = true;
                }
            } catch (error) {
                console.warn('⚠️ Erreur réactivation mémoire:', error.message);
            }

            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('app', {
                    lastReactivation: Date.now(),
                    reactivationResults: results
                });
            }

            console.log('✅ Réactivation terminée:', results);

            return {
                success: true,
                results,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ Erreur réactivation globale:', error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🚨 HANDLER ARRÊT D'URGENCE
    async handleEmergencyShutdownAll(event) {
        try {
            console.log('🚨 ARRÊT D\'URGENCE ACTIVÉ');

            const shutdownResults = {
                deepseek: false,
                memory: false,
                voice: false,
                plugins: false
            };

            // Arrêter DeepSeek
            try {
                if (global.deepSeekGuardian) {
                    shutdownResults.deepseek = global.deepSeekGuardian.emergencyDisconnect();
                }
            } catch (error) {
                console.warn('⚠️ Erreur arrêt DeepSeek:', error.message);
            }

            // Arrêter les systèmes vocaux
            try {
                if (global.electronVoiceSystem) {
                    global.electronVoiceSystem.emergencyStop();
                    shutdownResults.voice = true;
                }
            } catch (error) {
                console.warn('⚠️ Erreur arrêt vocal:', error.message);
            }

            // Optimisation mémoire d'urgence
            try {
                if (global.electronMemoryOptimizer) {
                    await global.electronMemoryOptimizer.performOptimization('critical');
                    shutdownResults.memory = true;
                }
            } catch (error) {
                console.warn('⚠️ Erreur optimisation urgence:', error.message);
            }

            console.log('🚨 Arrêt d\'urgence terminé:', shutdownResults);

            return {
                success: true,
                shutdownResults,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ Erreur arrêt d\'urgence:', error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🧹 HANDLER OPTIMISER MÉMOIRE
    async handleOptimizeMemory(event, level = 'normal') {
        try {
            if (global.electronMemoryOptimizer) {
                await global.electronMemoryOptimizer.performOptimization(level);

                return {
                    success: true,
                    level,
                    stats: global.electronMemoryOptimizer.getStats(),
                    timestamp: Date.now()
                };
            } else {
                return {
                    success: false,
                    error: 'Optimiseur mémoire non disponible',
                    timestamp: Date.now()
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 📊 HANDLER STATISTIQUES MÉMOIRE
    async handleMemoryStats(event) {
        try {
            const memoryUsage = process.memoryUsage();
            const stats = {
                process: memoryUsage,
                optimizer: global.electronMemoryOptimizer ?
                    global.electronMemoryOptimizer.getStats() : null
            };

            return {
                success: true,
                stats,
                timestamp: Date.now()
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🧠 HANDLER SAUVEGARDER RÉSULTATS QI
    async handleSaveQIResults(event, results) {
        try {
            const fs = require('fs');
            const path = require('path');

            // Créer le répertoire de sauvegarde s'il n'existe pas
            const saveDir = path.join(__dirname, '../data');
            if (!fs.existsSync(saveDir)) {
                fs.mkdirSync(saveDir, { recursive: true });
            }

            // Préparer les données à sauvegarder
            const qiData = {
                ...results,
                agentInfo: {
                    name: 'LOUNA AI Ultra-Autonome',
                    model: 'DeepSeek R1 8B',
                    version: '3.0.0',
                    neuronCount: 1064000
                },
                testInfo: {
                    type: 'QI Standardisé',
                    questions: 10,
                    sections: 3,
                    maxScore: 200
                }
            };

            // Nom du fichier avec timestamp
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `qi-test-results-${timestamp}.json`;
            const filepath = path.join(saveDir, filename);

            // Sauvegarder le fichier
            fs.writeFileSync(filepath, JSON.stringify(qiData, null, 2));

            // Mettre à jour l'état si disponible
            if (this.stateManager) {
                this.stateManager.updateState('brain', {
                    lastQITest: Date.now(),
                    qiScore: results.qi,
                    qiInterpretation: results.interpretation
                });
            }

            console.log('💾 Résultats QI sauvegardés:', filename);

            return {
                success: true,
                filename,
                filepath,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ Erreur sauvegarde QI:', error);
            return {
                success: false,
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    // 🧹 NETTOYER LES CANAUX
    cleanup() {
        for (const channel of this.channels.keys()) {
            ipcMain.removeHandler(channel);
        }

        this.channels.clear();
        console.log('🧹 Canaux IPC nettoyés');
    }
}

module.exports = ElectronIPCManager;
