/**
 * 🧹 OPTIMISEUR MÉMOIRE ELECTRON - LOUNA AI
 * Optimisation automatique de l'utilisation mémoire
 */

const logger = require('./electron-logger');

class ElectronMemoryOptimizer {
    constructor(stateManager, notificationSystem) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.isOptimizing = false;
        this.optimizationInterval = null;
        this.lastOptimization = null;
        
        // Configuration OPTIMISÉE POUR TESTS COMPLEXES
        this.config = {
            enabled: true,
            autoOptimize: true,
            optimizationInterval: 30000, // 30 secondes (plus fréquent)
            criticalThreshold: 85, // % (plus agressif)
            warningThreshold: 70, // % (plus agressif)
            forceGCThreshold: 75, // % (plus agressif)
            maxHistoryEntries: 500, // Réduit pour économiser mémoire
            maxLogEntries: 200 // Réduit pour économiser mémoire
        };
        
        // Statistiques
        this.stats = {
            totalOptimizations: 0,
            memoryFreed: 0,
            lastMemoryUsage: 0,
            averageOptimizationTime: 0,
            errors: 0
        };
        
        logger.system('Optimiseur mémoire Electron initialisé');
        this.startAutoOptimization();
    }

    // 🚀 DÉMARRER L'OPTIMISATION AUTOMATIQUE
    startAutoOptimization() {
        if (!this.config.autoOptimize) return;

        if (this.optimizationInterval) {
            clearInterval(this.optimizationInterval);
        }

        this.optimizationInterval = setInterval(() => {
            this.checkAndOptimize();
        }, this.config.optimizationInterval);

        logger.info('MEMORY_OPT', 'Optimisation automatique démarrée');
    }

    // 🔍 VÉRIFIER ET OPTIMISER
    async checkAndOptimize() {
        if (this.isOptimizing) return;

        try {
            const memoryUsage = this.getMemoryUsage();
            const memoryPercentage = this.calculateMemoryPercentage(memoryUsage);

            // Mettre à jour les statistiques
            this.stats.lastMemoryUsage = memoryPercentage;

            // Optimiser si nécessaire
            if (memoryPercentage >= this.config.criticalThreshold) {
                logger.warn('MEMORY_OPT', `Utilisation mémoire critique: ${memoryPercentage.toFixed(1)}%`);
                await this.performOptimization('critical');
            } else if (memoryPercentage >= this.config.warningThreshold) {
                logger.info('MEMORY_OPT', `Utilisation mémoire élevée: ${memoryPercentage.toFixed(1)}%`);
                await this.performOptimization('warning');
            }

            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('performance', {
                    memory: memoryPercentage,
                    lastOptimization: this.lastOptimization
                });
            }

        } catch (error) {
            logger.error('MEMORY_OPT', 'Erreur vérification mémoire', { error: error.message });
            this.stats.errors++;
        }
    }

    // ⚡ EFFECTUER L'OPTIMISATION
    async performOptimization(level = 'normal') {
        if (this.isOptimizing) return;

        this.isOptimizing = true;
        const startTime = Date.now();
        const initialMemory = this.getMemoryUsage();

        try {
            logger.info('MEMORY_OPT', `Optimisation mémoire niveau ${level}...`);

            // Notification
            if (this.notificationSystem && level === 'critical') {
                this.notificationSystem.createNotification(
                    'warning',
                    '🧹 Optimisation Mémoire',
                    'Optimisation automatique en cours...',
                    { duration: 3000 }
                );
            }

            // Étapes d'optimisation selon le niveau
            switch (level) {
                case 'critical':
                    await this.criticalOptimization();
                    break;
                case 'warning':
                    await this.warningOptimization();
                    break;
                default:
                    await this.normalOptimization();
            }

            // Calculer les résultats
            const finalMemory = this.getMemoryUsage();
            const memoryFreed = initialMemory.heapUsed - finalMemory.heapUsed;
            const optimizationTime = Date.now() - startTime;

            // Mettre à jour les statistiques
            this.stats.totalOptimizations++;
            this.stats.memoryFreed += Math.max(0, memoryFreed);
            this.stats.averageOptimizationTime = 
                (this.stats.averageOptimizationTime * (this.stats.totalOptimizations - 1) + optimizationTime) / 
                this.stats.totalOptimizations;
            this.lastOptimization = Date.now();

            logger.success('MEMORY_OPT', 
                `Optimisation terminée en ${optimizationTime}ms - Mémoire libérée: ${this.formatBytes(memoryFreed)}`);

            // Notification de succès pour optimisations critiques
            if (this.notificationSystem && level === 'critical') {
                this.notificationSystem.createNotification(
                    'success',
                    '✅ Optimisation Terminée',
                    `${this.formatBytes(memoryFreed)} libérés`,
                    { duration: 3000 }
                );
            }

        } catch (error) {
            logger.error('MEMORY_OPT', 'Erreur optimisation', { error: error.message });
            this.stats.errors++;
        } finally {
            this.isOptimizing = false;
        }
    }

    // 🚨 OPTIMISATION CRITIQUE
    async criticalOptimization() {
        // Forcer le garbage collection
        await this.forceGarbageCollection();
        
        // Nettoyer les caches
        await this.clearCaches();
        
        // Nettoyer l'historique des états
        await this.cleanStateHistory();
        
        // Nettoyer les logs
        await this.cleanLogs();
        
        // Optimiser les structures de données
        await this.optimizeDataStructures();
    }

    // ⚠️ OPTIMISATION WARNING
    async warningOptimization() {
        // Forcer le garbage collection
        await this.forceGarbageCollection();
        
        // Nettoyer les caches légers
        await this.clearLightCaches();
        
        // Nettoyer partiellement l'historique
        await this.cleanStateHistoryPartial();
    }

    // 🔄 OPTIMISATION NORMALE
    async normalOptimization() {
        // Garbage collection standard
        await this.forceGarbageCollection();
        
        // Nettoyage léger
        await this.clearLightCaches();
    }

    // 🗑️ FORCER LE GARBAGE COLLECTION
    async forceGarbageCollection() {
        try {
            if (global.gc) {
                global.gc();
                logger.debug('MEMORY_OPT', 'Garbage collection forcé');
            } else {
                logger.debug('MEMORY_OPT', 'Garbage collection non disponible');
            }
            
            // Petit délai pour laisser le GC s'exécuter
            await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur garbage collection', { error: error.message });
        }
    }

    // 🧹 NETTOYER LES CACHES
    async clearCaches() {
        try {
            // Nettoyer le cache du gestionnaire d'état
            if (this.stateManager && typeof this.stateManager.clearCache === 'function') {
                this.stateManager.clearCache();
            }

            // Nettoyer les caches Node.js
            if (require.cache) {
                const cacheKeys = Object.keys(require.cache);
                const toDelete = cacheKeys.filter(key => 
                    key.includes('temp') || key.includes('cache')
                );
                
                toDelete.forEach(key => {
                    delete require.cache[key];
                });
                
                logger.debug('MEMORY_OPT', `${toDelete.length} entrées de cache supprimées`);
            }

        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur nettoyage caches', { error: error.message });
        }
    }

    // 🧹 NETTOYER LES CACHES LÉGERS
    async clearLightCaches() {
        try {
            // Nettoyage léger - seulement les caches temporaires
            if (require.cache) {
                const cacheKeys = Object.keys(require.cache);
                const toDelete = cacheKeys.filter(key => 
                    key.includes('temp') && !key.includes('critical')
                );
                
                toDelete.forEach(key => {
                    delete require.cache[key];
                });
                
                logger.debug('MEMORY_OPT', `${toDelete.length} caches légers supprimés`);
            }
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur nettoyage caches légers', { error: error.message });
        }
    }

    // 📜 NETTOYER L'HISTORIQUE DES ÉTATS
    async cleanStateHistory() {
        try {
            if (this.stateManager && this.stateManager.history) {
                const originalLength = this.stateManager.history.length;
                
                // Garder seulement les dernières entrées
                this.stateManager.history = this.stateManager.history.slice(-this.config.maxHistoryEntries);
                
                const cleaned = originalLength - this.stateManager.history.length;
                logger.debug('MEMORY_OPT', `${cleaned} entrées d'historique supprimées`);
            }
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur nettoyage historique', { error: error.message });
        }
    }

    // 📜 NETTOYER PARTIELLEMENT L'HISTORIQUE
    async cleanStateHistoryPartial() {
        try {
            if (this.stateManager && this.stateManager.history) {
                const originalLength = this.stateManager.history.length;
                const keepEntries = Math.floor(this.config.maxHistoryEntries * 0.7);
                
                this.stateManager.history = this.stateManager.history.slice(-keepEntries);
                
                const cleaned = originalLength - this.stateManager.history.length;
                logger.debug('MEMORY_OPT', `${cleaned} entrées d'historique partiellement supprimées`);
            }
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur nettoyage partiel historique', { error: error.message });
        }
    }

    // 📝 NETTOYER LES LOGS
    async cleanLogs() {
        try {
            // Nettoyer les logs en mémoire si disponibles
            if (logger.logs && Array.isArray(logger.logs)) {
                const originalLength = logger.logs.length;
                logger.logs = logger.logs.slice(-this.config.maxLogEntries);
                
                const cleaned = originalLength - logger.logs.length;
                logger.debug('MEMORY_OPT', `${cleaned} entrées de logs supprimées`);
            }
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur nettoyage logs', { error: error.message });
        }
    }

    // 🔧 OPTIMISER LES STRUCTURES DE DONNÉES
    async optimizeDataStructures() {
        try {
            // Optimiser les structures de données si disponibles
            // Ici on pourrait optimiser les neurones, connexions, etc.
            
            logger.debug('MEMORY_OPT', 'Structures de données optimisées');
        } catch (error) {
            logger.warn('MEMORY_OPT', 'Erreur optimisation structures', { error: error.message });
        }
    }

    // 📊 OBTENIR L'UTILISATION MÉMOIRE
    getMemoryUsage() {
        return process.memoryUsage();
    }

    // 📊 CALCULER LE POURCENTAGE MÉMOIRE
    calculateMemoryPercentage(memoryUsage) {
        const totalMemory = require('os').totalmem();
        return (memoryUsage.heapUsed / totalMemory) * 100;
    }

    // 📏 FORMATER LES BYTES
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 📊 OBTENIR LES STATISTIQUES
    getStats() {
        const currentMemory = this.getMemoryUsage();
        const memoryPercentage = this.calculateMemoryPercentage(currentMemory);
        
        return {
            ...this.stats,
            currentMemory: {
                ...currentMemory,
                percentage: memoryPercentage,
                formatted: {
                    heapUsed: this.formatBytes(currentMemory.heapUsed),
                    heapTotal: this.formatBytes(currentMemory.heapTotal),
                    external: this.formatBytes(currentMemory.external),
                    rss: this.formatBytes(currentMemory.rss)
                }
            },
            config: this.config,
            isOptimizing: this.isOptimizing,
            lastOptimization: this.lastOptimization
        };
    }

    // ⚙️ CONFIGURER L'OPTIMISEUR
    configure(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        if (this.config.autoOptimize) {
            this.startAutoOptimization();
        } else {
            this.stopAutoOptimization();
        }
        
        logger.info('MEMORY_OPT', 'Configuration mise à jour', newConfig);
    }

    // ⏹️ ARRÊTER L'OPTIMISATION AUTOMATIQUE
    stopAutoOptimization() {
        if (this.optimizationInterval) {
            clearInterval(this.optimizationInterval);
            this.optimizationInterval = null;
        }
        
        logger.info('MEMORY_OPT', 'Optimisation automatique arrêtée');
    }

    // 🧹 NETTOYAGE
    cleanup() {
        this.stopAutoOptimization();
        logger.info('MEMORY_OPT', 'Optimiseur mémoire nettoyé');
    }
}

module.exports = ElectronMemoryOptimizer;
