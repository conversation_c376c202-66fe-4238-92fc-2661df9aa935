/**
 * 🔔 LOUNA AI ELECTRON - SYSTÈME DE NOTIFICATIONS AVANCÉ
 * Gestionnaire de notifications intelligent pour l'application Electron
 */

const { Notification, nativeImage } = require('electron');
const path = require('path');
const logger = require('./electron-logger');

class ElectronNotificationSystem {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.notifications = new Map();
        this.notificationQueue = [];
        this.isProcessingQueue = false;
        this.settings = {
            enabled: true,
            sound: true,
            priority: true,
            maxVisible: 3,
            defaultDuration: 5000,
            queueDelay: 1000
        };
        
        this.notificationTypes = {
            info: { icon: 'info', color: '#00d4aa', sound: 'default' },
            success: { icon: 'success', color: '#00ff00', sound: 'success' },
            warning: { icon: 'warning', color: '#f39c12', sound: 'warning' },
            error: { icon: 'error', color: '#e74c3c', sound: 'error' },
            deepseek: { icon: 'brain', color: '#ff6b9d', sound: 'ai' },
            security: { icon: 'shield', color: '#e67e22', sound: 'alert' },
            system: { icon: 'gear', color: '#3498db', sound: 'system' }
        };
        
        logger.system('Système de notifications Electron initialisé');
        this.setupNotificationHandlers();
    }

    // 🔧 CONFIGURER LES GESTIONNAIRES
    setupNotificationHandlers() {
        // Vérifier si les notifications sont supportées
        if (!Notification.isSupported()) {
            logger.warn('NOTIFICATIONS', 'Notifications système non supportées');
            return;
        }

        // Écouter les changements d'état pour notifications automatiques
        if (this.stateManager) {
            this.stateManager.on('stateChanged', this.handleStateChange.bind(this));
            this.stateManager.on('error', this.handleError.bind(this));
        }

        logger.success('NOTIFICATIONS', 'Gestionnaires de notifications configurés');
    }

    // 📢 CRÉER UNE NOTIFICATION
    async createNotification(type, title, message, options = {}) {
        if (!this.settings.enabled) {
            return null;
        }

        const notificationConfig = this.notificationTypes[type] || this.notificationTypes.info;
        const notificationId = Date.now().toString();

        const notification = {
            id: notificationId,
            type,
            title,
            message,
            timestamp: Date.now(),
            config: notificationConfig,
            options: {
                duration: options.duration || this.settings.defaultDuration,
                priority: options.priority || 'normal',
                actions: options.actions || [],
                data: options.data || {},
                persistent: options.persistent || false,
                ...options
            }
        };

        // Ajouter à la queue ou afficher immédiatement
        if (this.getVisibleNotificationsCount() >= this.settings.maxVisible) {
            this.notificationQueue.push(notification);
            logger.debug('NOTIFICATIONS', `Notification ajoutée à la queue: ${title}`);
        } else {
            await this.displayNotification(notification);
        }

        return notificationId;
    }

    // 🖥️ AFFICHER UNE NOTIFICATION
    async displayNotification(notification) {
        try {
            const { title, message, config, options } = notification;

            // Créer l'icône
            const iconPath = this.getIconPath(config.icon);
            const icon = iconPath ? nativeImage.createFromPath(iconPath) : null;

            // Créer la notification native
            const nativeNotification = new Notification({
                title,
                body: message,
                icon,
                silent: !this.settings.sound,
                urgency: this.getPriorityLevel(options.priority),
                actions: options.actions,
                closeButtonText: 'Fermer'
            });

            // Gestionnaires d'événements
            nativeNotification.on('show', () => {
                logger.debug('NOTIFICATIONS', `Notification affichée: ${title}`);
                this.notifications.set(notification.id, {
                    ...notification,
                    nativeNotification,
                    visible: true
                });
            });

            nativeNotification.on('click', () => {
                logger.debug('NOTIFICATIONS', `Notification cliquée: ${title}`);
                this.handleNotificationClick(notification);
            });

            nativeNotification.on('close', () => {
                logger.debug('NOTIFICATIONS', `Notification fermée: ${title}`);
                this.handleNotificationClose(notification);
            });

            nativeNotification.on('action', (event, index) => {
                logger.debug('NOTIFICATIONS', `Action notification: ${title}, index: ${index}`);
                this.handleNotificationAction(notification, index);
            });

            // Afficher la notification
            nativeNotification.show();

            // Programmer la fermeture automatique si non persistante
            if (!options.persistent && options.duration > 0) {
                setTimeout(() => {
                    if (this.notifications.has(notification.id)) {
                        nativeNotification.close();
                    }
                }, options.duration);
            }

            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.addNotification(notification.type, title, {
                    message,
                    id: notification.id,
                    timestamp: notification.timestamp
                });
            }

        } catch (error) {
            logger.error('NOTIFICATIONS', 'Erreur affichage notification', { error: error.message });
        }
    }

    // 🎯 GESTIONNAIRES D'ÉVÉNEMENTS
    handleNotificationClick(notification) {
        // Logique personnalisée selon le type
        switch (notification.type) {
            case 'deepseek':
                this.openDeepSeekInterface();
                break;
            case 'security':
                this.openSecurityPanel();
                break;
            case 'error':
                this.openErrorLog();
                break;
            default:
                this.openMainInterface();
        }

        // Callback personnalisé
        if (notification.options.onClick) {
            notification.options.onClick(notification);
        }
    }

    handleNotificationClose(notification) {
        this.notifications.delete(notification.id);
        this.processNotificationQueue();

        // Callback personnalisé
        if (notification.options.onClose) {
            notification.options.onClose(notification);
        }
    }

    handleNotificationAction(notification, actionIndex) {
        const action = notification.options.actions[actionIndex];
        if (action && action.callback) {
            action.callback(notification, actionIndex);
        }
    }

    // 📊 GESTIONNAIRE CHANGEMENTS D'ÉTAT
    handleStateChange(event) {
        const { category, newState, oldState } = event;

        // Notifications automatiques selon les changements d'état
        switch (category) {
            case 'deepseek':
                if (newState.connected !== oldState.connected) {
                    this.createNotification(
                        newState.connected ? 'success' : 'warning',
                        'DeepSeek R1 8B',
                        newState.connected ? 'Connexion établie' : 'Connexion perdue',
                        { priority: 'high' }
                    );
                }
                break;

            case 'security':
                if (newState.threatLevel !== oldState.threatLevel) {
                    const isHighThreat = newState.threatLevel === 'HIGH' || newState.threatLevel === 'CRITICAL';
                    this.createNotification(
                        isHighThreat ? 'error' : 'security',
                        'Alerte Sécurité',
                        `Niveau de menace: ${newState.threatLevel}`,
                        { priority: isHighThreat ? 'critical' : 'normal', persistent: isHighThreat }
                    );
                }
                break;

            case 'brain':
                if (newState.health?.needsRest && !oldState.health?.needsRest) {
                    this.createNotification(
                        'warning',
                        'Santé du Cerveau',
                        'LOUNA AI a besoin de repos',
                        { priority: 'high' }
                    );
                }
                break;
        }
    }

    // ❌ GESTIONNAIRE D'ERREURS
    handleError(errorEvent) {
        this.createNotification(
            'error',
            'Erreur Système',
            errorEvent.message || 'Une erreur est survenue',
            {
                priority: 'high',
                persistent: true,
                actions: [
                    { text: 'Voir Détails', callback: () => this.openErrorLog() },
                    { text: 'Ignorer', callback: () => {} }
                ]
            }
        );
    }

    // 🔄 TRAITER LA QUEUE DE NOTIFICATIONS
    async processNotificationQueue() {
        if (this.isProcessingQueue || this.notificationQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;

        while (this.notificationQueue.length > 0 && this.getVisibleNotificationsCount() < this.settings.maxVisible) {
            const notification = this.notificationQueue.shift();
            await this.displayNotification(notification);
            
            // Délai entre les notifications
            if (this.notificationQueue.length > 0) {
                await this.delay(this.settings.queueDelay);
            }
        }

        this.isProcessingQueue = false;
    }

    // 🔧 MÉTHODES UTILITAIRES
    getVisibleNotificationsCount() {
        return Array.from(this.notifications.values()).filter(n => n.visible).length;
    }

    getIconPath(iconName) {
        const iconMap = {
            info: 'info.png',
            success: 'success.png',
            warning: 'warning.png',
            error: 'error.png',
            brain: 'brain.png',
            shield: 'shield.png',
            gear: 'gear.png'
        };

        const iconFile = iconMap[iconName] || 'info.png';
        const iconPath = path.join(__dirname, '../public/img/notifications', iconFile);
        
        // Vérifier si le fichier existe (optionnel)
        return iconPath;
    }

    getPriorityLevel(priority) {
        const priorityMap = {
            low: 'low',
            normal: 'normal',
            high: 'critical',
            critical: 'critical'
        };
        return priorityMap[priority] || 'normal';
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    // 🎯 MÉTHODES D'OUVERTURE D'INTERFACE
    openMainInterface() {
        // Logique pour ouvrir l'interface principale
        logger.debug('NOTIFICATIONS', 'Ouverture interface principale');
    }

    openDeepSeekInterface() {
        // Logique pour ouvrir l'interface DeepSeek
        logger.debug('NOTIFICATIONS', 'Ouverture interface DeepSeek');
    }

    openSecurityPanel() {
        // Logique pour ouvrir le panneau de sécurité
        logger.debug('NOTIFICATIONS', 'Ouverture panneau sécurité');
    }

    openErrorLog() {
        // Logique pour ouvrir les logs d'erreur
        logger.debug('NOTIFICATIONS', 'Ouverture logs d\'erreur');
    }

    // 📢 MÉTHODES PUBLIQUES SPÉCIALISÉES
    notifyDeepSeekStatus(connected, message) {
        return this.createNotification(
            connected ? 'success' : 'warning',
            'DeepSeek R1 8B',
            message,
            { priority: 'high' }
        );
    }

    notifySecurityAlert(level, message) {
        return this.createNotification(
            level === 'HIGH' || level === 'CRITICAL' ? 'error' : 'security',
            'Alerte Sécurité',
            message,
            { priority: level === 'CRITICAL' ? 'critical' : 'high', persistent: level === 'CRITICAL' }
        );
    }

    notifySystemHealth(status, message) {
        return this.createNotification(
            status === 'critical' ? 'error' : status === 'warning' ? 'warning' : 'info',
            'Santé Système',
            message,
            { priority: status === 'critical' ? 'high' : 'normal' }
        );
    }

    // ⚙️ CONFIGURATION
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        logger.info('NOTIFICATIONS', 'Paramètres mis à jour', newSettings);
    }

    // 🧹 NETTOYAGE
    clearAllNotifications() {
        for (const notification of this.notifications.values()) {
            if (notification.nativeNotification) {
                notification.nativeNotification.close();
            }
        }
        this.notifications.clear();
        this.notificationQueue = [];
        logger.info('NOTIFICATIONS', 'Toutes les notifications supprimées');
    }

    // 📊 STATISTIQUES
    getStatistics() {
        return {
            visible: this.getVisibleNotificationsCount(),
            queued: this.notificationQueue.length,
            total: this.notifications.size,
            settings: this.settings,
            types: Object.keys(this.notificationTypes)
        };
    }
}

module.exports = ElectronNotificationSystem;
