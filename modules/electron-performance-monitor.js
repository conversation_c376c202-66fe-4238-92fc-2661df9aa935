/**
 * 📊 LOUNA AI ELECTRON - MONITEUR DE PERFORMANCE AVANCÉ
 * Surveillance temps réel des performances système et optimisation automatique
 */

const os = require('os');
const fs = require('fs');
const path = require('path');
const { app, powerMonitor } = require('electron');
const logger = require('./electron-logger');

class ElectronPerformanceMonitor {
    constructor(stateManager, notificationSystem) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.performanceHistory = [];
        this.maxHistoryLength = 1000;
        
        // Seuils d'alerte
        this.thresholds = {
            cpu: {
                warning: 70,
                critical: 85
            },
            memory: {
                warning: 75,
                critical: 90
            },
            temperature: {
                warning: 40.0,
                critical: 45.0
            },
            disk: {
                warning: 80,
                critical: 95
            },
            network: {
                warning: 80,
                critical: 95
            }
        };
        
        // Métriques actuelles
        this.currentMetrics = {
            cpu: {
                usage: 0,
                cores: os.cpus().length,
                model: os.cpus()[0].model,
                speed: os.cpus()[0].speed
            },
            memory: {
                total: os.totalmem(),
                free: os.freemem(),
                used: 0,
                percentage: 0,
                process: process.memoryUsage()
            },
            disk: {
                total: 0,
                free: 0,
                used: 0,
                percentage: 0
            },
            network: {
                bytesReceived: 0,
                bytesSent: 0,
                packetsReceived: 0,
                packetsSent: 0
            },
            system: {
                uptime: 0,
                loadAverage: os.loadavg(),
                platform: os.platform(),
                arch: os.arch(),
                nodeVersion: process.version,
                electronVersion: process.versions.electron
            },
            performance: {
                fps: 0,
                renderTime: 0,
                scriptTime: 0,
                gcTime: 0
            }
        };
        
        // Optimisations automatiques
        this.autoOptimizations = {
            memoryCleanup: true,
            cpuThrottling: true,
            diskCleanup: true,
            networkOptimization: true
        };
        
        // Répertoire des rapports
        this.reportsDir = path.join(__dirname, '../data/performance-reports');
        
        logger.system('Moniteur de performance Electron initialisé');
        this.initializeMonitor();
    }

    // 🔧 INITIALISER LE MONITEUR
    async initializeMonitor() {
        try {
            // Créer le répertoire des rapports
            this.ensureReportsDirectory();
            
            // Configurer les événements système
            this.setupSystemEvents();
            
            // Démarrer la surveillance
            this.startMonitoring();
            
            logger.success('PERFORMANCE', 'Moniteur de performance initialisé');
        } catch (error) {
            logger.error('PERFORMANCE', 'Erreur initialisation moniteur', { error: error.message });
        }
    }

    // 📁 CRÉER LE RÉPERTOIRE DES RAPPORTS
    ensureReportsDirectory() {
        if (!fs.existsSync(this.reportsDir)) {
            fs.mkdirSync(this.reportsDir, { recursive: true });
            logger.debug('PERFORMANCE', `Répertoire rapports créé: ${this.reportsDir}`);
        }
    }

    // ⚙️ CONFIGURER LES ÉVÉNEMENTS SYSTÈME
    setupSystemEvents() {
        // Surveillance de l'alimentation
        if (powerMonitor) {
            powerMonitor.on('suspend', () => {
                logger.info('PERFORMANCE', 'Système en veille - Pause surveillance');
                this.pauseMonitoring();
            });

            powerMonitor.on('resume', () => {
                logger.info('PERFORMANCE', 'Système réveillé - Reprise surveillance');
                this.resumeMonitoring();
            });

            powerMonitor.on('on-ac', () => {
                logger.info('PERFORMANCE', 'Alimentation secteur connectée');
                this.adjustPerformanceMode('high');
            });

            powerMonitor.on('on-battery', () => {
                logger.info('PERFORMANCE', 'Fonctionnement sur batterie');
                this.adjustPerformanceMode('battery');
            });
        }

        // Surveillance mémoire du processus
        process.on('memoryUsage', (usage) => {
            this.currentMetrics.memory.process = usage;
        });
    }

    // 🚀 DÉMARRER LA SURVEILLANCE
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, 2000); // Toutes les 2 secondes

        logger.info('PERFORMANCE', 'Surveillance des performances démarrée');
    }

    // ⏸️ METTRE EN PAUSE LA SURVEILLANCE
    pauseMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        this.isMonitoring = false;
    }

    // ▶️ REPRENDRE LA SURVEILLANCE
    resumeMonitoring() {
        if (!this.isMonitoring) {
            this.startMonitoring();
        }
    }

    // 📊 COLLECTER LES MÉTRIQUES
    async collectMetrics() {
        try {
            const timestamp = Date.now();
            
            // Métriques CPU
            await this.collectCPUMetrics();
            
            // Métriques mémoire
            this.collectMemoryMetrics();
            
            // Métriques disque
            await this.collectDiskMetrics();
            
            // Métriques réseau
            this.collectNetworkMetrics();
            
            // Métriques système
            this.collectSystemMetrics();
            
            // Métriques performance
            this.collectPerformanceMetrics();
            
            // Ajouter à l'historique
            this.addToHistory(timestamp);
            
            // Vérifier les seuils
            this.checkThresholds();
            
            // Optimisations automatiques
            this.performAutoOptimizations();
            
            // Mettre à jour l'état
            this.updateState();
            
        } catch (error) {
            logger.error('PERFORMANCE', 'Erreur collecte métriques', { error: error.message });
        }
    }

    // 🖥️ COLLECTER LES MÉTRIQUES CPU
    async collectCPUMetrics() {
        return new Promise((resolve) => {
            const startMeasure = process.cpuUsage();
            
            setTimeout(() => {
                const endMeasure = process.cpuUsage(startMeasure);
                const totalUsage = (endMeasure.user + endMeasure.system) / 1000; // en millisecondes
                
                this.currentMetrics.cpu.usage = Math.min(100, (totalUsage / 1000) * 100);
                resolve();
            }, 100);
        });
    }

    // 💾 COLLECTER LES MÉTRIQUES MÉMOIRE
    collectMemoryMetrics() {
        const totalMem = os.totalmem();
        const freeMem = os.freemem();
        const usedMem = totalMem - freeMem;
        
        this.currentMetrics.memory = {
            ...this.currentMetrics.memory,
            total: totalMem,
            free: freeMem,
            used: usedMem,
            percentage: (usedMem / totalMem) * 100,
            process: process.memoryUsage()
        };
    }

    // 💿 COLLECTER LES MÉTRIQUES DISQUE
    async collectDiskMetrics() {
        try {
            const stats = fs.statSync(process.cwd());
            // Simulation des métriques disque (remplacer par vraie logique)
            this.currentMetrics.disk = {
                total: 1000000000000, // 1TB
                free: 500000000000,   // 500GB
                used: 500000000000,   // 500GB
                percentage: 50
            };
        } catch (error) {
            logger.debug('PERFORMANCE', 'Erreur métriques disque', { error: error.message });
        }
    }

    // 🌐 COLLECTER LES MÉTRIQUES RÉSEAU
    collectNetworkMetrics() {
        // Simulation des métriques réseau (remplacer par vraie logique)
        this.currentMetrics.network = {
            bytesReceived: Math.floor(Math.random() * 1000000),
            bytesSent: Math.floor(Math.random() * 1000000),
            packetsReceived: Math.floor(Math.random() * 1000),
            packetsSent: Math.floor(Math.random() * 1000)
        };
    }

    // ⚙️ COLLECTER LES MÉTRIQUES SYSTÈME
    collectSystemMetrics() {
        this.currentMetrics.system = {
            ...this.currentMetrics.system,
            uptime: os.uptime(),
            loadAverage: os.loadavg()
        };
    }

    // 🎯 COLLECTER LES MÉTRIQUES PERFORMANCE
    collectPerformanceMetrics() {
        // Simulation des métriques de performance (remplacer par vraie logique)
        this.currentMetrics.performance = {
            fps: 60 - Math.floor(Math.random() * 10),
            renderTime: Math.random() * 16.67, // ms pour 60fps
            scriptTime: Math.random() * 5,
            gcTime: Math.random() * 2
        };
    }

    // 📈 AJOUTER À L'HISTORIQUE
    addToHistory(timestamp) {
        const snapshot = {
            timestamp,
            cpu: this.currentMetrics.cpu.usage,
            memory: this.currentMetrics.memory.percentage,
            disk: this.currentMetrics.disk.percentage,
            fps: this.currentMetrics.performance.fps
        };
        
        this.performanceHistory.push(snapshot);
        
        // Limiter la taille de l'historique
        if (this.performanceHistory.length > this.maxHistoryLength) {
            this.performanceHistory.shift();
        }
    }

    // ⚠️ VÉRIFIER LES SEUILS
    checkThresholds() {
        const alerts = [];
        
        // Vérifier CPU
        if (this.currentMetrics.cpu.usage > this.thresholds.cpu.critical) {
            alerts.push({
                type: 'critical',
                category: 'cpu',
                message: `Utilisation CPU critique: ${this.currentMetrics.cpu.usage.toFixed(1)}%`
            });
        } else if (this.currentMetrics.cpu.usage > this.thresholds.cpu.warning) {
            alerts.push({
                type: 'warning',
                category: 'cpu',
                message: `Utilisation CPU élevée: ${this.currentMetrics.cpu.usage.toFixed(1)}%`
            });
        }
        
        // Vérifier mémoire
        if (this.currentMetrics.memory.percentage > this.thresholds.memory.critical) {
            alerts.push({
                type: 'critical',
                category: 'memory',
                message: `Utilisation mémoire critique: ${this.currentMetrics.memory.percentage.toFixed(1)}%`
            });
        } else if (this.currentMetrics.memory.percentage > this.thresholds.memory.warning) {
            alerts.push({
                type: 'warning',
                category: 'memory',
                message: `Utilisation mémoire élevée: ${this.currentMetrics.memory.percentage.toFixed(1)}%`
            });
        }
        
        // Envoyer les alertes
        for (const alert of alerts) {
            this.sendAlert(alert);
        }
    }

    // 🚨 ENVOYER UNE ALERTE
    sendAlert(alert) {
        logger.warn('PERFORMANCE', alert.message);
        
        if (this.notificationSystem) {
            this.notificationSystem.createNotification(
                alert.type === 'critical' ? 'error' : 'warning',
                '⚠️ Alerte Performance',
                alert.message,
                {
                    priority: alert.type === 'critical' ? 'critical' : 'high',
                    persistent: alert.type === 'critical'
                }
            );
        }
    }

    // ⚡ OPTIMISATIONS AUTOMATIQUES
    performAutoOptimizations() {
        // Nettoyage mémoire automatique
        if (this.autoOptimizations.memoryCleanup && 
            this.currentMetrics.memory.percentage > this.thresholds.memory.warning) {
            this.optimizeMemory();
        }
        
        // Limitation CPU automatique
        if (this.autoOptimizations.cpuThrottling && 
            this.currentMetrics.cpu.usage > this.thresholds.cpu.warning) {
            this.optimizeCPU();
        }
    }

    // 💾 OPTIMISER LA MÉMOIRE
    optimizeMemory() {
        try {
            // Forcer le garbage collection
            if (global.gc) {
                global.gc();
                logger.info('PERFORMANCE', 'Garbage collection forcé');
            }
            
            // Nettoyer les caches
            this.clearCaches();
            
        } catch (error) {
            logger.error('PERFORMANCE', 'Erreur optimisation mémoire', { error: error.message });
        }
    }

    // 🖥️ OPTIMISER LE CPU
    optimizeCPU() {
        try {
            // Réduire la fréquence de surveillance temporairement
            if (this.monitoringInterval) {
                clearInterval(this.monitoringInterval);
                this.monitoringInterval = setInterval(() => {
                    this.collectMetrics();
                }, 5000); // Réduire à toutes les 5 secondes
                
                // Revenir à la normale après 1 minute
                setTimeout(() => {
                    if (this.monitoringInterval) {
                        clearInterval(this.monitoringInterval);
                        this.monitoringInterval = setInterval(() => {
                            this.collectMetrics();
                        }, 2000);
                    }
                }, 60000);
            }
            
            logger.info('PERFORMANCE', 'Optimisation CPU appliquée');
        } catch (error) {
            logger.error('PERFORMANCE', 'Erreur optimisation CPU', { error: error.message });
        }
    }

    // 🧹 NETTOYER LES CACHES
    clearCaches() {
        // Nettoyer les caches internes
        if (this.performanceHistory.length > 500) {
            this.performanceHistory = this.performanceHistory.slice(-500);
        }
        
        logger.debug('PERFORMANCE', 'Caches nettoyés');
    }

    // 🔋 AJUSTER LE MODE PERFORMANCE
    adjustPerformanceMode(mode) {
        switch (mode) {
            case 'high':
                // Mode haute performance
                this.monitoringInterval && clearInterval(this.monitoringInterval);
                this.monitoringInterval = setInterval(() => {
                    this.collectMetrics();
                }, 1000); // Plus fréquent
                break;
                
            case 'battery':
                // Mode économie d'énergie
                this.monitoringInterval && clearInterval(this.monitoringInterval);
                this.monitoringInterval = setInterval(() => {
                    this.collectMetrics();
                }, 5000); // Moins fréquent
                break;
                
            default:
                // Mode normal
                this.monitoringInterval && clearInterval(this.monitoringInterval);
                this.monitoringInterval = setInterval(() => {
                    this.collectMetrics();
                }, 2000);
        }
        
        logger.info('PERFORMANCE', `Mode performance ajusté: ${mode}`);
    }

    // 🔄 METTRE À JOUR L'ÉTAT
    updateState() {
        if (this.stateManager) {
            this.stateManager.updateState('performance', {
                cpu: this.currentMetrics.cpu.usage,
                memory: this.currentMetrics.memory.percentage,
                disk: this.currentMetrics.disk.percentage,
                fps: this.currentMetrics.performance.fps,
                lastUpdate: Date.now()
            });
        }
    }

    // 📊 OBTENIR LES MÉTRIQUES ACTUELLES
    getCurrentMetrics() {
        return { ...this.currentMetrics };
    }

    // 📈 OBTENIR L'HISTORIQUE
    getPerformanceHistory(duration = 3600000) { // 1 heure par défaut
        const cutoff = Date.now() - duration;
        return this.performanceHistory.filter(entry => entry.timestamp > cutoff);
    }

    // 📋 GÉNÉRER UN RAPPORT
    generateReport() {
        const report = {
            timestamp: Date.now(),
            summary: {
                averageCPU: this.calculateAverage('cpu'),
                averageMemory: this.calculateAverage('memory'),
                averageFPS: this.calculateAverage('fps'),
                uptime: this.currentMetrics.system.uptime
            },
            current: this.currentMetrics,
            history: this.getPerformanceHistory(),
            thresholds: this.thresholds,
            optimizations: this.autoOptimizations
        };
        
        // Sauvegarder le rapport
        const reportPath = path.join(this.reportsDir, `performance-${Date.now()}.json`);
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        logger.info('PERFORMANCE', `Rapport généré: ${reportPath}`);
        return report;
    }

    // 📊 CALCULER LA MOYENNE
    calculateAverage(metric) {
        if (this.performanceHistory.length === 0) return 0;
        
        const sum = this.performanceHistory.reduce((acc, entry) => acc + entry[metric], 0);
        return sum / this.performanceHistory.length;
    }

    // ⚙️ CONFIGURER LES SEUILS
    configureThresholds(newThresholds) {
        this.thresholds = { ...this.thresholds, ...newThresholds };
        logger.info('PERFORMANCE', 'Seuils mis à jour', newThresholds);
    }

    // 🔧 CONFIGURER LES OPTIMISATIONS
    configureOptimizations(optimizations) {
        this.autoOptimizations = { ...this.autoOptimizations, ...optimizations };
        logger.info('PERFORMANCE', 'Optimisations configurées', optimizations);
    }

    // 🧹 NETTOYAGE
    cleanup() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
        }
        
        this.isMonitoring = false;
        
        // Générer un rapport final
        this.generateReport();
        
        logger.info('PERFORMANCE', 'Moniteur de performance nettoyé');
    }
}

module.exports = ElectronPerformanceMonitor;
