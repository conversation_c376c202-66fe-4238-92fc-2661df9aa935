/**
 * 🔌 LOUNA AI ELECTRON - GESTIONNAIRE DE PLUGINS AVANCÉ
 * Système de plugins dynamiques pour étendre les fonctionnalités
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');
const logger = require('./electron-logger');

class ElectronPluginManager extends EventEmitter {
    constructor(stateManager, ipcManager) {
        super();
        this.stateManager = stateManager;
        this.ipcManager = ipcManager;
        this.plugins = new Map();
        this.pluginHooks = new Map();
        this.pluginConfigs = new Map();
        
        // Répertoires des plugins
        this.pluginsDir = path.join(__dirname, '../plugins');
        this.userPluginsDir = path.join(__dirname, '../data/user-plugins');
        
        // API exposée aux plugins
        this.pluginAPI = {
            logger: logger,
            state: this.stateManager,
            ipc: this.ipcManager,
            hooks: this.createHookAPI(),
            utils: this.createUtilsAPI()
        };

        logger.system('Gestionnaire de plugins Electron initialisé');
        this.initializePluginSystem();
    }

    // 🔧 INITIALISER LE SYSTÈME DE PLUGINS
    async initializePluginSystem() {
        try {
            // Créer les répertoires
            this.ensureDirectories();
            
            // Charger les plugins par défaut
            await this.loadDefaultPlugins();
            
            // Charger les plugins utilisateur
            await this.loadUserPlugins();
            
            // Initialiser les plugins chargés
            await this.initializeLoadedPlugins();
            
            logger.success('PLUGINS', `${this.plugins.size} plugins chargés et initialisés`);
        } catch (error) {
            logger.error('PLUGINS', 'Erreur initialisation système plugins', { error: error.message });
        }
    }

    // 📁 CRÉER LES RÉPERTOIRES
    ensureDirectories() {
        [this.pluginsDir, this.userPluginsDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug('PLUGINS', `Répertoire créé: ${dir}`);
            }
        });
    }

    // 🔌 CHARGER LES PLUGINS PAR DÉFAUT
    async loadDefaultPlugins() {
        const defaultPlugins = {
            'voice-synthesizer': {
                name: 'Synthétiseur Vocal Avancé',
                version: '1.0.0',
                description: 'Synthèse vocale avec voix naturelles multiples',
                author: 'LOUNA Team',
                category: 'audio',
                enabled: true,
                config: {
                    defaultVoice: 'female-fr',
                    rate: 0.8,
                    pitch: 1.1,
                    volume: 0.7
                },
                hooks: ['chat-response', 'notification-display'],
                permissions: ['audio', 'speech'],
                main: this.createVoiceSynthesizerPlugin()
            },

            'brain-visualizer': {
                name: 'Visualiseur Cerveau 3D Avancé',
                version: '1.0.0',
                description: 'Visualisation 3D interactive du cerveau avec WebGL',
                author: 'LOUNA Team',
                category: 'visualization',
                enabled: true,
                config: {
                    renderQuality: 'high',
                    animationSpeed: 1.0,
                    particleCount: 10000
                },
                hooks: ['neuron-activity', 'brain-state-change'],
                permissions: ['webgl', 'performance'],
                main: this.createBrainVisualizerPlugin()
            },

            'auto-backup': {
                name: 'Sauvegarde Automatique Intelligente',
                version: '1.0.0',
                description: 'Système de sauvegarde automatique avec versioning',
                author: 'LOUNA Team',
                category: 'utility',
                enabled: true,
                config: {
                    interval: 300000, // 5 minutes
                    maxBackups: 10,
                    compression: true
                },
                hooks: ['memory-change', 'system-shutdown'],
                permissions: ['filesystem', 'compression'],
                main: this.createAutoBackupPlugin()
            },

            'performance-monitor': {
                name: 'Moniteur Performance Temps Réel',
                version: '1.0.0',
                description: 'Surveillance avancée des performances système',
                author: 'LOUNA Team',
                category: 'monitoring',
                enabled: true,
                config: {
                    updateInterval: 1000,
                    alertThresholds: {
                        cpu: 80,
                        memory: 85,
                        temperature: 41.0
                    }
                },
                hooks: ['metrics-update', 'performance-alert'],
                permissions: ['system', 'monitoring'],
                main: this.createPerformanceMonitorPlugin()
            }
        };

        for (const [id, plugin] of Object.entries(defaultPlugins)) {
            this.plugins.set(id, {
                ...plugin,
                id,
                type: 'default',
                loaded: false,
                initialized: false,
                instance: null
            });
        }

        logger.debug('PLUGINS', `${Object.keys(defaultPlugins).length} plugins par défaut chargés`);
    }

    // 📂 CHARGER LES PLUGINS UTILISATEUR
    async loadUserPlugins() {
        try {
            if (!fs.existsSync(this.userPluginsDir)) {
                return;
            }

            const pluginDirs = fs.readdirSync(this.userPluginsDir, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            for (const pluginDir of pluginDirs) {
                try {
                    await this.loadUserPlugin(pluginDir);
                } catch (error) {
                    logger.warn('PLUGINS', `Erreur chargement plugin utilisateur ${pluginDir}`, { error: error.message });
                }
            }

            logger.debug('PLUGINS', `${pluginDirs.length} plugins utilisateur traités`);
        } catch (error) {
            logger.error('PLUGINS', 'Erreur chargement plugins utilisateur', { error: error.message });
        }
    }

    // 📦 CHARGER UN PLUGIN UTILISATEUR
    async loadUserPlugin(pluginDir) {
        const pluginPath = path.join(this.userPluginsDir, pluginDir);
        const manifestPath = path.join(pluginPath, 'manifest.json');
        const mainPath = path.join(pluginPath, 'main.js');

        if (!fs.existsSync(manifestPath) || !fs.existsSync(mainPath)) {
            throw new Error('Fichiers manifest.json ou main.js manquants');
        }

        const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
        const pluginMain = require(mainPath);

        const plugin = {
            ...manifest,
            id: pluginDir,
            type: 'user',
            path: pluginPath,
            loaded: false,
            initialized: false,
            instance: null,
            main: pluginMain
        };

        // Valider le plugin
        if (!this.validatePlugin(plugin)) {
            throw new Error('Plugin invalide');
        }

        this.plugins.set(pluginDir, plugin);
        logger.debug('PLUGINS', `Plugin utilisateur chargé: ${plugin.name}`);
    }

    // ✅ VALIDER UN PLUGIN
    validatePlugin(plugin) {
        const required = ['name', 'version', 'description', 'main'];
        
        for (const field of required) {
            if (!plugin[field]) {
                logger.warn('PLUGINS', `Champ manquant: ${field}`);
                return false;
            }
        }

        if (typeof plugin.main !== 'function' && typeof plugin.main !== 'object') {
            logger.warn('PLUGINS', 'Main doit être une fonction ou un objet');
            return false;
        }

        return true;
    }

    // 🚀 INITIALISER LES PLUGINS CHARGÉS
    async initializeLoadedPlugins() {
        for (const [id, plugin] of this.plugins) {
            if (plugin.enabled) {
                try {
                    await this.initializePlugin(id);
                } catch (error) {
                    logger.error('PLUGINS', `Erreur initialisation plugin ${id}`, { error: error.message });
                }
            }
        }
    }

    // 🔌 INITIALISER UN PLUGIN
    async initializePlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`Plugin non trouvé: ${pluginId}`);
        }

        if (plugin.initialized) {
            return;
        }

        try {
            // Créer l'instance du plugin
            if (typeof plugin.main === 'function') {
                plugin.instance = new plugin.main(this.pluginAPI, plugin.config || {});
            } else {
                plugin.instance = plugin.main;
            }

            // Initialiser le plugin
            if (plugin.instance.initialize) {
                await plugin.instance.initialize();
            }

            // Enregistrer les hooks
            if (plugin.hooks && plugin.instance.hooks) {
                for (const hookName of plugin.hooks) {
                    this.registerPluginHook(pluginId, hookName, plugin.instance.hooks[hookName]);
                }
            }

            plugin.loaded = true;
            plugin.initialized = true;

            logger.success('PLUGINS', `Plugin initialisé: ${plugin.name}`);
            this.emit('plugin-initialized', { pluginId, plugin });

        } catch (error) {
            logger.error('PLUGINS', `Erreur initialisation plugin ${pluginId}`, { error: error.message });
            throw error;
        }
    }

    // 🪝 ENREGISTRER UN HOOK DE PLUGIN
    registerPluginHook(pluginId, hookName, hookFunction) {
        if (!this.pluginHooks.has(hookName)) {
            this.pluginHooks.set(hookName, new Map());
        }

        this.pluginHooks.get(hookName).set(pluginId, hookFunction);
        logger.debug('PLUGINS', `Hook enregistré: ${pluginId} -> ${hookName}`);
    }

    // 🎣 EXÉCUTER LES HOOKS
    async executeHooks(hookName, data = {}) {
        const hooks = this.pluginHooks.get(hookName);
        if (!hooks) {
            return data;
        }

        let result = data;
        for (const [pluginId, hookFunction] of hooks) {
            try {
                if (typeof hookFunction === 'function') {
                    result = await hookFunction(result, this.pluginAPI) || result;
                }
            } catch (error) {
                logger.error('PLUGINS', `Erreur hook ${hookName} du plugin ${pluginId}`, { error: error.message });
            }
        }

        return result;
    }

    // 🔧 CRÉER L'API DES HOOKS
    createHookAPI() {
        return {
            register: (hookName, callback) => {
                // Permet aux plugins d'enregistrer leurs propres hooks
                this.registerPluginHook('dynamic', hookName, callback);
            },
            execute: (hookName, data) => {
                return this.executeHooks(hookName, data);
            }
        };
    }

    // 🛠️ CRÉER L'API DES UTILITAIRES
    createUtilsAPI() {
        return {
            formatDate: (date) => new Date(date).toLocaleString('fr-FR'),
            formatBytes: (bytes) => {
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                if (bytes === 0) return '0 Bytes';
                const i = Math.floor(Math.log(bytes) / Math.log(1024));
                return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
            },
            generateId: () => Date.now().toString(36) + Math.random().toString(36).substr(2),
            delay: (ms) => new Promise(resolve => setTimeout(resolve, ms))
        };
    }

    // 🎤 CRÉER LE PLUGIN SYNTHÉTISEUR VOCAL
    createVoiceSynthesizerPlugin() {
        return class VoiceSynthesizerPlugin {
            constructor(api, config) {
                this.api = api;
                this.config = config;
                this.synthesis = null;
            }

            async initialize() {
                if ('speechSynthesis' in window) {
                    this.synthesis = window.speechSynthesis;
                    this.api.logger.success('VOICE_PLUGIN', 'Synthétiseur vocal initialisé');
                } else {
                    throw new Error('Synthèse vocale non supportée');
                }
            }

            hooks = {
                'chat-response': async (data) => {
                    if (data.response && this.config.autoSpeak) {
                        await this.speak(data.response);
                    }
                    return data;
                },
                'notification-display': async (data) => {
                    if (data.message && this.config.speakNotifications) {
                        await this.speak(data.message);
                    }
                    return data;
                }
            };

            async speak(text) {
                if (!this.synthesis) return;

                const utterance = new SpeechSynthesisUtterance(text);
                utterance.rate = this.config.rate || 0.8;
                utterance.pitch = this.config.pitch || 1.1;
                utterance.volume = this.config.volume || 0.7;
                utterance.lang = 'fr-FR';

                this.synthesis.speak(utterance);
            }
        };
    }

    // 🧠 CRÉER LE PLUGIN VISUALISEUR CERVEAU
    createBrainVisualizerPlugin() {
        return class BrainVisualizerPlugin {
            constructor(api, config) {
                this.api = api;
                this.config = config;
                this.scene = null;
                this.neurons = [];
            }

            async initialize() {
                this.api.logger.success('BRAIN_PLUGIN', 'Visualiseur cerveau 3D initialisé');
            }

            hooks = {
                'neuron-activity': async (data) => {
                    this.updateNeuronActivity(data);
                    return data;
                },
                'brain-state-change': async (data) => {
                    this.updateBrainState(data);
                    return data;
                }
            };

            updateNeuronActivity(data) {
                // Logique de mise à jour de l'activité neuronale
                this.api.logger.debug('BRAIN_PLUGIN', 'Activité neuronale mise à jour');
            }

            updateBrainState(data) {
                // Logique de mise à jour de l'état du cerveau
                this.api.logger.debug('BRAIN_PLUGIN', 'État du cerveau mis à jour');
            }
        };
    }

    // 💾 CRÉER LE PLUGIN SAUVEGARDE AUTO
    createAutoBackupPlugin() {
        return class AutoBackupPlugin {
            constructor(api, config) {
                this.api = api;
                this.config = config;
                this.backupInterval = null;
            }

            async initialize() {
                this.startAutoBackup();
                this.api.logger.success('BACKUP_PLUGIN', 'Sauvegarde automatique initialisée');
            }

            startAutoBackup() {
                this.backupInterval = setInterval(() => {
                    this.performBackup();
                }, this.config.interval);
            }

            async performBackup() {
                try {
                    // Logique de sauvegarde
                    this.api.logger.info('BACKUP_PLUGIN', 'Sauvegarde automatique effectuée');
                } catch (error) {
                    this.api.logger.error('BACKUP_PLUGIN', 'Erreur sauvegarde', { error: error.message });
                }
            }

            hooks = {
                'system-shutdown': async (data) => {
                    if (this.backupInterval) {
                        clearInterval(this.backupInterval);
                    }
                    await this.performBackup();
                    return data;
                }
            };
        };
    }

    // 📊 CRÉER LE PLUGIN MONITEUR PERFORMANCE
    createPerformanceMonitorPlugin() {
        return class PerformanceMonitorPlugin {
            constructor(api, config) {
                this.api = api;
                this.config = config;
                this.monitorInterval = null;
            }

            async initialize() {
                this.startMonitoring();
                this.api.logger.success('PERF_PLUGIN', 'Moniteur performance initialisé');
            }

            startMonitoring() {
                this.monitorInterval = setInterval(() => {
                    this.checkPerformance();
                }, this.config.updateInterval);
            }

            checkPerformance() {
                const memoryUsage = process.memoryUsage();
                const memoryPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

                if (memoryPercent > this.config.alertThresholds.memory) {
                    this.api.logger.warn('PERF_PLUGIN', `Utilisation mémoire élevée: ${memoryPercent.toFixed(1)}%`);
                }
            }

            hooks = {
                'metrics-update': async (data) => {
                    // Ajouter des métriques de performance
                    data.performance = {
                        memory: process.memoryUsage(),
                        uptime: process.uptime()
                    };
                    return data;
                }
            };
        };
    }

    // 🔌 ACTIVER/DÉSACTIVER UN PLUGIN
    async togglePlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`Plugin non trouvé: ${pluginId}`);
        }

        if (plugin.enabled) {
            await this.disablePlugin(pluginId);
        } else {
            await this.enablePlugin(pluginId);
        }
    }

    async enablePlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`Plugin non trouvé: ${pluginId}`);
        }

        plugin.enabled = true;
        if (!plugin.initialized) {
            await this.initializePlugin(pluginId);
        }

        logger.success('PLUGINS', `Plugin activé: ${plugin.name}`);
    }

    async disablePlugin(pluginId) {
        const plugin = this.plugins.get(pluginId);
        if (!plugin) {
            throw new Error(`Plugin non trouvé: ${pluginId}`);
        }

        // Désactiver les hooks
        for (const hookName of plugin.hooks || []) {
            const hooks = this.pluginHooks.get(hookName);
            if (hooks) {
                hooks.delete(pluginId);
            }
        }

        // Nettoyer l'instance
        if (plugin.instance && plugin.instance.cleanup) {
            await plugin.instance.cleanup();
        }

        plugin.enabled = false;
        plugin.initialized = false;
        plugin.instance = null;

        logger.info('PLUGINS', `Plugin désactivé: ${plugin.name}`);
    }

    // 📋 OBTENIR LA LISTE DES PLUGINS
    getPluginsList() {
        const plugins = [];
        for (const [id, plugin] of this.plugins) {
            plugins.push({
                id,
                name: plugin.name,
                version: plugin.version,
                description: plugin.description,
                author: plugin.author,
                category: plugin.category,
                type: plugin.type,
                enabled: plugin.enabled,
                initialized: plugin.initialized
            });
        }
        return plugins.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 📊 OBTENIR LES STATISTIQUES
    getStatistics() {
        const stats = {
            total: this.plugins.size,
            enabled: 0,
            disabled: 0,
            initialized: 0,
            categories: {},
            hooks: this.pluginHooks.size
        };

        for (const plugin of this.plugins.values()) {
            if (plugin.enabled) stats.enabled++;
            else stats.disabled++;
            
            if (plugin.initialized) stats.initialized++;
            
            const category = plugin.category || 'other';
            stats.categories[category] = (stats.categories[category] || 0) + 1;
        }

        return stats;
    }

    // 🧹 NETTOYAGE
    async cleanup() {
        for (const [id, plugin] of this.plugins) {
            if (plugin.initialized) {
                await this.disablePlugin(id);
            }
        }
        
        this.plugins.clear();
        this.pluginHooks.clear();
        
        logger.info('PLUGINS', 'Gestionnaire de plugins nettoyé');
    }
}

module.exports = ElectronPluginManager;
