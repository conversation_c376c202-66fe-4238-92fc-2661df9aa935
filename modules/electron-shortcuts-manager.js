/**
 * ⌨️ LOUNA AI ELECTRON - GESTIONNAIRE DE RACCOURCIS AVANCÉ
 * Système de raccourcis clavier intelligent pour l'application Electron
 */

const { globalShortcut, BrowserWindow } = require('electron');
const logger = require('./electron-logger');

class ElectronShortcutsManager {
    constructor(stateManager, notificationSystem) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.shortcuts = new Map();
        this.isEnabled = true;
        this.mainWindow = null;
        
        // Configuration des raccourcis par défaut
        this.defaultShortcuts = {
            // Navigation principale
            'CommandOrControl+H': {
                name: 'Accueil',
                description: 'Retour à l\'accueil',
                action: 'navigate-home',
                global: false
            },
            'CommandOrControl+C': {
                name: 'Chat IA',
                description: 'Ouvrir le chat IA',
                action: 'navigate-chat',
                global: false
            },
            'CommandOrControl+B': {
                name: 'Cerveau 3D',
                description: 'Visualisation cerveau 3D',
                action: 'navigate-brain',
                global: false
            },
            'CommandOrControl+M': {
                name: 'Mode MCP',
                description: 'Dialogue ChatGPT automatique',
                action: 'navigate-mcp',
                global: false
            },
            'CommandOrControl+S': {
                name: 'Sécurité',
                description: 'Panneau de sécurité',
                action: 'navigate-security',
                global: false
            },
            'CommandOrControl+T': {
                name: 'Mémoire Thermique',
                description: 'Dashboard mémoire thermique',
                action: 'navigate-thermal',
                global: false
            },

            // Contrôles DeepSeek
            'CommandOrControl+D': {
                name: 'Déconnecter DeepSeek',
                description: 'Déconnexion sécurisée DeepSeek',
                action: 'deepseek-disconnect',
                global: false,
                confirm: true
            },
            'CommandOrControl+R': {
                name: 'Reconnecter DeepSeek',
                description: 'Reconnexion DeepSeek',
                action: 'deepseek-reconnect',
                global: false
            },
            'CommandOrControl+Shift+E': {
                name: 'Arrêt Urgence',
                description: 'Arrêt d\'urgence DeepSeek',
                action: 'deepseek-emergency',
                global: true,
                confirm: true
            },

            // Fonctionnalités système
            'CommandOrControl+I': {
                name: 'Informations Système',
                description: 'Afficher les métriques',
                action: 'show-metrics',
                global: false
            },
            'CommandOrControl+L': {
                name: 'Logs Système',
                description: 'Ouvrir les logs',
                action: 'show-logs',
                global: false
            },
            'CommandOrControl+N': {
                name: 'Notifications',
                description: 'Panneau notifications',
                action: 'show-notifications',
                global: false
            },

            // Raccourcis globaux (fonctionnent même quand l'app n'est pas active)
            'CommandOrControl+Shift+L': {
                name: 'Activer LOUNA',
                description: 'Activer/Désactiver LOUNA AI',
                action: 'toggle-louna',
                global: true
            },
            'CommandOrControl+Shift+V': {
                name: 'Voix LOUNA',
                description: 'Activer/Désactiver la voix',
                action: 'toggle-voice',
                global: true
            },

            // Développement
            'F12': {
                name: 'DevTools',
                description: 'Outils de développement',
                action: 'toggle-devtools',
                global: false
            },
            'CommandOrControl+Shift+R': {
                name: 'Recharger',
                description: 'Recharger l\'application',
                action: 'reload-app',
                global: false
            },
            'CommandOrControl+Q': {
                name: 'Quitter',
                description: 'Quitter l\'application',
                action: 'quit-app',
                global: false,
                confirm: true
            }
        };

        logger.system('Gestionnaire de raccourcis Electron initialisé');
        this.initializeShortcuts();
    }

    // 🔧 INITIALISER LES RACCOURCIS
    initializeShortcuts() {
        try {
            // Enregistrer tous les raccourcis par défaut
            for (const [accelerator, config] of Object.entries(this.defaultShortcuts)) {
                this.registerShortcut(accelerator, config);
            }

            logger.success('SHORTCUTS', `${this.shortcuts.size} raccourcis enregistrés`);
        } catch (error) {
            logger.error('SHORTCUTS', 'Erreur initialisation raccourcis', { error: error.message });
        }
    }

    // 📝 ENREGISTRER UN RACCOURCI
    registerShortcut(accelerator, config) {
        try {
            const shortcutHandler = () => {
                this.executeShortcut(accelerator, config);
            };

            if (config.global) {
                // Raccourci global (fonctionne même si l'app n'est pas active)
                const success = globalShortcut.register(accelerator, shortcutHandler);
                if (!success) {
                    logger.warn('SHORTCUTS', `Impossible d'enregistrer le raccourci global: ${accelerator}`);
                    return false;
                }
            } else {
                // Raccourci local (fonctionne seulement quand l'app est active)
                // Ces raccourcis seront gérés par le renderer process
            }

            this.shortcuts.set(accelerator, {
                ...config,
                handler: shortcutHandler,
                registered: true,
                lastUsed: null,
                useCount: 0
            });

            logger.debug('SHORTCUTS', `Raccourci enregistré: ${accelerator} -> ${config.name}`);
            return true;

        } catch (error) {
            logger.error('SHORTCUTS', `Erreur enregistrement raccourci ${accelerator}`, { error: error.message });
            return false;
        }
    }

    // ⚡ EXÉCUTER UN RACCOURCI
    async executeShortcut(accelerator, config) {
        if (!this.isEnabled) {
            return;
        }

        try {
            const shortcut = this.shortcuts.get(accelerator);
            if (!shortcut) {
                logger.warn('SHORTCUTS', `Raccourci non trouvé: ${accelerator}`);
                return;
            }

            // Mettre à jour les statistiques
            shortcut.lastUsed = Date.now();
            shortcut.useCount++;

            logger.debug('SHORTCUTS', `Exécution raccourci: ${accelerator} -> ${config.action}`);

            // Confirmation si nécessaire
            if (config.confirm) {
                const confirmed = await this.confirmAction(config.name, config.description);
                if (!confirmed) {
                    return;
                }
            }

            // Exécuter l'action
            await this.executeAction(config.action, config);

            // Notification si configurée
            if (this.notificationSystem && config.notify !== false) {
                this.notificationSystem.createNotification(
                    'info',
                    'Raccourci Exécuté',
                    `${config.name}: ${config.description}`,
                    { duration: 2000 }
                );
            }

        } catch (error) {
            logger.error('SHORTCUTS', `Erreur exécution raccourci ${accelerator}`, { error: error.message });
        }
    }

    // 🎯 EXÉCUTER UNE ACTION
    async executeAction(action, config) {
        switch (action) {
            // Navigation
            case 'navigate-home':
                this.navigateToPage('/');
                break;
            case 'navigate-chat':
                this.navigateToPage('/louna-interface-nouvelle.html');
                break;
            case 'navigate-brain':
                this.navigateToPage('/brain-3d-spectacular.html');
                break;
            case 'navigate-mcp':
                this.navigateToPage('/mcp-chat-interface.html');
                break;
            case 'navigate-security':
                this.navigateToPage('/security-dashboard.html');
                break;
            case 'navigate-thermal':
                this.navigateToPage('/thermal-memory-dashboard.html');
                break;

            // Contrôles DeepSeek
            case 'deepseek-disconnect':
                await this.executeIPCAction('deepseek-disconnect', 'Déconnexion via raccourci');
                break;
            case 'deepseek-reconnect':
                await this.executeIPCAction('deepseek-reconnect');
                break;
            case 'deepseek-emergency':
                await this.executeIPCAction('deepseek-emergency-stop', 'Arrêt d\'urgence via raccourci');
                break;

            // Système
            case 'show-metrics':
                this.showMetricsWindow();
                break;
            case 'show-logs':
                this.showLogsWindow();
                break;
            case 'show-notifications':
                this.showNotificationsPanel();
                break;
            case 'toggle-louna':
                this.toggleLounaActivation();
                break;
            case 'toggle-voice':
                this.toggleVoice();
                break;

            // Développement
            case 'toggle-devtools':
                this.toggleDevTools();
                break;
            case 'reload-app':
                this.reloadApp();
                break;
            case 'quit-app':
                this.quitApp();
                break;

            default:
                logger.warn('SHORTCUTS', `Action inconnue: ${action}`);
        }
    }

    // 🌐 NAVIGATION
    navigateToPage(url) {
        if (this.mainWindow) {
            this.mainWindow.webContents.send('navigate-to', url);
            logger.debug('SHORTCUTS', `Navigation vers: ${url}`);
        }
    }

    // 📡 EXÉCUTER ACTION IPC
    async executeIPCAction(channel, ...args) {
        if (this.mainWindow) {
            try {
                const result = await this.mainWindow.webContents.executeJavaScript(`
                    window.electronAPI ? window.electronAPI.invoke('${channel}', ${JSON.stringify(args)}) : null
                `);
                logger.debug('SHORTCUTS', `Action IPC exécutée: ${channel}`, result);
                return result;
            } catch (error) {
                logger.error('SHORTCUTS', `Erreur action IPC ${channel}`, { error: error.message });
            }
        }
    }

    // 🔧 ACTIONS SPÉCIALISÉES
    showMetricsWindow() {
        // Créer une fenêtre popup pour les métriques
        const metricsWindow = new BrowserWindow({
            width: 800,
            height: 600,
            parent: this.mainWindow,
            modal: true,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            }
        });

        metricsWindow.loadFile('public/metrics-popup.html');
        logger.debug('SHORTCUTS', 'Fenêtre métriques ouverte');
    }

    showLogsWindow() {
        // Ouvrir les logs dans l'éditeur par défaut
        const { shell } = require('electron');
        shell.openPath(require('path').join(__dirname, '../logs/electron.log'));
        logger.debug('SHORTCUTS', 'Logs ouverts');
    }

    showNotificationsPanel() {
        if (this.mainWindow) {
            this.mainWindow.webContents.send('show-notifications-panel');
            logger.debug('SHORTCUTS', 'Panneau notifications affiché');
        }
    }

    toggleLounaActivation() {
        if (this.stateManager) {
            const currentState = this.stateManager.getState('app');
            const newState = !currentState.active;
            this.stateManager.updateState('app', { active: newState });
            
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    newState ? 'success' : 'warning',
                    'LOUNA AI',
                    newState ? 'Activée' : 'Désactivée',
                    { priority: 'high' }
                );
            }
        }
    }

    toggleVoice() {
        if (this.mainWindow) {
            this.mainWindow.webContents.send('toggle-voice');
            logger.debug('SHORTCUTS', 'Voix basculée');
        }
    }

    toggleDevTools() {
        if (this.mainWindow) {
            if (this.mainWindow.webContents.isDevToolsOpened()) {
                this.mainWindow.webContents.closeDevTools();
            } else {
                this.mainWindow.webContents.openDevTools();
            }
        }
    }

    reloadApp() {
        if (this.mainWindow) {
            this.mainWindow.webContents.reload();
            logger.debug('SHORTCUTS', 'Application rechargée');
        }
    }

    quitApp() {
        const { app } = require('electron');
        app.quit();
    }

    // ❓ CONFIRMATION D'ACTION
    async confirmAction(name, description) {
        if (this.mainWindow) {
            try {
                const result = await this.mainWindow.webContents.executeJavaScript(`
                    confirm('Confirmer l\\'action: ${name}\\n\\n${description}')
                `);
                return result;
            } catch (error) {
                logger.error('SHORTCUTS', 'Erreur confirmation', { error: error.message });
                return false;
            }
        }
        return false;
    }

    // 🔧 GESTION DES RACCOURCIS
    setMainWindow(window) {
        this.mainWindow = window;
        logger.debug('SHORTCUTS', 'Fenêtre principale définie');
    }

    enableShortcuts() {
        this.isEnabled = true;
        logger.info('SHORTCUTS', 'Raccourcis activés');
    }

    disableShortcuts() {
        this.isEnabled = false;
        logger.info('SHORTCUTS', 'Raccourcis désactivés');
    }

    // 📊 STATISTIQUES
    getShortcutStatistics() {
        const stats = {
            total: this.shortcuts.size,
            global: 0,
            local: 0,
            mostUsed: null,
            totalUses: 0
        };

        let maxUses = 0;
        for (const [accelerator, shortcut] of this.shortcuts) {
            if (shortcut.global) stats.global++;
            else stats.local++;
            
            stats.totalUses += shortcut.useCount;
            
            if (shortcut.useCount > maxUses) {
                maxUses = shortcut.useCount;
                stats.mostUsed = { accelerator, ...shortcut };
            }
        }

        return stats;
    }

    // 📋 OBTENIR LA LISTE DES RACCOURCIS
    getShortcutsList() {
        const shortcuts = [];
        for (const [accelerator, config] of this.shortcuts) {
            shortcuts.push({
                accelerator,
                name: config.name,
                description: config.description,
                global: config.global,
                useCount: config.useCount,
                lastUsed: config.lastUsed
            });
        }
        return shortcuts.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 🧹 NETTOYAGE
    cleanup() {
        // Désinscrire tous les raccourcis globaux
        globalShortcut.unregisterAll();
        this.shortcuts.clear();
        logger.info('SHORTCUTS', 'Raccourcis nettoyés');
    }
}

module.exports = ElectronShortcutsManager;
