/**
 * 🧠 LOUNA AI ELECTRON - GESTIONNAIRE D'ÉTAT GLOBAL
 * Gestion centralisée de l'état de l'application Electron
 */

const EventEmitter = require('events');
const config = require('../config/electron-config');

class ElectronStateManager extends EventEmitter {
    constructor() {
        super();
        this.state = {
            // 🚀 État de l'application
            app: {
                initialized: false,
                ready: false,
                version: '3.0.0',
                startTime: Date.now(),
                lastUpdate: Date.now()
            },

            // 🌐 État du serveur
            server: {
                running: false,
                port: null,
                pid: null,
                startTime: null,
                lastResponse: null,
                errors: 0
            },

            // 🤖 État DeepSeek R1 8B
            deepseek: {
                connected: false,
                model: 'deepseek-r1:8b',
                lastResponse: null,
                responseTime: 0,
                totalRequests: 0,
                errors: 0,
                quality: 0.95
            },

            // 🧠 État mémoire thermique
            thermalMemory: {
                initialized: false,
                temperature: 37.0,
                totalEntries: 0,
                zones: {},
                efficiency: 95,
                lastBackup: null
            },

            // 🌀 État système Möbius
            mobius: {
                active: false,
                position: 0,
                phase: 'exploration',
                direction: 1,
                chaosLevel: 0.3,
                thoughtsGenerated: 0,
                authenticReflections: 0
            },

            // 🧮 État du cerveau
            brain: {
                neurons: 1064000,
                synapses: 7448000,
                temperature: 37.0,
                qi: {
                    agent: 100,
                    memory: 0,
                    total: 100
                },
                health: {
                    fatigue: 0,
                    stress: 0,
                    needsRest: false,
                    isResting: false,
                    isDreaming: false
                }
            },

            // 🛡️ État sécurité
            security: {
                guardianActive: false,
                threatLevel: 'LOW',
                lastScan: null,
                blockedRequests: 0,
                quarantinedItems: 0
            },

            // 🌐 État MCP
            mcp: {
                active: false,
                dialogueRunning: false,
                questionsAsked: 0,
                lastQuestion: null,
                chatgptResponses: 0
            },

            // 🎨 État interface
            ui: {
                currentPage: 'home',
                voiceEnabled: false,
                theme: 'default',
                notifications: [],
                errors: []
            },

            // 📊 État métriques
            metrics: {
                lastUpdate: Date.now(),
                updateCount: 0,
                averageResponseTime: 0,
                peakMemoryUsage: 0,
                totalInteractions: 0
            }
        };

        this.history = [];
        this.maxHistoryLength = 1000;
        
        console.log('🧠 Gestionnaire d\'état Electron initialisé');
        this.startPeriodicUpdates();
    }

    // 📊 METTRE À JOUR L'ÉTAT
    updateState(category, updates) {
        if (!this.state[category]) {
            console.warn(`⚠️ Catégorie d'état inconnue: ${category}`);
            return false;
        }

        const oldState = { ...this.state[category] };
        this.state[category] = { ...this.state[category], ...updates };
        this.state[category].lastUpdate = Date.now();

        // Ajouter à l'historique
        this.addToHistory(category, oldState, this.state[category]);

        // Émettre l'événement de changement
        this.emit('stateChanged', {
            category,
            oldState,
            newState: this.state[category],
            timestamp: Date.now()
        });

        console.log(`🔄 État mis à jour [${category}]:`, updates);
        return true;
    }

    // 📖 OBTENIR L'ÉTAT
    getState(category = null) {
        if (category) {
            return this.state[category] || null;
        }
        return { ...this.state };
    }

    // 📊 OBTENIR LES MÉTRIQUES COMPLÈTES
    getMetrics() {
        const uptime = Date.now() - this.state.app.startTime;
        
        return {
            ...this.state,
            computed: {
                uptime,
                uptimeFormatted: this.formatUptime(uptime),
                memoryUsage: process.memoryUsage(),
                performance: {
                    averageResponseTime: this.state.metrics.averageResponseTime,
                    totalInteractions: this.state.metrics.totalInteractions,
                    successRate: this.calculateSuccessRate(),
                    efficiency: this.calculateEfficiency()
                }
            },
            timestamp: Date.now()
        };
    }

    // 📝 AJOUTER À L'HISTORIQUE
    addToHistory(category, oldState, newState) {
        const historyEntry = {
            timestamp: Date.now(),
            category,
            changes: this.getChanges(oldState, newState)
        };

        this.history.push(historyEntry);

        // Limiter la taille de l'historique
        if (this.history.length > this.maxHistoryLength) {
            this.history = this.history.slice(-this.maxHistoryLength);
        }
    }

    // 🔍 DÉTECTER LES CHANGEMENTS
    getChanges(oldState, newState) {
        const changes = {};
        
        for (const key in newState) {
            if (oldState[key] !== newState[key]) {
                changes[key] = {
                    from: oldState[key],
                    to: newState[key]
                };
            }
        }
        
        return changes;
    }

    // ⏱️ FORMATER LE TEMPS DE FONCTIONNEMENT
    formatUptime(uptime) {
        const seconds = Math.floor(uptime / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) {
            return `${days}j ${hours % 24}h ${minutes % 60}m`;
        } else if (hours > 0) {
            return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        } else {
            return `${seconds}s`;
        }
    }

    // 📊 CALCULER LE TAUX DE SUCCÈS
    calculateSuccessRate() {
        const totalRequests = this.state.deepseek.totalRequests;
        const errors = this.state.deepseek.errors + this.state.server.errors;
        
        if (totalRequests === 0) return 100;
        return Math.max(0, ((totalRequests - errors) / totalRequests) * 100);
    }

    // ⚡ CALCULER L'EFFICACITÉ
    calculateEfficiency() {
        const factors = [
            this.state.thermalMemory.efficiency || 95,
            this.state.deepseek.quality * 100 || 95,
            this.calculateSuccessRate(),
            Math.max(0, 100 - (this.state.brain.health.fatigue * 100)),
            Math.max(0, 100 - (this.state.brain.health.stress * 100))
        ];

        return factors.reduce((sum, factor) => sum + factor, 0) / factors.length;
    }

    // 🔄 MISES À JOUR PÉRIODIQUES
    startPeriodicUpdates() {
        setInterval(() => {
            this.updateMetrics();
        }, config.metrics.updateInterval);

        console.log('⏰ Mises à jour périodiques démarrées');
    }

    // 📊 METTRE À JOUR LES MÉTRIQUES
    updateMetrics() {
        const now = Date.now();
        const timeSinceLastUpdate = now - this.state.metrics.lastUpdate;

        this.updateState('metrics', {
            lastUpdate: now,
            updateCount: this.state.metrics.updateCount + 1,
            timeSinceLastUpdate
        });

        // Mettre à jour l'état de l'application
        this.updateState('app', {
            lastUpdate: now
        });
    }

    // 🚨 AJOUTER UNE NOTIFICATION
    addNotification(type, message, data = {}) {
        const notification = {
            id: Date.now(),
            type, // 'info', 'warning', 'error', 'success'
            message,
            data,
            timestamp: Date.now()
        };

        const notifications = [...this.state.ui.notifications, notification];
        
        // Garder seulement les 50 dernières notifications
        if (notifications.length > 50) {
            notifications.shift();
        }

        this.updateState('ui', { notifications });
        this.emit('notification', notification);

        console.log(`🔔 Notification [${type}]: ${message}`);
    }

    // ❌ AJOUTER UNE ERREUR
    addError(category, error, context = {}) {
        const errorEntry = {
            id: Date.now(),
            category,
            message: error.message || error,
            stack: error.stack,
            context,
            timestamp: Date.now()
        };

        const errors = [...this.state.ui.errors, errorEntry];
        
        // Garder seulement les 100 dernières erreurs
        if (errors.length > 100) {
            errors.shift();
        }

        this.updateState('ui', { errors });
        this.emit('error', errorEntry);

        console.error(`❌ Erreur [${category}]:`, error);
    }

    // 🧹 NETTOYER L'ÉTAT
    cleanup() {
        this.history = [];
        this.updateState('ui', {
            notifications: [],
            errors: []
        });
        
        console.log('🧹 État nettoyé');
    }

    // 📊 OBTENIR LES STATISTIQUES
    getStatistics() {
        return {
            uptime: Date.now() - this.state.app.startTime,
            totalStateUpdates: this.state.metrics.updateCount,
            historyEntries: this.history.length,
            notifications: this.state.ui.notifications.length,
            errors: this.state.ui.errors.length,
            successRate: this.calculateSuccessRate(),
            efficiency: this.calculateEfficiency(),
            memoryUsage: process.memoryUsage(),
            timestamp: Date.now()
        };
    }
}

module.exports = ElectronStateManager;
