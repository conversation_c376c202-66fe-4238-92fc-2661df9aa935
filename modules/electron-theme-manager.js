/**
 * 🎨 LOUNA AI ELECTRON - GESTIONNAIRE DE THÈMES AVANCÉ
 * Système de thèmes dynamiques pour l'application Electron
 */

const fs = require('fs');
const path = require('path');
const logger = require('./electron-logger');

class ElectronThemeManager {
    constructor(stateManager) {
        this.stateManager = stateManager;
        this.currentTheme = 'louna-default';
        this.themes = new Map();
        this.customThemes = new Map();
        this.themeHistory = [];
        this.maxHistoryLength = 10;
        
        // Répertoires des thèmes
        this.themesDir = path.join(__dirname, '../public/themes');
        this.customThemesDir = path.join(__dirname, '../data/custom-themes');
        
        logger.system('Gestionnaire de thèmes Electron initialisé');
        this.initializeThemes();
    }

    // 🔧 INITIALISER LES THÈMES
    async initializeThemes() {
        try {
            // Créer les répertoires si nécessaire
            this.ensureDirectories();
            
            // Charger les thèmes par défaut
            await this.loadDefaultThemes();
            
            // Charger les thèmes personnalisés
            await this.loadCustomThemes();
            
            // Appliquer le thème par défaut
            await this.applyTheme(this.currentTheme);
            
            logger.success('THEMES', `${this.themes.size} thèmes chargés`);
        } catch (error) {
            logger.error('THEMES', 'Erreur initialisation thèmes', { error: error.message });
        }
    }

    // 📁 CRÉER LES RÉPERTOIRES
    ensureDirectories() {
        [this.themesDir, this.customThemesDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug('THEMES', `Répertoire créé: ${dir}`);
            }
        });
    }

    // 🎨 CHARGER LES THÈMES PAR DÉFAUT
    async loadDefaultThemes() {
        const defaultThemes = {
            'louna-default': {
                name: 'LOUNA Default',
                description: 'Thème par défaut de LOUNA AI',
                author: 'LOUNA Team',
                version: '1.0.0',
                colors: {
                    primary: '#ff6b9d',
                    secondary: '#00d4aa',
                    background: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%)',
                    surface: 'rgba(255, 255, 255, 0.1)',
                    text: '#ffffff',
                    textSecondary: 'rgba(255, 255, 255, 0.8)',
                    accent: '#f39c12',
                    success: '#00ff00',
                    warning: '#f39c12',
                    error: '#e74c3c',
                    info: '#3498db'
                },
                fonts: {
                    primary: "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif",
                    mono: "'Fira Code', 'Consolas', monospace"
                },
                effects: {
                    blur: '15px',
                    borderRadius: '20px',
                    shadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
                    glowIntensity: 0.5
                },
                animations: {
                    duration: '300ms',
                    easing: 'ease-in-out',
                    enabled: true
                }
            },

            'louna-dark': {
                name: 'LOUNA Dark',
                description: 'Thème sombre élégant',
                author: 'LOUNA Team',
                version: '1.0.0',
                colors: {
                    primary: '#bb86fc',
                    secondary: '#03dac6',
                    background: 'linear-gradient(135deg, #121212 0%, #1e1e1e 50%, #2d2d2d 100%)',
                    surface: 'rgba(255, 255, 255, 0.05)',
                    text: '#ffffff',
                    textSecondary: 'rgba(255, 255, 255, 0.7)',
                    accent: '#cf6679',
                    success: '#4caf50',
                    warning: '#ff9800',
                    error: '#f44336',
                    info: '#2196f3'
                },
                fonts: {
                    primary: "'Roboto', sans-serif",
                    mono: "'JetBrains Mono', monospace"
                },
                effects: {
                    blur: '10px',
                    borderRadius: '12px',
                    shadow: '0 4px 20px rgba(0, 0, 0, 0.5)',
                    glowIntensity: 0.3
                },
                animations: {
                    duration: '250ms',
                    easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
                    enabled: true
                }
            },

            'louna-light': {
                name: 'LOUNA Light',
                description: 'Thème clair et moderne',
                author: 'LOUNA Team',
                version: '1.0.0',
                colors: {
                    primary: '#6200ea',
                    secondary: '#00bcd4',
                    background: 'linear-gradient(135deg, #f5f5f5 0%, #e8eaf6 50%, #c5cae9 100%)',
                    surface: 'rgba(255, 255, 255, 0.9)',
                    text: '#212121',
                    textSecondary: 'rgba(0, 0, 0, 0.6)',
                    accent: '#ff5722',
                    success: '#4caf50',
                    warning: '#ff9800',
                    error: '#f44336',
                    info: '#2196f3'
                },
                fonts: {
                    primary: "'Inter', sans-serif",
                    mono: "'Source Code Pro', monospace"
                },
                effects: {
                    blur: '8px',
                    borderRadius: '16px',
                    shadow: '0 2px 16px rgba(0, 0, 0, 0.1)',
                    glowIntensity: 0.2
                },
                animations: {
                    duration: '200ms',
                    easing: 'ease-out',
                    enabled: true
                }
            },

            'louna-neon': {
                name: 'LOUNA Neon',
                description: 'Thème cyberpunk néon',
                author: 'LOUNA Team',
                version: '1.0.0',
                colors: {
                    primary: '#00ffff',
                    secondary: '#ff00ff',
                    background: 'linear-gradient(135deg, #0a0a0a 0%, #1a0033 25%, #330066 50%, #660099 75%, #9900cc 100%)',
                    surface: 'rgba(0, 255, 255, 0.1)',
                    text: '#00ffff',
                    textSecondary: 'rgba(0, 255, 255, 0.8)',
                    accent: '#ff0080',
                    success: '#00ff00',
                    warning: '#ffff00',
                    error: '#ff0040',
                    info: '#0080ff'
                },
                fonts: {
                    primary: "'Orbitron', monospace",
                    mono: "'Share Tech Mono', monospace"
                },
                effects: {
                    blur: '20px',
                    borderRadius: '8px',
                    shadow: '0 0 30px rgba(0, 255, 255, 0.5)',
                    glowIntensity: 1.0
                },
                animations: {
                    duration: '400ms',
                    easing: 'ease-in-out',
                    enabled: true
                }
            }
        };

        for (const [id, theme] of Object.entries(defaultThemes)) {
            this.themes.set(id, {
                ...theme,
                id,
                type: 'default',
                createdAt: Date.now(),
                lastUsed: null
            });
        }

        logger.debug('THEMES', `${Object.keys(defaultThemes).length} thèmes par défaut chargés`);
    }

    // 📂 CHARGER LES THÈMES PERSONNALISÉS
    async loadCustomThemes() {
        try {
            if (!fs.existsSync(this.customThemesDir)) {
                return;
            }

            const files = fs.readdirSync(this.customThemesDir);
            const themeFiles = files.filter(file => file.endsWith('.json'));

            for (const file of themeFiles) {
                try {
                    const filePath = path.join(this.customThemesDir, file);
                    const themeData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
                    const themeId = path.basename(file, '.json');

                    this.customThemes.set(themeId, {
                        ...themeData,
                        id: themeId,
                        type: 'custom',
                        filePath
                    });

                    logger.debug('THEMES', `Thème personnalisé chargé: ${themeId}`);
                } catch (error) {
                    logger.warn('THEMES', `Erreur chargement thème ${file}`, { error: error.message });
                }
            }

            logger.debug('THEMES', `${this.customThemes.size} thèmes personnalisés chargés`);
        } catch (error) {
            logger.error('THEMES', 'Erreur chargement thèmes personnalisés', { error: error.message });
        }
    }

    // 🎨 APPLIQUER UN THÈME
    async applyTheme(themeId) {
        try {
            const theme = this.getTheme(themeId);
            if (!theme) {
                logger.warn('THEMES', `Thème non trouvé: ${themeId}`);
                return false;
            }

            // Générer le CSS du thème
            const css = this.generateThemeCSS(theme);

            // Sauvegarder le CSS dans un fichier temporaire
            const cssPath = path.join(this.themesDir, 'current-theme.css');
            fs.writeFileSync(cssPath, css);

            // Mettre à jour l'état
            this.currentTheme = themeId;
            theme.lastUsed = Date.now();

            // Ajouter à l'historique
            this.addToHistory(themeId);

            // Notifier l'interface
            if (this.stateManager) {
                this.stateManager.updateState('ui', {
                    currentTheme: themeId,
                    themeApplied: Date.now()
                });
            }

            logger.success('THEMES', `Thème appliqué: ${theme.name}`);
            return true;

        } catch (error) {
            logger.error('THEMES', `Erreur application thème ${themeId}`, { error: error.message });
            return false;
        }
    }

    // 🎨 GÉNÉRER LE CSS DU THÈME
    generateThemeCSS(theme) {
        const { colors, fonts, effects, animations } = theme;

        return `
/* 🎨 LOUNA AI - Thème: ${theme.name} */
:root {
    /* Couleurs */
    --color-primary: ${colors.primary};
    --color-secondary: ${colors.secondary};
    --color-background: ${colors.background};
    --color-surface: ${colors.surface};
    --color-text: ${colors.text};
    --color-text-secondary: ${colors.textSecondary};
    --color-accent: ${colors.accent};
    --color-success: ${colors.success};
    --color-warning: ${colors.warning};
    --color-error: ${colors.error};
    --color-info: ${colors.info};

    /* Polices */
    --font-primary: ${fonts.primary};
    --font-mono: ${fonts.mono};

    /* Effets */
    --effect-blur: ${effects.blur};
    --effect-border-radius: ${effects.borderRadius};
    --effect-shadow: ${effects.shadow};
    --effect-glow-intensity: ${effects.glowIntensity};

    /* Animations */
    --animation-duration: ${animations.duration};
    --animation-easing: ${animations.easing};
    --animations-enabled: ${animations.enabled ? '1' : '0'};
}

/* Application du thème */
body {
    background: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-primary);
    transition: all var(--animation-duration) var(--animation-easing);
}

.theme-surface {
    background: var(--color-surface);
    backdrop-filter: blur(var(--effect-blur));
    border-radius: var(--effect-border-radius);
    box-shadow: var(--effect-shadow);
}

.theme-primary {
    color: var(--color-primary);
}

.theme-secondary {
    color: var(--color-secondary);
}

.theme-accent {
    color: var(--color-accent);
}

.theme-glow {
    box-shadow: 0 0 20px var(--color-primary);
    filter: brightness(calc(1 + var(--effect-glow-intensity)));
}

/* Animations conditionnelles */
.theme-animated {
    transition: all var(--animation-duration) var(--animation-easing);
}

@media (prefers-reduced-motion: reduce) {
    :root {
        --animations-enabled: 0;
        --animation-duration: 0ms;
    }
}
`;
    }

    // 🔍 OBTENIR UN THÈME
    getTheme(themeId) {
        return this.themes.get(themeId) || this.customThemes.get(themeId);
    }

    // 📋 OBTENIR TOUS LES THÈMES
    getAllThemes() {
        const allThemes = [];
        
        // Thèmes par défaut
        for (const theme of this.themes.values()) {
            allThemes.push(theme);
        }
        
        // Thèmes personnalisés
        for (const theme of this.customThemes.values()) {
            allThemes.push(theme);
        }
        
        return allThemes.sort((a, b) => a.name.localeCompare(b.name));
    }

    // 💾 CRÉER UN THÈME PERSONNALISÉ
    async createCustomTheme(themeData) {
        try {
            const themeId = themeData.id || `custom-${Date.now()}`;
            const theme = {
                ...themeData,
                id: themeId,
                type: 'custom',
                createdAt: Date.now(),
                lastUsed: null
            };

            // Valider le thème
            if (!this.validateTheme(theme)) {
                throw new Error('Données de thème invalides');
            }

            // Sauvegarder le fichier
            const filePath = path.join(this.customThemesDir, `${themeId}.json`);
            fs.writeFileSync(filePath, JSON.stringify(theme, null, 2));

            // Ajouter à la collection
            theme.filePath = filePath;
            this.customThemes.set(themeId, theme);

            logger.success('THEMES', `Thème personnalisé créé: ${theme.name}`);
            return themeId;

        } catch (error) {
            logger.error('THEMES', 'Erreur création thème personnalisé', { error: error.message });
            throw error;
        }
    }

    // ✅ VALIDER UN THÈME
    validateTheme(theme) {
        const required = ['name', 'colors', 'fonts', 'effects', 'animations'];
        const colorRequired = ['primary', 'secondary', 'background', 'surface', 'text'];

        // Vérifier les propriétés requises
        for (const prop of required) {
            if (!theme[prop]) {
                logger.warn('THEMES', `Propriété manquante: ${prop}`);
                return false;
            }
        }

        // Vérifier les couleurs requises
        for (const color of colorRequired) {
            if (!theme.colors[color]) {
                logger.warn('THEMES', `Couleur manquante: ${color}`);
                return false;
            }
        }

        return true;
    }

    // 🗑️ SUPPRIMER UN THÈME PERSONNALISÉ
    async deleteCustomTheme(themeId) {
        try {
            const theme = this.customThemes.get(themeId);
            if (!theme) {
                throw new Error('Thème non trouvé');
            }

            if (theme.type !== 'custom') {
                throw new Error('Impossible de supprimer un thème par défaut');
            }

            // Supprimer le fichier
            if (theme.filePath && fs.existsSync(theme.filePath)) {
                fs.unlinkSync(theme.filePath);
            }

            // Supprimer de la collection
            this.customThemes.delete(themeId);

            // Si c'est le thème actuel, revenir au thème par défaut
            if (this.currentTheme === themeId) {
                await this.applyTheme('louna-default');
            }

            logger.success('THEMES', `Thème personnalisé supprimé: ${theme.name}`);
            return true;

        } catch (error) {
            logger.error('THEMES', `Erreur suppression thème ${themeId}`, { error: error.message });
            throw error;
        }
    }

    // 📚 GESTION DE L'HISTORIQUE
    addToHistory(themeId) {
        // Supprimer l'entrée existante
        const index = this.themeHistory.indexOf(themeId);
        if (index > -1) {
            this.themeHistory.splice(index, 1);
        }

        // Ajouter au début
        this.themeHistory.unshift(themeId);

        // Limiter la taille
        if (this.themeHistory.length > this.maxHistoryLength) {
            this.themeHistory = this.themeHistory.slice(0, this.maxHistoryLength);
        }
    }

    getThemeHistory() {
        return this.themeHistory.map(themeId => this.getTheme(themeId)).filter(Boolean);
    }

    // 🔄 THÈME SUIVANT/PRÉCÉDENT
    async nextTheme() {
        const themes = this.getAllThemes();
        const currentIndex = themes.findIndex(t => t.id === this.currentTheme);
        const nextIndex = (currentIndex + 1) % themes.length;
        return await this.applyTheme(themes[nextIndex].id);
    }

    async previousTheme() {
        const themes = this.getAllThemes();
        const currentIndex = themes.findIndex(t => t.id === this.currentTheme);
        const prevIndex = currentIndex === 0 ? themes.length - 1 : currentIndex - 1;
        return await this.applyTheme(themes[prevIndex].id);
    }

    // 📊 STATISTIQUES
    getThemeStatistics() {
        const stats = {
            total: this.themes.size + this.customThemes.size,
            default: this.themes.size,
            custom: this.customThemes.size,
            current: this.currentTheme,
            mostUsed: null,
            historyLength: this.themeHistory.length
        };

        // Trouver le thème le plus utilisé
        let maxUsage = 0;
        for (const theme of [...this.themes.values(), ...this.customThemes.values()]) {
            if (theme.lastUsed && theme.lastUsed > maxUsage) {
                maxUsage = theme.lastUsed;
                stats.mostUsed = theme;
            }
        }

        return stats;
    }

    // 🧹 NETTOYAGE
    cleanup() {
        // Sauvegarder la configuration actuelle
        const config = {
            currentTheme: this.currentTheme,
            history: this.themeHistory
        };

        const configPath = path.join(this.customThemesDir, 'theme-config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

        logger.info('THEMES', 'Configuration des thèmes sauvegardée');
    }
}

module.exports = ElectronThemeManager;
