/**
 * 🔄 LOUNA AI ELECTRON - SYSTÈME DE MISE À JOUR AUTOMATIQUE
 * Gestionnaire de mises à jour intelligentes pour l'application
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const crypto = require('crypto');
const { app, dialog } = require('electron');
const logger = require('./electron-logger');

class ElectronUpdaterSystem {
    constructor(stateManager, notificationSystem) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.currentVersion = app.getVersion();
        this.updateChannel = 'stable'; // stable, beta, alpha
        this.autoCheck = true;
        this.autoDownload = false;
        this.autoInstall = false;
        
        // Configuration des serveurs de mise à jour
        this.updateServers = {
            primary: 'https://updates.louna-ai.com',
            fallback: 'https://github.com/louna-ai/releases',
            local: 'http://localhost:3001/updates'
        };
        
        // État des mises à jour
        this.updateState = {
            checking: false,
            available: false,
            downloading: false,
            downloaded: false,
            installing: false,
            error: null,
            progress: 0,
            version: null,
            changelog: null,
            size: 0
        };

        // Répertoires
        this.updatesDir = path.join(__dirname, '../updates');
        this.backupDir = path.join(__dirname, '../backups');
        
        logger.system('Système de mise à jour Electron initialisé');
        this.initializeUpdater();
    }

    // 🔧 INITIALISER LE SYSTÈME DE MISE À JOUR
    async initializeUpdater() {
        try {
            // Créer les répertoires
            this.ensureDirectories();
            
            // Charger la configuration
            await this.loadConfiguration();
            
            // Programmer les vérifications automatiques
            if (this.autoCheck) {
                this.scheduleAutoCheck();
            }
            
            // Vérifier les mises à jour au démarrage
            setTimeout(() => {
                this.checkForUpdates();
            }, 30000); // Attendre 30 secondes après le démarrage
            
            logger.success('UPDATER', 'Système de mise à jour initialisé');
        } catch (error) {
            logger.error('UPDATER', 'Erreur initialisation système mise à jour', { error: error.message });
        }
    }

    // 📁 CRÉER LES RÉPERTOIRES
    ensureDirectories() {
        [this.updatesDir, this.backupDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug('UPDATER', `Répertoire créé: ${dir}`);
            }
        });
    }

    // ⚙️ CHARGER LA CONFIGURATION
    async loadConfiguration() {
        const configPath = path.join(__dirname, '../config/updater-config.json');
        
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                this.updateChannel = config.channel || 'stable';
                this.autoCheck = config.autoCheck !== false;
                this.autoDownload = config.autoDownload || false;
                this.autoInstall = config.autoInstall || false;
                
                logger.debug('UPDATER', 'Configuration chargée', config);
            } catch (error) {
                logger.warn('UPDATER', 'Erreur chargement configuration', { error: error.message });
            }
        }
    }

    // 📅 PROGRAMMER LES VÉRIFICATIONS AUTOMATIQUES
    scheduleAutoCheck() {
        // Vérifier toutes les 4 heures
        setInterval(() => {
            this.checkForUpdates();
        }, 4 * 60 * 60 * 1000);
        
        logger.debug('UPDATER', 'Vérifications automatiques programmées (toutes les 4h)');
    }

    // 🔍 VÉRIFIER LES MISES À JOUR
    async checkForUpdates(manual = false) {
        if (this.updateState.checking) {
            return this.updateState;
        }

        this.updateState.checking = true;
        this.updateState.error = null;
        
        try {
            logger.info('UPDATER', `Vérification des mises à jour (canal: ${this.updateChannel})`);
            
            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('updater', {
                    checking: true,
                    lastCheck: Date.now()
                });
            }

            // Vérifier sur le serveur principal
            const updateInfo = await this.fetchUpdateInfo();
            
            if (updateInfo && this.isNewerVersion(updateInfo.version)) {
                this.updateState.available = true;
                this.updateState.version = updateInfo.version;
                this.updateState.changelog = updateInfo.changelog;
                this.updateState.size = updateInfo.size;
                
                logger.success('UPDATER', `Mise à jour disponible: v${updateInfo.version}`);
                
                // Notification
                if (this.notificationSystem) {
                    this.notificationSystem.createNotification(
                        'info',
                        '🔄 Mise à jour disponible',
                        `Version ${updateInfo.version} disponible`,
                        {
                            priority: manual ? 'high' : 'normal',
                            persistent: true,
                            actions: [
                                { text: 'Télécharger', callback: () => this.downloadUpdate() },
                                { text: 'Plus tard', callback: () => {} }
                            ]
                        }
                    );
                }
                
                // Téléchargement automatique si activé
                if (this.autoDownload && !manual) {
                    await this.downloadUpdate();
                }
                
            } else {
                this.updateState.available = false;
                logger.info('UPDATER', 'Aucune mise à jour disponible');
                
                if (manual && this.notificationSystem) {
                    this.notificationSystem.createNotification(
                        'info',
                        '✅ Application à jour',
                        'Vous utilisez la dernière version',
                        { duration: 3000 }
                    );
                }
            }
            
        } catch (error) {
            this.updateState.error = error.message;
            logger.error('UPDATER', 'Erreur vérification mises à jour', { error: error.message });
            
            if (manual && this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'error',
                    '❌ Erreur de vérification',
                    'Impossible de vérifier les mises à jour',
                    { duration: 5000 }
                );
            }
        } finally {
            this.updateState.checking = false;
            
            if (this.stateManager) {
                this.stateManager.updateState('updater', {
                    checking: false,
                    available: this.updateState.available,
                    version: this.updateState.version,
                    error: this.updateState.error
                });
            }
        }
        
        return this.updateState;
    }

    // 📡 RÉCUPÉRER LES INFORMATIONS DE MISE À JOUR
    async fetchUpdateInfo() {
        const servers = [
            this.updateServers.primary,
            this.updateServers.fallback,
            this.updateServers.local
        ];

        for (const server of servers) {
            try {
                const updateInfo = await this.fetchFromServer(server);
                if (updateInfo) {
                    return updateInfo;
                }
            } catch (error) {
                logger.debug('UPDATER', `Serveur ${server} non disponible`, { error: error.message });
            }
        }

        throw new Error('Aucun serveur de mise à jour disponible');
    }

    // 🌐 RÉCUPÉRER DEPUIS UN SERVEUR
    async fetchFromServer(serverUrl) {
        return new Promise((resolve, reject) => {
            const url = `${serverUrl}/api/updates/check?version=${this.currentVersion}&channel=${this.updateChannel}&platform=${process.platform}`;
            
            const request = https.get(url, (response) => {
                let data = '';
                
                response.on('data', (chunk) => {
                    data += chunk;
                });
                
                response.on('end', () => {
                    try {
                        const updateInfo = JSON.parse(data);
                        resolve(updateInfo);
                    } catch (error) {
                        reject(new Error('Réponse serveur invalide'));
                    }
                });
            });
            
            request.on('error', (error) => {
                reject(error);
            });
            
            request.setTimeout(10000, () => {
                request.destroy();
                reject(new Error('Timeout serveur'));
            });
        });
    }

    // 🔢 COMPARER LES VERSIONS
    isNewerVersion(newVersion) {
        const current = this.currentVersion.split('.').map(Number);
        const newer = newVersion.split('.').map(Number);
        
        for (let i = 0; i < Math.max(current.length, newer.length); i++) {
            const currentPart = current[i] || 0;
            const newerPart = newer[i] || 0;
            
            if (newerPart > currentPart) return true;
            if (newerPart < currentPart) return false;
        }
        
        return false;
    }

    // 📥 TÉLÉCHARGER LA MISE À JOUR
    async downloadUpdate() {
        if (this.updateState.downloading || !this.updateState.available) {
            return;
        }

        this.updateState.downloading = true;
        this.updateState.progress = 0;
        
        try {
            logger.info('UPDATER', `Téléchargement de la mise à jour v${this.updateState.version}`);
            
            // Notification de début de téléchargement
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'info',
                    '📥 Téléchargement en cours',
                    `Mise à jour v${this.updateState.version}`,
                    { duration: 3000 }
                );
            }

            // Simuler le téléchargement (remplacer par vraie logique)
            await this.simulateDownload();
            
            this.updateState.downloaded = true;
            this.updateState.downloading = false;
            
            logger.success('UPDATER', 'Mise à jour téléchargée avec succès');
            
            // Notification de fin de téléchargement
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'success',
                    '✅ Téléchargement terminé',
                    'Mise à jour prête à installer',
                    {
                        persistent: true,
                        actions: [
                            { text: 'Installer maintenant', callback: () => this.installUpdate() },
                            { text: 'Installer au redémarrage', callback: () => this.scheduleInstallation() }
                        ]
                    }
                );
            }
            
            // Installation automatique si activée
            if (this.autoInstall) {
                await this.installUpdate();
            }
            
        } catch (error) {
            this.updateState.downloading = false;
            this.updateState.error = error.message;
            logger.error('UPDATER', 'Erreur téléchargement mise à jour', { error: error.message });
            
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'error',
                    '❌ Erreur de téléchargement',
                    'Impossible de télécharger la mise à jour',
                    { duration: 5000 }
                );
            }
        }
    }

    // 📥 SIMULER LE TÉLÉCHARGEMENT
    async simulateDownload() {
        return new Promise((resolve) => {
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 10;
                this.updateState.progress = Math.min(progress, 100);
                
                if (this.stateManager) {
                    this.stateManager.updateState('updater', {
                        downloading: true,
                        progress: this.updateState.progress
                    });
                }
                
                if (progress >= 100) {
                    clearInterval(interval);
                    resolve();
                }
            }, 200);
        });
    }

    // 🔧 INSTALLER LA MISE À JOUR
    async installUpdate() {
        if (this.updateState.installing || !this.updateState.downloaded) {
            return;
        }

        // Demander confirmation
        const response = await dialog.showMessageBox({
            type: 'question',
            buttons: ['Installer maintenant', 'Annuler'],
            defaultId: 0,
            title: 'Installation de la mise à jour',
            message: `Installer la mise à jour v${this.updateState.version} ?`,
            detail: 'L\'application sera redémarrée automatiquement.'
        });

        if (response.response !== 0) {
            return;
        }

        this.updateState.installing = true;
        
        try {
            logger.info('UPDATER', 'Installation de la mise à jour');
            
            // Notification d'installation
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'info',
                    '🔧 Installation en cours',
                    'Application en cours de mise à jour...',
                    { duration: 3000 }
                );
            }

            // Créer une sauvegarde
            await this.createBackup();
            
            // Simuler l'installation
            await this.simulateInstallation();
            
            logger.success('UPDATER', 'Mise à jour installée avec succès');
            
            // Redémarrer l'application
            app.relaunch();
            app.exit(0);
            
        } catch (error) {
            this.updateState.installing = false;
            this.updateState.error = error.message;
            logger.error('UPDATER', 'Erreur installation mise à jour', { error: error.message });
            
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'error',
                    '❌ Erreur d\'installation',
                    'Impossible d\'installer la mise à jour',
                    { duration: 5000 }
                );
            }
        }
    }

    // 📅 PROGRAMMER L'INSTALLATION
    scheduleInstallation() {
        // Marquer pour installation au prochain démarrage
        const flagPath = path.join(this.updatesDir, 'install-on-restart.flag');
        fs.writeFileSync(flagPath, JSON.stringify({
            version: this.updateState.version,
            timestamp: Date.now()
        }));
        
        logger.info('UPDATER', 'Installation programmée au prochain redémarrage');
        
        if (this.notificationSystem) {
            this.notificationSystem.createNotification(
                'info',
                '📅 Installation programmée',
                'La mise à jour sera installée au prochain démarrage',
                { duration: 3000 }
            );
        }
    }

    // 💾 CRÉER UNE SAUVEGARDE
    async createBackup() {
        const backupPath = path.join(this.backupDir, `backup-${this.currentVersion}-${Date.now()}`);
        
        // Logique de sauvegarde (simplifiée)
        fs.mkdirSync(backupPath, { recursive: true });
        
        logger.info('UPDATER', `Sauvegarde créée: ${backupPath}`);
    }

    // 🔧 SIMULER L'INSTALLATION
    async simulateInstallation() {
        return new Promise((resolve) => {
            setTimeout(resolve, 3000); // Simuler 3 secondes d'installation
        });
    }

    // ⚙️ CONFIGURER LE SYSTÈME
    configure(options) {
        if (options.channel) this.updateChannel = options.channel;
        if (options.autoCheck !== undefined) this.autoCheck = options.autoCheck;
        if (options.autoDownload !== undefined) this.autoDownload = options.autoDownload;
        if (options.autoInstall !== undefined) this.autoInstall = options.autoInstall;
        
        // Sauvegarder la configuration
        this.saveConfiguration();
        
        logger.info('UPDATER', 'Configuration mise à jour', options);
    }

    // 💾 SAUVEGARDER LA CONFIGURATION
    saveConfiguration() {
        const config = {
            channel: this.updateChannel,
            autoCheck: this.autoCheck,
            autoDownload: this.autoDownload,
            autoInstall: this.autoInstall
        };
        
        const configPath = path.join(__dirname, '../config/updater-config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }

    // 📊 OBTENIR L'ÉTAT
    getState() {
        return {
            ...this.updateState,
            currentVersion: this.currentVersion,
            channel: this.updateChannel,
            autoCheck: this.autoCheck,
            autoDownload: this.autoDownload,
            autoInstall: this.autoInstall
        };
    }

    // 📋 OBTENIR L'HISTORIQUE DES MISES À JOUR
    getUpdateHistory() {
        const historyPath = path.join(this.updatesDir, 'history.json');
        
        if (fs.existsSync(historyPath)) {
            try {
                return JSON.parse(fs.readFileSync(historyPath, 'utf8'));
            } catch (error) {
                logger.warn('UPDATER', 'Erreur lecture historique', { error: error.message });
            }
        }
        
        return [];
    }

    // 📝 AJOUTER À L'HISTORIQUE
    addToHistory(updateInfo) {
        const history = this.getUpdateHistory();
        history.unshift({
            ...updateInfo,
            installedAt: Date.now()
        });
        
        // Garder seulement les 10 dernières mises à jour
        const limitedHistory = history.slice(0, 10);
        
        const historyPath = path.join(this.updatesDir, 'history.json');
        fs.writeFileSync(historyPath, JSON.stringify(limitedHistory, null, 2));
    }

    // 🧹 NETTOYER LES FICHIERS TEMPORAIRES
    cleanup() {
        try {
            // Nettoyer les anciens téléchargements
            const files = fs.readdirSync(this.updatesDir);
            const now = Date.now();
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 jours
            
            for (const file of files) {
                const filePath = path.join(this.updatesDir, file);
                const stats = fs.statSync(filePath);
                
                if (now - stats.mtime.getTime() > maxAge) {
                    fs.unlinkSync(filePath);
                    logger.debug('UPDATER', `Fichier nettoyé: ${file}`);
                }
            }
            
            logger.info('UPDATER', 'Nettoyage des fichiers temporaires terminé');
        } catch (error) {
            logger.error('UPDATER', 'Erreur nettoyage', { error: error.message });
        }
    }
}

module.exports = ElectronUpdaterSystem;
