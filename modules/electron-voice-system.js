/**
 * 🎤 LOUNA AI ELECTRON - SYSTÈME VOCAL AVANCÉ
 * Reconnaissance vocale et synthèse vocale intelligente
 */

const fs = require('fs');
const path = require('path');
const { app } = require('electron');
const logger = require('./electron-logger');

class ElectronVoiceSystem {
    constructor(stateManager, notificationSystem, deepSeekManager) {
        this.stateManager = stateManager;
        this.notificationSystem = notificationSystem;
        this.deepSeekManager = deepSeekManager;
        this.isEnabled = false;
        this.isListening = false;
        this.isSpeaking = false;
        
        // Configuration vocale
        this.voiceConfig = {
            recognition: {
                enabled: true,
                language: 'fr-FR',
                continuous: true,
                interimResults: true,
                maxAlternatives: 3,
                confidence: 0.7,
                hotwords: ['louna', 'hey louna', 'ok louna'],
                commands: {
                    'arrêt urgence': 'emergency-stop',
                    'déconnexion': 'disconnect',
                    'statut': 'status',
                    'aide': 'help',
                    'mode silencieux': 'mute',
                    'réveille toi': 'wake-up'
                }
            },
            synthesis: {
                enabled: true,
                voice: 'female-fr',
                rate: 0.8,
                pitch: 1.1,
                volume: 0.7,
                language: 'fr-FR',
                autoSpeak: false,
                speakNotifications: false,
                speakResponses: true
            },
            audio: {
                inputDevice: 'default',
                outputDevice: 'default',
                noiseReduction: true,
                echoCancellation: true,
                autoGainControl: true,
                sampleRate: 16000,
                channels: 1
            }
        };
        
        // État vocal
        this.voiceState = {
            recognition: {
                active: false,
                listening: false,
                lastResult: null,
                confidence: 0,
                alternatives: [],
                errors: []
            },
            synthesis: {
                active: false,
                speaking: false,
                queue: [],
                currentText: null,
                voices: [],
                selectedVoice: null
            },
            audio: {
                inputLevel: 0,
                outputLevel: 0,
                devices: {
                    input: [],
                    output: []
                }
            }
        };
        
        // Commandes vocales
        this.voiceCommands = new Map();
        this.setupDefaultCommands();
        
        // Répertoires
        this.voiceDir = path.join(__dirname, '../data/voice');
        this.recordingsDir = path.join(this.voiceDir, 'recordings');
        
        logger.system('Système vocal Electron initialisé');
        this.initializeVoiceSystem();
    }

    // 🔧 INITIALISER LE SYSTÈME VOCAL
    async initializeVoiceSystem() {
        try {
            // Créer les répertoires
            this.ensureDirectories();
            
            // Charger la configuration
            await this.loadConfiguration();
            
            // Initialiser les composants vocaux
            await this.initializeComponents();
            
            logger.success('VOICE', 'Système vocal initialisé');
        } catch (error) {
            logger.error('VOICE', 'Erreur initialisation système vocal', { error: error.message });
        }
    }

    // 📁 CRÉER LES RÉPERTOIRES
    ensureDirectories() {
        [this.voiceDir, this.recordingsDir].forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
                logger.debug('VOICE', `Répertoire créé: ${dir}`);
            }
        });
    }

    // ⚙️ CHARGER LA CONFIGURATION
    async loadConfiguration() {
        const configPath = path.join(__dirname, '../config/voice-config.json');
        
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                this.voiceConfig = { ...this.voiceConfig, ...config };
                this.isEnabled = config.enabled || false;
                
                logger.debug('VOICE', 'Configuration vocale chargée', { enabled: this.isEnabled });
            } catch (error) {
                logger.warn('VOICE', 'Erreur chargement configuration', { error: error.message });
            }
        }
    }

    // 🎯 INITIALISER LES COMPOSANTS
    async initializeComponents() {
        if (!this.isEnabled) {
            return;
        }

        try {
            // Initialiser la reconnaissance vocale
            await this.initializeSpeechRecognition();
            
            // Initialiser la synthèse vocale
            await this.initializeSpeechSynthesis();
            
            // Initialiser l'audio
            await this.initializeAudio();
            
            logger.success('VOICE', 'Composants vocaux initialisés');
        } catch (error) {
            logger.error('VOICE', 'Erreur initialisation composants', { error: error.message });
        }
    }

    // 🎤 INITIALISER LA RECONNAISSANCE VOCALE
    async initializeSpeechRecognition() {
        // Note: La reconnaissance vocale sera gérée côté renderer
        // Ici on prépare la configuration et les handlers
        
        this.voiceState.recognition.active = true;
        logger.success('VOICE', 'Reconnaissance vocale configurée');
    }

    // 🔊 INITIALISER LA SYNTHÈSE VOCALE
    async initializeSpeechSynthesis() {
        // Note: La synthèse vocale sera gérée côté renderer
        // Ici on prépare la configuration et les handlers
        
        this.voiceState.synthesis.active = true;
        logger.success('VOICE', 'Synthèse vocale configurée');
    }

    // 🎵 INITIALISER L'AUDIO
    async initializeAudio() {
        // Configuration audio pour Electron
        this.voiceState.audio.devices.input = ['Microphone par défaut'];
        this.voiceState.audio.devices.output = ['Haut-parleurs par défaut'];
        
        logger.success('VOICE', 'Système audio configuré');
    }

    // 🎯 CONFIGURER LES COMMANDES PAR DÉFAUT
    setupDefaultCommands() {
        const commands = {
            // Commandes système
            'arrêt urgence': () => this.executeEmergencyStop(),
            'déconnexion deepseek': () => this.executeDisconnect(),
            'statut système': () => this.executeStatusCheck(),
            'aide vocale': () => this.executeHelp(),
            
            // Commandes navigation
            'aller accueil': () => this.executeNavigation('home'),
            'ouvrir chat': () => this.executeNavigation('chat'),
            'ouvrir cerveau': () => this.executeNavigation('brain'),
            'ouvrir plugins': () => this.executeNavigation('plugins'),
            
            // Commandes contrôle
            'mode silencieux': () => this.toggleMute(),
            'arrêter écoute': () => this.stopListening(),
            'commencer écoute': () => this.startListening(),
            'répéter': () => this.repeatLastResponse(),
            
            // Commandes IA
            'nouvelle conversation': () => this.startNewConversation(),
            'sauvegarder mémoire': () => this.saveMemory(),
            'optimiser performance': () => this.optimizePerformance()
        };

        for (const [phrase, handler] of Object.entries(commands)) {
            this.voiceCommands.set(phrase.toLowerCase(), handler);
        }

        logger.debug('VOICE', `${this.voiceCommands.size} commandes vocales configurées`);
    }

    // 🎤 DÉMARRER L'ÉCOUTE
    async startListening() {
        if (!this.isEnabled || this.isListening) {
            return;
        }

        try {
            this.isListening = true;
            this.voiceState.recognition.listening = true;
            
            // Notification
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'info',
                    '🎤 Écoute Activée',
                    'LOUNA AI vous écoute...',
                    { duration: 2000 }
                );
            }
            
            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('voice', {
                    listening: true,
                    lastAction: 'start-listening'
                });
            }
            
            logger.info('VOICE', 'Écoute vocale démarrée');
            
        } catch (error) {
            logger.error('VOICE', 'Erreur démarrage écoute', { error: error.message });
        }
    }

    // 🔇 ARRÊTER L'ÉCOUTE
    async stopListening() {
        if (!this.isListening) {
            return;
        }

        try {
            this.isListening = false;
            this.voiceState.recognition.listening = false;
            
            // Notification
            if (this.notificationSystem) {
                this.notificationSystem.createNotification(
                    'info',
                    '🔇 Écoute Désactivée',
                    'LOUNA AI ne vous écoute plus',
                    { duration: 2000 }
                );
            }
            
            // Mettre à jour l'état
            if (this.stateManager) {
                this.stateManager.updateState('voice', {
                    listening: false,
                    lastAction: 'stop-listening'
                });
            }
            
            logger.info('VOICE', 'Écoute vocale arrêtée');
            
        } catch (error) {
            logger.error('VOICE', 'Erreur arrêt écoute', { error: error.message });
        }
    }

    // 🗣️ PARLER
    async speak(text, options = {}) {
        if (!this.isEnabled || !this.voiceConfig.synthesis.enabled) {
            return;
        }

        try {
            const speakOptions = {
                ...this.voiceConfig.synthesis,
                ...options
            };

            // Ajouter à la queue
            this.voiceState.synthesis.queue.push({
                text,
                options: speakOptions,
                timestamp: Date.now()
            });

            // Traiter la queue si pas déjà en cours
            if (!this.isSpeaking) {
                await this.processSpeechQueue();
            }
            
        } catch (error) {
            logger.error('VOICE', 'Erreur synthèse vocale', { error: error.message });
        }
    }

    // 📢 TRAITER LA QUEUE DE PAROLE
    async processSpeechQueue() {
        if (this.isSpeaking || this.voiceState.synthesis.queue.length === 0) {
            return;
        }

        this.isSpeaking = true;
        this.voiceState.synthesis.speaking = true;

        while (this.voiceState.synthesis.queue.length > 0) {
            const item = this.voiceState.synthesis.queue.shift();
            
            try {
                this.voiceState.synthesis.currentText = item.text;
                
                // Ici, la synthèse vocale sera gérée côté renderer
                // On simule le processus
                await this.simulateSpeech(item.text, item.options);
                
                logger.debug('VOICE', `Texte prononcé: ${item.text.substring(0, 50)}...`);
                
            } catch (error) {
                logger.error('VOICE', 'Erreur prononciation', { error: error.message });
            }
        }

        this.isSpeaking = false;
        this.voiceState.synthesis.speaking = false;
        this.voiceState.synthesis.currentText = null;
    }

    // 🎭 SIMULER LA PAROLE
    async simulateSpeech(text, options) {
        // Calculer la durée approximative basée sur la longueur du texte
        const wordsPerMinute = 150;
        const words = text.split(' ').length;
        const duration = (words / wordsPerMinute) * 60 * 1000 / options.rate;
        
        return new Promise(resolve => {
            setTimeout(resolve, Math.max(1000, duration));
        });
    }

    // 🎯 TRAITER LA RECONNAISSANCE VOCALE
    async processVoiceInput(transcript, confidence) {
        if (!this.isEnabled || !transcript) {
            return;
        }

        try {
            const normalizedTranscript = transcript.toLowerCase().trim();
            
            // Mettre à jour l'état
            this.voiceState.recognition.lastResult = transcript;
            this.voiceState.recognition.confidence = confidence;
            
            logger.debug('VOICE', `Reconnaissance: "${transcript}" (${Math.round(confidence * 100)}%)`);

            // Vérifier les mots-clés d'activation
            const hasHotword = this.voiceConfig.recognition.hotwords.some(hotword => 
                normalizedTranscript.includes(hotword.toLowerCase())
            );

            if (hasHotword || this.isListening) {
                // Chercher une commande correspondante
                const command = this.findMatchingCommand(normalizedTranscript);
                
                if (command) {
                    await this.executeVoiceCommand(command, transcript);
                } else {
                    // Envoyer à DeepSeek pour traitement
                    await this.processWithDeepSeek(transcript);
                }
            }
            
        } catch (error) {
            logger.error('VOICE', 'Erreur traitement vocal', { error: error.message });
        }
    }

    // 🔍 TROUVER UNE COMMANDE CORRESPONDANTE
    findMatchingCommand(transcript) {
        for (const [phrase, handler] of this.voiceCommands) {
            if (transcript.includes(phrase)) {
                return { phrase, handler };
            }
        }
        return null;
    }

    // ⚡ EXÉCUTER UNE COMMANDE VOCALE
    async executeVoiceCommand(command, originalTranscript) {
        try {
            logger.info('VOICE', `Exécution commande: ${command.phrase}`);
            
            await command.handler();
            
            // Confirmation vocale
            await this.speak(`Commande "${command.phrase}" exécutée`);
            
        } catch (error) {
            logger.error('VOICE', `Erreur commande ${command.phrase}`, { error: error.message });
            await this.speak('Erreur lors de l\'exécution de la commande');
        }
    }

    // 🤖 TRAITER AVEC DEEPSEEK
    async processWithDeepSeek(transcript) {
        if (!this.deepSeekManager) {
            await this.speak('DeepSeek n\'est pas disponible');
            return;
        }

        try {
            // Ajouter un contexte vocal
            const voicePrompt = `[Commande vocale] ${transcript}`;
            
            const response = await this.deepSeekManager.chat(voicePrompt);
            
            if (response.success && this.voiceConfig.synthesis.speakResponses) {
                // Extraire la réponse et la prononcer
                const responseText = response.response.replace(/<think>.*?<\/think>/gs, '').trim();
                await this.speak(responseText);
            }
            
        } catch (error) {
            logger.error('VOICE', 'Erreur traitement DeepSeek', { error: error.message });
            await this.speak('Erreur de traitement de votre demande');
        }
    }

    // 🚨 COMMANDES SYSTÈME
    async executeEmergencyStop() {
        logger.warn('VOICE', 'Arrêt d\'urgence vocal déclenché');
        await this.speak('Arrêt d\'urgence activé');
        
        // Déclencher l'arrêt d'urgence
        if (this.deepSeekManager) {
            await this.deepSeekManager.emergencyStop();
        }
    }

    async executeDisconnect() {
        await this.speak('Déconnexion de DeepSeek');
        
        if (this.deepSeekManager) {
            await this.deepSeekManager.disconnect();
        }
    }

    async executeStatusCheck() {
        const status = this.getSystemStatus();
        await this.speak(`Statut système: ${status}`);
    }

    async executeHelp() {
        const helpText = 'Commandes disponibles: arrêt urgence, déconnexion, statut, navigation, contrôle vocal';
        await this.speak(helpText);
    }

    async executeNavigation(page) {
        await this.speak(`Navigation vers ${page}`);
        // Logique de navigation
    }

    async toggleMute() {
        this.voiceConfig.synthesis.enabled = !this.voiceConfig.synthesis.enabled;
        const status = this.voiceConfig.synthesis.enabled ? 'activée' : 'désactivée';
        
        if (this.voiceConfig.synthesis.enabled) {
            await this.speak(`Voix ${status}`);
        }
    }

    async repeatLastResponse() {
        if (this.voiceState.synthesis.currentText) {
            await this.speak(this.voiceState.synthesis.currentText);
        } else {
            await this.speak('Aucune réponse à répéter');
        }
    }

    async startNewConversation() {
        await this.speak('Nouvelle conversation démarrée');
        // Logique de nouvelle conversation
    }

    async saveMemory() {
        await this.speak('Sauvegarde de la mémoire en cours');
        // Logique de sauvegarde
    }

    async optimizePerformance() {
        await this.speak('Optimisation des performances en cours');
        // Logique d'optimisation
    }

    // 📊 OBTENIR LE STATUT SYSTÈME
    getSystemStatus() {
        if (this.stateManager) {
            const state = this.stateManager.getState('app');
            return state.ready ? 'opérationnel' : 'en cours d\'initialisation';
        }
        return 'inconnu';
    }

    // ⚙️ CONFIGURER LE SYSTÈME VOCAL
    configure(config) {
        this.voiceConfig = { ...this.voiceConfig, ...config };
        this.isEnabled = config.enabled !== undefined ? config.enabled : this.isEnabled;
        
        // Sauvegarder la configuration
        this.saveConfiguration();
        
        logger.info('VOICE', 'Configuration vocale mise à jour', config);
    }

    // 💾 SAUVEGARDER LA CONFIGURATION
    saveConfiguration() {
        const config = {
            ...this.voiceConfig,
            enabled: this.isEnabled
        };
        
        const configPath = path.join(__dirname, '../config/voice-config.json');
        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    }

    // 📊 OBTENIR L'ÉTAT
    getState() {
        return {
            enabled: this.isEnabled,
            listening: this.isListening,
            speaking: this.isSpeaking,
            recognition: this.voiceState.recognition,
            synthesis: this.voiceState.synthesis,
            audio: this.voiceState.audio,
            config: this.voiceConfig,
            commands: Array.from(this.voiceCommands.keys())
        };
    }

    // 🧹 NETTOYAGE
    cleanup() {
        this.stopListening();
        this.voiceState.synthesis.queue = [];
        
        logger.info('VOICE', 'Système vocal nettoyé');
    }
}

module.exports = ElectronVoiceSystem;
