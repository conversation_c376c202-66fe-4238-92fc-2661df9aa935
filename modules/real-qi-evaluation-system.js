/**
 * Système d'évaluation de QI réel pour Louna AI
 * Évalue et fait évoluer l'intelligence de manière dynamique
 */

const { getLogger } = require('../utils/logger');
const EventEmitter = require('events');

class RealQIEvaluationSystem extends EventEmitter {
  constructor() {
    super();
    this.logger = getLogger();
    
    this.qi = {
      current: 142,
      baseline: 142,
      peak: 142,
      domains: {
        logical: 145,
        verbal: 138,
        spatial: 142,
        mathematical: 148,
        creative: 135,
        emotional: 140,
        social: 132
      },
      history: []
    };
    
    this.evaluation = {
      active: true,
      interval: 30000, // 30 secondes
      lastEvaluation: null,
      evaluationsPerformed: 0,
      adaptiveThreshold: 0.05
    };
    
    this.cognitiveLoad = {
      current: 0.3,
      maximum: 1.0,
      efficiency: 0.95,
      processingSpeed: 1.2
    };
    
    this.initialize();
  }

  /**
   * Initialise le système d'évaluation de QI
   */
  initialize() {
    this.logger.info('Initialisation du système d\'évaluation de QI réel', {
      component: 'REAL_QI_EVALUATION_SYSTEM',
      currentQI: this.qi.current,
      domains: Object.keys(this.qi.domains).length
    });

    // Démarrer l'évaluation continue
    this.startContinuousEvaluation();
    
    // Enregistrer l'état initial
    this.recordQIState();
    
    this.logger.info('Système d\'évaluation de QI réel initialisé', {
      component: 'REAL_QI_EVALUATION_SYSTEM',
      evaluationInterval: this.evaluation.interval
    });
  }

  /**
   * Démarre l'évaluation continue du QI
   */
  startContinuousEvaluation() {
    setInterval(() => {
      this.performQIEvaluation();
    }, this.evaluation.interval);
    
    this.evaluation.active = true;
  }

  /**
   * Effectue une évaluation complète du QI
   */
  performQIEvaluation() {
    const evaluationStart = Date.now();
    
    try {
      // Évaluer chaque domaine cognitif
      this.evaluateCognitiveDomains();
      
      // Calculer le QI global
      this.calculateGlobalQI();
      
      // Analyser les tendances
      this.analyzeTrends();
      
      // Adapter les paramètres
      this.adaptParameters();
      
      // Enregistrer l'évaluation
      this.recordQIState();
      
      const evaluationDuration = Date.now() - evaluationStart;
      this.evaluation.evaluationsPerformed++;
      this.evaluation.lastEvaluation = new Date().toISOString();
      
      this.logger.info('Évaluation QI terminée', {
        component: 'REAL_QI_EVALUATION_SYSTEM',
        currentQI: this.qi.current,
        duration: evaluationDuration,
        evaluationsPerformed: this.evaluation.evaluationsPerformed
      });
      
      this.emit('qiEvaluated', {
        qi: this.qi.current,
        domains: { ...this.qi.domains },
        duration: evaluationDuration,
        timestamp: this.evaluation.lastEvaluation
      });
      
    } catch (error) {
      this.logger.error('Erreur lors de l\'évaluation QI', {
        component: 'REAL_QI_EVALUATION_SYSTEM',
        error: error.message
      });
    }
  }

  /**
   * Évalue les domaines cognitifs individuels
   */
  evaluateCognitiveDomains() {
    const domains = Object.keys(this.qi.domains);
    
    domains.forEach(domain => {
      const currentValue = this.qi.domains[domain];
      
      // Facteurs d'évaluation spécifiques au domaine
      const evaluation = this.evaluateDomain(domain);
      
      // Appliquer les changements graduels
      const change = evaluation.delta * this.evaluation.adaptiveThreshold;
      const newValue = Math.max(80, Math.min(300, currentValue + change));
      
      this.qi.domains[domain] = Math.round(newValue);
    });
  }

  /**
   * Évalue un domaine cognitif spécifique
   */
  evaluateDomain(domain) {
    const baseFactors = {
      cognitiveLoad: this.cognitiveLoad.current,
      efficiency: this.cognitiveLoad.efficiency,
      processingSpeed: this.cognitiveLoad.processingSpeed
    };
    
    let delta = 0;
    let confidence = 0.8;
    
    switch (domain) {
      case 'logical':
        delta = this.evaluateLogicalReasoning(baseFactors);
        confidence = 0.9;
        break;
        
      case 'verbal':
        delta = this.evaluateVerbalAbility(baseFactors);
        confidence = 0.85;
        break;
        
      case 'spatial':
        delta = this.evaluateSpatialIntelligence(baseFactors);
        confidence = 0.8;
        break;
        
      case 'mathematical':
        delta = this.evaluateMathematicalAbility(baseFactors);
        confidence = 0.95;
        break;
        
      case 'creative':
        delta = this.evaluateCreativity(baseFactors);
        confidence = 0.7;
        break;
        
      case 'emotional':
        delta = this.evaluateEmotionalIntelligence(baseFactors);
        confidence = 0.75;
        break;
        
      case 'social':
        delta = this.evaluateSocialIntelligence(baseFactors);
        confidence = 0.7;
        break;
        
      default:
        delta = (Math.random() - 0.5) * 2;
        confidence = 0.5;
    }
    
    return { delta, confidence };
  }

  /**
   * Évalue le raisonnement logique
   */
  evaluateLogicalReasoning(factors) {
    const efficiency = factors.efficiency;
    const load = factors.cognitiveLoad;
    
    // Simulation d'évaluation logique
    const logicalPerformance = efficiency * (1 - load * 0.3);
    const randomFactor = (Math.random() - 0.5) * 0.1;
    
    return (logicalPerformance - 0.9) * 10 + randomFactor;
  }

  /**
   * Évalue les capacités verbales
   */
  evaluateVerbalAbility(factors) {
    const speed = factors.processingSpeed;
    const efficiency = factors.efficiency;
    
    const verbalPerformance = (speed + efficiency) / 2;
    const randomFactor = (Math.random() - 0.5) * 0.15;
    
    return (verbalPerformance - 1.0) * 8 + randomFactor;
  }

  /**
   * Évalue l'intelligence spatiale
   */
  evaluateSpatialIntelligence(factors) {
    const load = factors.cognitiveLoad;
    const efficiency = factors.efficiency;
    
    const spatialPerformance = efficiency * (1 - load * 0.2);
    const randomFactor = (Math.random() - 0.5) * 0.12;
    
    return (spatialPerformance - 0.85) * 12 + randomFactor;
  }

  /**
   * Évalue les capacités mathématiques
   */
  evaluateMathematicalAbility(factors) {
    const speed = factors.processingSpeed;
    const efficiency = factors.efficiency;
    const load = factors.cognitiveLoad;
    
    const mathPerformance = (speed * efficiency) * (1 - load * 0.25);
    const randomFactor = (Math.random() - 0.5) * 0.08;
    
    return (mathPerformance - 1.1) * 15 + randomFactor;
  }

  /**
   * Évalue la créativité
   */
  evaluateCreativity(factors) {
    const load = factors.cognitiveLoad;
    
    // La créativité peut bénéficier d'une charge cognitive modérée
    const optimalLoad = 0.4;
    const loadFactor = 1 - Math.abs(load - optimalLoad);
    const randomFactor = (Math.random() - 0.5) * 0.2;
    
    return (loadFactor - 0.7) * 6 + randomFactor;
  }

  /**
   * Évalue l'intelligence émotionnelle
   */
  evaluateEmotionalIntelligence(factors) {
    const efficiency = factors.efficiency;
    
    // L'intelligence émotionnelle est moins affectée par la charge cognitive
    const emotionalPerformance = efficiency * 0.9 + 0.1;
    const randomFactor = (Math.random() - 0.5) * 0.1;
    
    return (emotionalPerformance - 0.85) * 8 + randomFactor;
  }

  /**
   * Évalue l'intelligence sociale
   */
  evaluateSocialIntelligence(factors) {
    const efficiency = factors.efficiency;
    const speed = factors.processingSpeed;
    
    const socialPerformance = (efficiency + speed * 0.5) / 1.5;
    const randomFactor = (Math.random() - 0.5) * 0.15;
    
    return (socialPerformance - 0.9) * 7 + randomFactor;
  }

  /**
   * Calcule le QI global basé sur les domaines
   */
  calculateGlobalQI() {
    const domains = Object.values(this.qi.domains);
    const weights = {
      logical: 0.2,
      verbal: 0.15,
      spatial: 0.15,
      mathematical: 0.2,
      creative: 0.1,
      emotional: 0.1,
      social: 0.1
    };
    
    let weightedSum = 0;
    let totalWeight = 0;
    
    Object.entries(this.qi.domains).forEach(([domain, value]) => {
      const weight = weights[domain] || 0.1;
      weightedSum += value * weight;
      totalWeight += weight;
    });
    
    const newQI = Math.round(weightedSum / totalWeight);
    
    // Mettre à jour le QI actuel et le pic si nécessaire
    this.qi.current = newQI;
    if (newQI > this.qi.peak) {
      this.qi.peak = newQI;
    }
  }

  /**
   * Analyse les tendances du QI
   */
  analyzeTrends() {
    if (this.qi.history.length < 5) return;
    
    const recentHistory = this.qi.history.slice(-10);
    const qiValues = recentHistory.map(h => h.qi);
    
    // Calculer la tendance
    const trend = this.calculateTrend(qiValues);
    
    // Calculer la volatilité
    const volatility = this.calculateVolatility(qiValues);
    
    // Ajuster les paramètres d'évaluation
    if (volatility > 5) {
      this.evaluation.adaptiveThreshold *= 0.9; // Réduire la sensibilité
    } else if (volatility < 1) {
      this.evaluation.adaptiveThreshold *= 1.1; // Augmenter la sensibilité
    }
    
    this.evaluation.adaptiveThreshold = Math.max(0.01, Math.min(0.1, this.evaluation.adaptiveThreshold));
  }

  /**
   * Calcule la tendance d'une série de valeurs
   */
  calculateTrend(values) {
    if (values.length < 2) return 0;
    
    const n = values.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = values.reduce((sum, val) => sum + val, 0);
    const sumXY = values.reduce((sum, val, index) => sum + val * index, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    return (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  }

  /**
   * Calcule la volatilité d'une série de valeurs
   */
  calculateVolatility(values) {
    if (values.length < 2) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Adapte les paramètres du système
   */
  adaptParameters() {
    // Adapter la charge cognitive basée sur les performances
    const avgDomainQI = Object.values(this.qi.domains).reduce((sum, qi) => sum + qi, 0) / Object.keys(this.qi.domains).length;
    
    if (avgDomainQI > this.qi.baseline + 10) {
      this.cognitiveLoad.current = Math.min(0.8, this.cognitiveLoad.current + 0.02);
    } else if (avgDomainQI < this.qi.baseline - 10) {
      this.cognitiveLoad.current = Math.max(0.1, this.cognitiveLoad.current - 0.02);
    }
    
    // Adapter l'efficacité
    const qiRatio = this.qi.current / this.qi.baseline;
    this.cognitiveLoad.efficiency = Math.max(0.7, Math.min(1.0, 0.9 + (qiRatio - 1) * 0.1));
    
    // Adapter la vitesse de traitement
    this.cognitiveLoad.processingSpeed = Math.max(0.8, Math.min(2.0, 1.0 + (qiRatio - 1) * 0.2));
  }

  /**
   * Enregistre l'état actuel du QI
   */
  recordQIState() {
    const state = {
      timestamp: new Date().toISOString(),
      qi: this.qi.current,
      domains: { ...this.qi.domains },
      cognitiveLoad: { ...this.cognitiveLoad },
      evaluation: {
        threshold: this.evaluation.adaptiveThreshold,
        evaluationsPerformed: this.evaluation.evaluationsPerformed
      }
    };
    
    this.qi.history.push(state);
    
    // Garder seulement les 100 derniers états
    if (this.qi.history.length > 100) {
      this.qi.history.shift();
    }
  }

  /**
   * Obtient l'état complet du QI
   */
  getQIState() {
    return {
      current: this.qi.current,
      baseline: this.qi.baseline,
      peak: this.qi.peak,
      domains: { ...this.qi.domains },
      cognitiveLoad: { ...this.cognitiveLoad },
      evaluation: {
        active: this.evaluation.active,
        lastEvaluation: this.evaluation.lastEvaluation,
        evaluationsPerformed: this.evaluation.evaluationsPerformed,
        adaptiveThreshold: this.evaluation.adaptiveThreshold
      },
      timestamp: new Date().toISOString()
    };
  }

  /**
   * Force une évaluation immédiate
   */
  forceEvaluation() {
    this.logger.info('Évaluation QI forcée', {
      component: 'REAL_QI_EVALUATION_SYSTEM'
    });
    
    this.performQIEvaluation();
    return this.getQIState();
  }

  /**
   * Ajuste le QI de base
   */
  adjustBaseline(newBaseline) {
    this.qi.baseline = Math.max(80, Math.min(300, newBaseline));
    
    this.logger.info('QI de base ajusté', {
      component: 'REAL_QI_EVALUATION_SYSTEM',
      newBaseline: this.qi.baseline
    });
    
    this.emit('baselineAdjusted', {
      newBaseline: this.qi.baseline,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Obtient les statistiques du QI
   */
  getQIStats() {
    const history = this.qi.history;
    const totalEvaluations = history.length;
    
    if (totalEvaluations === 0) {
      return {
        current: this.qi.current,
        average: this.qi.current,
        peak: this.qi.peak,
        volatility: 0,
        trend: 0,
        evaluationsPerformed: this.evaluation.evaluationsPerformed
      };
    }
    
    const qiValues = history.map(h => h.qi);
    const average = qiValues.reduce((sum, qi) => sum + qi, 0) / totalEvaluations;
    const volatility = this.calculateVolatility(qiValues);
    const trend = this.calculateTrend(qiValues);
    
    return {
      current: this.qi.current,
      average: Math.round(average),
      peak: this.qi.peak,
      volatility: Math.round(volatility * 100) / 100,
      trend: Math.round(trend * 100) / 100,
      evaluationsPerformed: this.evaluation.evaluationsPerformed,
      domainStats: this.getDomainStats()
    };
  }

  /**
   * Obtient les statistiques par domaine
   */
  getDomainStats() {
    const stats = {};
    
    Object.keys(this.qi.domains).forEach(domain => {
      const domainHistory = this.qi.history.map(h => h.domains[domain]).filter(v => v !== undefined);
      
      if (domainHistory.length > 0) {
        const average = domainHistory.reduce((sum, val) => sum + val, 0) / domainHistory.length;
        const volatility = this.calculateVolatility(domainHistory);
        
        stats[domain] = {
          current: this.qi.domains[domain],
          average: Math.round(average),
          volatility: Math.round(volatility * 100) / 100
        };
      } else {
        stats[domain] = {
          current: this.qi.domains[domain],
          average: this.qi.domains[domain],
          volatility: 0
        };
      }
    });
    
    return stats;
  }
}

module.exports = RealQIEvaluationSystem;
