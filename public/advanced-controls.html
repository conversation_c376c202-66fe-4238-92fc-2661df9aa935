<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ LOUNA AI - Contrôles Avancés</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .control-panel:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .panel-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff6b9d;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .panel-icon {
            font-size: 1.2em;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #00d4aa;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            margin: 5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .control-btn.primary {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }

        .control-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        .control-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .control-input {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1em;
        }

        .control-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .control-select {
            width: 100%;
            padding: 12px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1em;
        }

        .control-select option {
            background: #1a1a2e;
            color: white;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }

        .status-inactive {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }

        .status-warning {
            background: #f39c12;
            box-shadow: 0 0 10px #f39c12;
        }

        .metrics-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .metric-value {
            color: #00d4aa;
            font-weight: 600;
        }

        .shortcuts-list {
            max-height: 200px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
        }

        .shortcut-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .shortcut-key {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 8px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 0.8em;
        }

        .theme-preview {
            width: 100%;
            height: 60px;
            border-radius: 10px;
            margin: 10px 0;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .theme-preview:hover {
            transform: scale(1.05);
        }

        .theme-preview.active {
            border: 2px solid #00d4aa;
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.5);
        }

        .notification-test {
            margin-top: 15px;
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        @media (max-width: 768px) {
            .controls-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <button class="nav-btn" onclick="navigateHome()">🏠 Accueil</button>
        <button class="nav-btn" onclick="navigateChat()">💬 Chat</button>
        <button class="nav-btn" onclick="navigateBrain()">🧠 Cerveau</button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎛️ Contrôles Avancés LOUNA AI</h1>
            <p>Gestion complète des systèmes et fonctionnalités avancées</p>
        </div>

        <!-- Grille de contrôles -->
        <div class="controls-grid">
            <!-- Contrôles DeepSeek -->
            <div class="control-panel">
                <div class="panel-title">
                    <span class="panel-icon">🤖</span>
                    DeepSeek R1 8B
                    <span class="status-indicator status-active" id="deepseekStatus"></span>
                </div>

                <div class="control-group">
                    <button class="control-btn success" onclick="testDeepSeekConnection()">🔗 Tester Connexion</button>
                    <button class="control-btn warning" onclick="disconnectDeepSeek()">⏸️ Déconnecter</button>
                    <button class="control-btn primary" onclick="reconnectDeepSeek()">🔄 Reconnecter</button>
                    <button class="control-btn danger" onclick="emergencyStopDeepSeek()">🚨 Arrêt Urgence</button>
                </div>

                <div class="metrics-display" id="deepseekMetrics">
                    <div class="metric-item">
                        <span>Statut:</span>
                        <span class="metric-value" id="deepseekStatusText">Connecté</span>
                    </div>
                    <div class="metric-item">
                        <span>Temps de réponse:</span>
                        <span class="metric-value" id="deepseekResponseTime">0ms</span>
                    </div>
                    <div class="metric-item">
                        <span>Qualité:</span>
                        <span class="metric-value" id="deepseekQuality">95%</span>
                    </div>
                </div>
            </div>

            <!-- Contrôles Mémoire Thermique -->
            <div class="control-panel">
                <div class="panel-title">
                    <span class="panel-icon">🧠</span>
                    Mémoire Thermique
                    <span class="status-indicator status-active" id="thermalStatus"></span>
                </div>

                <div class="control-group">
                    <button class="control-btn success" onclick="backupThermalMemory()">💾 Sauvegarder</button>
                    <button class="control-btn primary" onclick="optimizeThermalMemory()">⚡ Optimiser</button>
                    <button class="control-btn warning" onclick="clearThermalCache()">🧹 Nettoyer Cache</button>
                </div>

                <div class="metrics-display" id="thermalMetrics">
                    <div class="metric-item">
                        <span>Neurones:</span>
                        <span class="metric-value" id="thermalNeurons">1,064,000</span>
                    </div>
                    <div class="metric-item">
                        <span>Température:</span>
                        <span class="metric-value" id="thermalTemperature">37.0°C</span>
                    </div>
                    <div class="metric-item">
                        <span>Efficacité:</span>
                        <span class="metric-value" id="thermalEfficiency">95%</span>
                    </div>
                </div>
            </div>

            <!-- Contrôles Notifications -->
            <div class="control-panel">
                <div class="panel-title">
                    <span class="panel-icon">🔔</span>
                    Système de Notifications
                </div>

                <div class="control-group">
                    <label class="control-label">Type de notification test:</label>
                    <select class="control-select" id="notificationType">
                        <option value="info">Information</option>
                        <option value="success">Succès</option>
                        <option value="warning">Avertissement</option>
                        <option value="error">Erreur</option>
                        <option value="deepseek">DeepSeek</option>
                        <option value="security">Sécurité</option>
                    </select>
                </div>

                <div class="control-group">
                    <input type="text" class="control-input" id="notificationMessage" placeholder="Message de test...">
                </div>

                <div class="notification-test">
                    <button class="control-btn primary" onclick="testNotification()">📢 Tester Notification</button>
                    <button class="control-btn warning" onclick="clearAllNotifications()">🧹 Effacer Toutes</button>
                </div>
            </div>

            <!-- Contrôles Raccourcis -->
            <div class="control-panel">
                <div class="panel-title">
                    <span class="panel-icon">⌨️</span>
                    Raccourcis Clavier
                </div>

                <div class="control-group">
                    <button class="control-btn success" onclick="toggleShortcuts()">🔄 Activer/Désactiver</button>
                    <button class="control-btn primary" onclick="showShortcutsList()">📋 Liste Complète</button>
                </div>

                <div class="shortcuts-list" id="shortcutsList">
                    <div class="shortcut-item">
                        <span>Accueil</span>
                        <span class="shortcut-key">Ctrl+H</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Chat IA</span>
                        <span class="shortcut-key">Ctrl+C</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Cerveau 3D</span>
                        <span class="shortcut-key">Ctrl+B</span>
                    </div>
                    <div class="shortcut-item">
                        <span>Arrêt Urgence</span>
                        <span class="shortcut-key">Ctrl+Shift+E</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;
        let updateInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎛️ Interface contrôles avancés chargée');

            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            initializeInterface();
            startMetricsUpdates();
        });

        // Initialiser l'interface
        function initializeInterface() {
            updateSystemStatus();
            loadShortcutsList();
        }

        // Navigation
        function navigateHome() {
            window.location.href = '/electron-optimized-interface.html';
        }

        function navigateChat() {
            window.location.href = '/louna-interface-nouvelle.html';
        }

        function navigateBrain() {
            window.location.href = '/brain-3d-spectacular.html';
        }

        // Contrôles DeepSeek
        async function testDeepSeekConnection() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-chat', 'Test de connexion avancé');
                    if (result.success) {
                        alert('✅ Connexion DeepSeek réussie !\nRéponse: ' + result.response.substring(0, 100) + '...');
                        updateDeepSeekMetrics(result);
                    } else {
                        alert('❌ Erreur connexion: ' + result.error);
                    }
                }
            } catch (error) {
                alert('❌ Erreur test connexion: ' + error.message);
            }
        }

        async function disconnectDeepSeek() {
            if (confirm('Voulez-vous vraiment déconnecter DeepSeek R1 8B ?')) {
                try {
                    if (electronAPI) {
                        const result = await electronAPI.invoke('deepseek-disconnect', 'Déconnexion via contrôles avancés');
                        if (result.success) {
                            alert('✅ DeepSeek déconnecté');
                            updateSystemStatus();
                        }
                    }
                } catch (error) {
                    alert('❌ Erreur déconnexion: ' + error.message);
                }
            }
        }

        async function reconnectDeepSeek() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-reconnect');
                    if (result.success) {
                        alert('✅ DeepSeek reconnecté');
                        updateSystemStatus();
                    }
                }
            } catch (error) {
                alert('❌ Erreur reconnexion: ' + error.message);
            }
        }

        async function emergencyStopDeepSeek() {
            if (confirm('⚠️ ARRÊT D\'URGENCE DEEPSEEK - Êtes-vous sûr ?')) {
                try {
                    if (electronAPI) {
                        const result = await electronAPI.invoke('deepseek-emergency-stop', 'Arrêt d\'urgence via contrôles avancés');
                        if (result.success) {
                            alert('🚨 Arrêt d\'urgence activé');
                            updateSystemStatus();
                        }
                    }
                } catch (error) {
                    alert('❌ Erreur arrêt urgence: ' + error.message);
                }
            }
        }

        // Contrôles Mémoire Thermique
        async function backupThermalMemory() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('thermal-memory-backup');
                    if (result.success) {
                        alert('✅ Sauvegarde mémoire thermique réussie');
                    }
                }
            } catch (error) {
                alert('❌ Erreur sauvegarde: ' + error.message);
            }
        }

        async function optimizeThermalMemory() {
            alert('⚡ Optimisation mémoire thermique en cours...');
            // Simulation d'optimisation
            setTimeout(() => {
                alert('✅ Mémoire thermique optimisée');
                updateSystemStatus();
            }, 2000);
        }

        async function clearThermalCache() {
            if (confirm('Voulez-vous vraiment nettoyer le cache thermique ?')) {
                alert('🧹 Cache thermique nettoyé');
                updateSystemStatus();
            }
        }

        // Contrôles Notifications
        async function testNotification() {
            const type = document.getElementById('notificationType').value;
            const message = document.getElementById('notificationMessage').value || 'Test de notification';

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('ui-notification', type, 'Test Notification', message);
                    if (result.success) {
                        console.log('✅ Notification envoyée');
                    }
                }
            } catch (error) {
                alert('❌ Erreur notification: ' + error.message);
            }
        }

        async function clearAllNotifications() {
            if (confirm('Voulez-vous effacer toutes les notifications ?')) {
                alert('🧹 Toutes les notifications effacées');
            }
        }

        // Contrôles Raccourcis
        async function toggleShortcuts() {
            alert('🔄 Raccourcis basculés');
        }

        function showShortcutsList() {
            alert('📋 Liste complète des raccourcis affichée dans la console');
            console.log('Liste des raccourcis LOUNA AI:');
            console.log('Ctrl+H - Accueil');
            console.log('Ctrl+C - Chat IA');
            console.log('Ctrl+B - Cerveau 3D');
            console.log('Ctrl+M - Mode MCP');
            console.log('Ctrl+S - Sécurité');
            console.log('Ctrl+Shift+E - Arrêt Urgence');
        }

        // Mise à jour du statut système
        async function updateSystemStatus() {
            try {
                if (electronAPI) {
                    // Statut DeepSeek
                    const deepseekStatus = await electronAPI.invoke('deepseek-status');
                    updateDeepSeekStatus(deepseekStatus);

                    // Métriques système
                    const metrics = await electronAPI.invoke('get-metrics');
                    updateMetricsDisplay(metrics);
                }
            } catch (error) {
                console.error('❌ Erreur mise à jour statut:', error);
            }
        }

        function updateDeepSeekStatus(status) {
            const statusIndicator = document.getElementById('deepseekStatus');
            const statusText = document.getElementById('deepseekStatusText');

            if (status.success && status.status.connected) {
                statusIndicator.className = 'status-indicator status-active';
                statusText.textContent = 'Connecté';
            } else {
                statusIndicator.className = 'status-indicator status-inactive';
                statusText.textContent = 'Déconnecté';
            }
        }

        function updateDeepSeekMetrics(result) {
            if (result.quality) {
                document.getElementById('deepseekQuality').textContent = Math.round(result.quality * 100) + '%';
            }
        }

        function updateMetricsDisplay(metrics) {
            if (metrics.success && metrics.metrics) {
                const data = metrics.metrics;

                // Mémoire thermique
                if (data.brain) {
                    document.getElementById('thermalNeurons').textContent = data.brain.neurons?.toLocaleString() || '1,064,000';
                    document.getElementById('thermalTemperature').textContent = (data.brain.temperature || 37.0) + '°C';
                }
            }
        }

        function loadShortcutsList() {
            // Liste déjà chargée statiquement
            console.log('📋 Liste des raccourcis chargée');
        }

        // Démarrer les mises à jour des métriques
        function startMetricsUpdates() {
            updateInterval = setInterval(updateSystemStatus, 5000); // Toutes les 5 secondes
        }

        // Nettoyage à la fermeture
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>