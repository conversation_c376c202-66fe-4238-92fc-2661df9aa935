/**
 * 🔄 MISE À JOUR GLOBALE QI - TOUTES LES PAGES
 * Script pour s'assurer que toutes les pages affichent le QI réaliste de 185
 */

window.QIGlobalUpdater = {
    // Configuration
    config: {
        realisticQI: 185,
        updateInterval: 5000, // 5 secondes
        elementsToUpdate: [
            'qi', 'qiTotal', 'qi-display', 'qiAgentTotal', 'qiTotalFinal',
            'combined-iq', 'qi-value', 'qi-score', 'current-qi', 'currentQI',
            'currentQIText', 'qi-number', 'qiAgentBase'
        ]
    },

    // État
    state: {
        isUpdating: false,
        updateCount: 0,
        lastUpdate: null
    },

    // 🚀 Initialiser le système de mise à jour
    init: function() {
        console.log('🔄 QI Global Updater initialisé - QI réaliste: 185');
        this.updateAllQIElements();
        this.startAutoUpdate();
        this.bindToServerUpdates();
    },

    // 🔄 Démarrer la mise à jour automatique
    startAutoUpdate: function() {
        setInterval(() => {
            this.updateAllQIElements();
        }, this.config.updateInterval);
    },

    // 🎯 Mettre à jour tous les éléments QI
    updateAllQIElements: function() {
        if (this.state.isUpdating) return;
        
        this.state.isUpdating = true;
        
        try {
            const realisticQI = this.config.realisticQI;
            
            // 🔍 MISE À JOUR PAR ID
            this.config.elementsToUpdate.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    // Vérifier si c'est un élément QI
                    const isQIElement = this.isQIElement(element, elementId);
                    if (isQIElement) {
                        element.textContent = realisticQI;
                    }
                }
            });

            // 🔍 MISE À JOUR PAR CLASSE
            const qiClasses = [
                'qi-value', 'qi-number', 'qi-total', 'qi-agent', 'qi-memory'
            ];

            qiClasses.forEach(className => {
                const elements = document.getElementsByClassName(className);
                Array.from(elements).forEach(element => {
                    if (this.isQIElement(element, className)) {
                        // Logique spéciale pour différents types
                        if (className === 'qi-agent' && element.id === 'qiAgentBase') {
                            element.textContent = '100'; // Base fixe
                        } else if (className === 'qi-memory' && element.id === 'qiAgentTotal') {
                            element.textContent = '85'; // Bonus mémoire
                        } else if (className === 'qi-total' || element.id === 'qiTotalFinal') {
                            element.textContent = realisticQI; // Total réaliste
                        } else {
                            element.textContent = realisticQI;
                        }
                    }
                });
            });

            // 🔍 MISE À JOUR DES TEXTES CONTENANT QI
            this.updateQITexts();

            this.state.updateCount++;
            this.state.lastUpdate = new Date().toISOString();

            // Log moins fréquent
            if (this.state.updateCount % 20 === 0) {
                console.log(`🔄 QI mis à jour globalement: ${realisticQI} (${this.state.updateCount} mises à jour)`);
            }

        } catch (error) {
            console.error('❌ Erreur mise à jour QI globale:', error);
        } finally {
            this.state.isUpdating = false;
        }
    },

    // 🔍 Vérifier si un élément est lié au QI
    isQIElement: function(element, identifier) {
        const qiKeywords = ['qi', 'iq', 'coefficient', 'intelligence'];
        const elementText = (element.textContent || '').toLowerCase();
        const elementId = (element.id || '').toLowerCase();
        const elementClass = (element.className || '').toLowerCase();
        const parentText = (element.parentElement?.textContent || '').toLowerCase();
        
        // Vérifier si l'élément ou son contexte contient des mots-clés QI
        const hasQIKeyword = qiKeywords.some(keyword => 
            identifier.toLowerCase().includes(keyword) ||
            elementId.includes(keyword) ||
            elementClass.includes(keyword) ||
            parentText.includes(keyword)
        );

        // Vérifier si c'est un nombre qui pourrait être un QI
        const isNumeric = /^\d+$/.test(elementText.trim());
        const isQIRange = isNumeric && parseInt(elementText) >= 80 && parseInt(elementText) <= 500;

        return hasQIKeyword || (isNumeric && isQIRange);
    },

    // 📝 Mettre à jour les textes contenant des références QI
    updateQITexts: function() {
        const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6');
        
        textElements.forEach(element => {
            const text = element.textContent;
            
            // Remplacer les anciennes valeurs QI par la nouvelle
            if (text && typeof text === 'string') {
                let newText = text;
                
                // Remplacements spécifiques
                newText = newText.replace(/QI de 225/g, 'QI de 185');
                newText = newText.replace(/QI 225/g, 'QI 185');
                newText = newText.replace(/QI de 420/g, 'QI de 185');
                newText = newText.replace(/QI 420/g, 'QI 185');
                newText = newText.replace(/Pourquoi QI 225/g, 'Pourquoi QI 185 réaliste');
                
                if (newText !== text) {
                    element.textContent = newText;
                }
            }
        });
    },

    // 🌐 Se connecter aux mises à jour du serveur
    bindToServerUpdates: function() {
        // Écouter les événements de mise à jour
        if (window.addEventListener) {
            window.addEventListener('qiUpdated', (event) => {
                if (event.detail && event.detail.newQI) {
                    // Utiliser le QI du serveur s'il est réaliste
                    const serverQI = event.detail.newQI;
                    if (serverQI >= 150 && serverQI <= 200) {
                        this.config.realisticQI = serverQI;
                    }
                    this.updateAllQIElements();
                }
            });
        }

        // Synchroniser avec le serveur
        this.syncWithServer();
    },

    // 🌐 Synchroniser avec le serveur
    syncWithServer: async function() {
        try {
            const response = await fetch('/api/metrics');
            const data = await response.json();
            
            if (data.success && data.qi) {
                const serverQI = data.qi.unified || 185;
                
                // Utiliser le QI du serveur s'il est dans une plage réaliste
                if (serverQI >= 150 && serverQI <= 200) {
                    this.config.realisticQI = serverQI;
                    this.updateAllQIElements();
                }
            }
        } catch (error) {
            console.log('📡 Serveur non disponible, QI local utilisé: 185');
        }
    },

    // 🎯 Forcer une mise à jour complète
    forceUpdate: function() {
        this.updateAllQIElements();
        this.syncWithServer();
        
        // Déclencher un événement global
        if (window.dispatchEvent) {
            window.dispatchEvent(new CustomEvent('qiGloballyUpdated', {
                detail: { 
                    newQI: this.config.realisticQI, 
                    source: 'global_updater',
                    timestamp: new Date().toISOString()
                }
            }));
        }
        
        console.log(`🔄 Mise à jour forcée - QI: ${this.config.realisticQI}`);
    },

    // 📊 Obtenir les statistiques
    getStats: function() {
        return {
            realisticQI: this.config.realisticQI,
            updateCount: this.state.updateCount,
            lastUpdate: this.state.lastUpdate,
            isUpdating: this.state.isUpdating
        };
    }
};

// 🚀 Auto-initialisation
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.QIGlobalUpdater.init();
        
        // Forcer une mise à jour immédiate
        window.QIGlobalUpdater.forceUpdate();
        
    }, 2000); // Attendre 2 secondes pour que les autres scripts se chargent
});

// 🌐 Fonction globale pour forcer une mise à jour
window.updateAllQI = function() {
    if (window.QIGlobalUpdater) {
        return window.QIGlobalUpdater.forceUpdate();
    }
};

console.log('🔄 QI Global Updater chargé - Mise à jour automatique vers QI 185');
