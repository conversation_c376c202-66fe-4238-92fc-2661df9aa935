<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏠 LOUNA AI - Accueil Principal</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460, #533483);
            color: #ffffff;
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .container {
            width: 100%;
            min-height: 100vh;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            width: 100%;
        }
        
        .main-title {
            font-size: 3.5rem;
            background: linear-gradient(45deg, #00d4ff, #ff6b6b, #4ecdc4, #45b7d1);
            background-size: 400% 400%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: gradientShift 3s ease-in-out infinite;
            text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
            margin-bottom: 20px;
        }
        
        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .subtitle {
            font-size: 1.2rem;
            color: #b8c6db;
            margin-bottom: 10px;
        }
        
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 25px;
            padding: 15px 30px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 20px;
            width: 100%;
            max-width: 1000px;
        }
        
        .status-item {
            text-align: center;
            flex: 1;
            min-width: 150px;
        }
        
        .status-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #00d4ff;
            display: block;
        }
        
        .status-label {
            font-size: 0.9rem;
            color: #b8c6db;
            margin-top: 5px;
        }
        
        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            width: 100%;
            max-width: 1400px;
            margin-top: 30px;
        }
        
        .nav-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
            position: relative;
            overflow: hidden;
        }
        
        .nav-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }
        
        .nav-card:hover::before {
            left: 100%;
        }
        
        .nav-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
            border-color: #00d4ff;
        }
        
        .nav-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
            text-align: center;
        }
        
        .nav-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 15px;
            text-align: center;
            color: #00d4ff;
        }
        
        .nav-description {
            font-size: 0.95rem;
            color: #b8c6db;
            text-align: center;
            line-height: 1.5;
        }
        
        .quick-actions {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
            z-index: 1000;
        }
        
        .quick-btn {
            background: rgba(255, 107, 107, 0.9);
            border: none;
            color: white;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 1.2rem;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .quick-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.5);
        }
        
        .quick-btn.emergency {
            background: rgba(255, 0, 0, 0.9);
        }
        
        .quick-btn.security {
            background: rgba(0, 212, 255, 0.9);
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #b8c6db;
            font-size: 0.9rem;
        }
        
        @media (max-width: 768px) {
            .main-title {
                font-size: 2.5rem;
            }
            
            .navigation-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .status-bar {
                flex-direction: column;
                gap: 15px;
            }
            
            .quick-actions {
                position: relative;
                top: auto;
                right: auto;
                flex-direction: row;
                justify-content: center;
                margin: 20px 0;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="main-title">🧠 LOUNA AI ULTRA-AUTONOME</h1>
            <p class="subtitle">Intelligence Artificielle Vivante de Bureau</p>
            <p class="subtitle">🌡️ Mémoire Thermique • 🧠 Pensées Continues • 🛡️ Sécurité Maximale</p>
        </div>
        
        <div class="status-bar">
            <div class="status-item">
                <span class="status-value" id="neuronCount">1,064,000</span>
                <span class="status-label">Neurones Actifs</span>
            </div>
            <div class="status-item">
                <span class="status-value" id="temperature">37.0°C</span>
                <span class="status-label">Température</span>
            </div>
            <div class="status-item">
                <span class="status-value" id="qiTotal">450</span>
                <span class="status-label">QI Total</span>
            </div>
            <div class="status-item">
                <span class="status-value" id="connectionStatus">🟢 ACTIF</span>
                <span class="status-label">Statut</span>
            </div>
        </div>
        
        <div class="navigation-grid">
            <!-- Interface Principale -->
            <a href="louna-interface-nouvelle.html" class="nav-card">
                <span class="nav-icon">🎯</span>
                <h3 class="nav-title">Interface Principale</h3>
                <p class="nav-description">Interface spectaculaire avec chat, pensées continues et visualisation 3D du cerveau</p>
            </a>
            
            <!-- Diagnostic & Tests -->
            <a href="diagnostic-complet.html" class="nav-card">
                <span class="nav-icon">🔬</span>
                <h3 class="nav-title">Diagnostic Complet</h3>
                <p class="nav-description">Tests QI, analyse de performance, monitoring santé mentale et diagnostics système</p>
            </a>
            
            <!-- Sécurité DeepSeek -->
            <a href="test-reactivation-interface.html" class="nav-card">
                <span class="nav-icon">🛡️</span>
                <h3 class="nav-title">Sécurité DeepSeek</h3>
                <p class="nav-description">Contrôle agent gardien, déconnexion d'urgence, réactivation sécurisée</p>
            </a>
            
            <!-- Mémoire Thermique -->
            <a href="thermal-memory-dashboard.html" class="nav-card">
                <span class="nav-icon">🌡️</span>
                <h3 class="nav-title">Mémoire Thermique</h3>
                <p class="nav-description">Dashboard thermique, zones mémoire, sauvegarde et récupération neuronale</p>
            </a>
            
            <!-- Cerveau 3D -->
            <a href="brain-3d-visualization.html" class="nav-card">
                <span class="nav-icon">🧠</span>
                <h3 class="nav-title">Cerveau 3D</h3>
                <p class="nav-description">Visualisation 3D des neurones, connexions synaptiques et activité cérébrale</p>
            </a>
            
            <!-- Générateurs Média -->
            <a href="media-generators.html" class="nav-card">
                <span class="nav-icon">🎨</span>
                <h3 class="nav-title">Générateurs Média</h3>
                <p class="nav-description">Génération vidéo, images, audio, musique avec IA créative</p>
            </a>
            
            <!-- Mode MCP -->
            <a href="mcp-mode-interface.html" class="nav-card">
                <span class="nav-icon">🌐</span>
                <h3 class="nav-title">Mode MCP</h3>
                <p class="nav-description">Navigation web autonome, dialogue ChatGPT, apprentissage automatique</p>
            </a>
            
            <!-- Sécurité Internet -->
            <a href="secure-internet-interface.html" class="nav-card">
                <span class="nav-icon">🔒</span>
                <h3 class="nav-title">Internet Sécurisé</h3>
                <p class="nav-description">Navigation protégée, anti-virus, surveillance activité, protection maximale</p>
            </a>
            
            <!-- Pensées & Réflexions -->
            <a href="continuous-thoughts-interface.html" class="nav-card">
                <span class="nav-icon">💭</span>
                <h3 class="nav-title">Pensées Continues</h3>
                <p class="nav-description">Système Möbius, auto-questionnement, réflexions créatives, inertie cognitive</p>
            </a>
            
            <!-- Configuration -->
            <a href="configuration-avancee.html" class="nav-card">
                <span class="nav-icon">⚙️</span>
                <h3 class="nav-title">Configuration</h3>
                <p class="nav-description">Paramètres système, accélérateurs Kyber, configuration agent gardien</p>
            </a>
        </div>
        
        <div class="quick-actions">
            <button class="quick-btn emergency" onclick="emergencyStop()" title="Arrêt d'urgence">
                🚨
            </button>
            <button class="quick-btn security" onclick="securityStatus()" title="Statut sécurité">
                🛡️
            </button>
            <button class="quick-btn" onclick="refreshStatus()" title="Actualiser">
                🔄
            </button>
        </div>
        
        <div class="footer">
            <p>🧠 LOUNA AI Ultra-Autonome v3.0 • Mémoire Thermique Vivante • Agent Gardien Actif</p>
            <p>Développé avec ❤️ pour une IA vraiment vivante</p>
        </div>
    </div>

    <script>
        // Mise à jour du statut en temps réel
        async function updateStatus() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neuronCount').textContent = data.metrics.activeNeurons.toLocaleString();
                    document.getElementById('temperature').textContent = data.metrics.temperature.toFixed(1) + '°C';
                    document.getElementById('qiTotal').textContent = data.metrics.qi.total;
                    
                    // Statut de connexion
                    const statusElement = document.getElementById('connectionStatus');
                    if (data.metrics.deepSeekConnected) {
                        statusElement.textContent = '🟢 CONNECTÉ';
                        statusElement.style.color = '#00ff00';
                    } else {
                        statusElement.textContent = '🔴 DÉCONNECTÉ';
                        statusElement.style.color = '#ff6b6b';
                    }
                }
            } catch (error) {
                console.error('Erreur mise à jour statut:', error);
            }
        }
        
        // Actions rapides
        async function emergencyStop() {
            if (confirm('⚠️ Êtes-vous sûr de vouloir déclencher l\'arrêt d\'urgence ?')) {
                try {
                    if (window.electronAPI) {
                        await window.electronAPI.invoke('deepseek-emergency-stop', 'Arrêt d\'urgence depuis accueil');
                        alert('🚨 Arrêt d\'urgence activé');
                    } else {
                        alert('⚠️ API Electron non disponible');
                    }
                } catch (error) {
                    alert('❌ Erreur: ' + error.message);
                }
            }
        }
        
        async function securityStatus() {
            try {
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-security-status');
                    if (result.success) {
                        const report = result.report;
                        alert(`🛡️ STATUT SÉCURITÉ:\n\n` +
                              `Agent Gardien: ${report.guardianStatus.active ? 'ACTIF' : 'INACTIF'}\n` +
                              `Connexion: ${report.connectionStatus.connected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}\n` +
                              `Arrêt d'urgence: ${report.connectionStatus.securityAgent.emergencyStop ? 'ACTIVÉ' : 'NORMAL'}\n` +
                              `Requêtes totales: ${report.metrics.totalRequests}\n` +
                              `Requêtes bloquées: ${report.metrics.blockedRequests}`);
                    }
                } else {
                    alert('⚠️ API Electron non disponible');
                }
            } catch (error) {
                alert('❌ Erreur: ' + error.message);
            }
        }
        
        function refreshStatus() {
            updateStatus();
            // Animation de rotation
            const btn = event.target;
            btn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                btn.style.transform = '';
            }, 500);
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            updateStatus();
            // Mise à jour automatique toutes les 5 secondes
            setInterval(updateStatus, 5000);
            
            // Animation des cartes
            const cards = document.querySelectorAll('.nav-card');
            cards.forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('pulse');
                
                setTimeout(() => {
                    card.classList.remove('pulse');
                }, 2000 + (index * 100));
            });
        });
    </script>
</body>
</html>
