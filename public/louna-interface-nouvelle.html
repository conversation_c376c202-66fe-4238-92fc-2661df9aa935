<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 LOUNA AI Ultra-Autonome - NOUVELLE INTERFACE SANS CACHE</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            overflow-x: hidden;
            min-height: 100vh;
            position: relative;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 15px;
        }

        /* Header principal */
        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 25px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 8px 32px rgba(255, 107, 157, 0.3);
        }

        .header-title h1 {
            font-size: 2.2em;
            margin: 0;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .header-title p {
            font-size: 0.9em;
            opacity: 0.8;
            margin: 0;
            color: #00ff00;
            font-weight: bold;
        }

        .status-badge {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        /* Section Chat avec pensées continues */
        .chat-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .chat-title {
            text-align: center;
            font-size: 1.8em;
            margin-bottom: 25px;
            background: linear-gradient(135deg, #ff6b9d, #00d4aa);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .chat-input {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1.1em;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .send-btn {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1.1em;
            width: 100%;
            margin-bottom: 20px;
        }

        .thoughts-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 15px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .thought-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #ff6b9d;
        }

        .thought-time {
            font-size: 0.8em;
            color: #00d4aa;
            font-weight: bold;
        }

        .thought-content {
            margin-top: 5px;
            line-height: 1.4;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .metric-number {
            font-size: 2em;
            font-weight: 700;
            color: #00d4aa;
            margin-bottom: 10px;
        }

        .metric-label {
            font-size: 0.9em;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header principal -->
        <div class="header">
            <div class="header-left">
                <div class="logo-icon">🧠</div>
                <div class="header-title">
                    <h1>LOUNA AI Ultra-Autonome</h1>
                    <p>✅ NOUVELLE INTERFACE SANS CACHE - PENSÉES CONTINUES ACTIVES</p>
                </div>
            </div>
            <div style="display: flex; gap: 15px; align-items: center;">
                <button onclick="goToHome()" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 600;">
                    🏠 Accueil
                </button>
                <button onclick="toggleVoiceReflection()" id="voiceBtn" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 600;">
                    🔊 Écouter Pensées
                </button>
                <button onclick="startChatGPTDialogue()" id="chatgptBtn" style="background: linear-gradient(135deg, #00d4aa, #01a3a4); color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer; font-weight: 600;">
                    🌐 Dialoguer ChatGPT
                </button>

                <!-- 🧠 ZONE D'APPRENTISSAGE CHATGPT -->
                <div id="chatgptLearningZone" style="display: none; margin-top: 20px; padding: 20px; background: linear-gradient(135deg, rgba(0, 212, 170, 0.1), rgba(1, 163, 164, 0.1)); border-radius: 15px; border: 2px solid #00d4aa;">
                    <h3 style="color: #00d4aa; margin: 0 0 15px 0;">🧠 Zone d'Apprentissage ChatGPT</h3>
                    <p style="color: #fff; margin-bottom: 15px;">Collez ici la réponse de ChatGPT pour que LOUNA apprenne et génère une question de suivi intelligente :</p>
                    <textarea id="chatgptResponseInput" placeholder="Collez la réponse de ChatGPT ici..." style="width: 100%; height: 150px; padding: 15px; border-radius: 10px; border: 1px solid #00d4aa; background: rgba(0, 0, 0, 0.3); color: #fff; font-size: 14px; resize: vertical;"></textarea>
                    <div style="margin-top: 15px; display: flex; gap: 10px;">
                        <button onclick="submitChatGPTResponse()" style="background: linear-gradient(135deg, #00d4aa, #01a3a4); color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            🧠 Analyser et Apprendre
                        </button>
                        <button onclick="clearChatGPTResponse()" style="background: linear-gradient(135deg, #ff6b9d, #c44569); color: white; border: none; padding: 10px 20px; border-radius: 10px; cursor: pointer; font-weight: 600;">
                            🗑️ Effacer
                        </button>
                    </div>
                    <div id="learningResult" style="margin-top: 15px; padding: 15px; background: rgba(0, 0, 0, 0.2); border-radius: 10px; display: none;">
                        <h4 style="color: #00d4aa; margin: 0 0 10px 0;">📊 Résultat de l'Apprentissage :</h4>
                        <div id="learningDetails"></div>
                        <div id="nextQuestionDisplay" style="margin-top: 15px; padding: 15px; background: linear-gradient(135deg, rgba(255, 107, 157, 0.2), rgba(196, 69, 105, 0.2)); border-radius: 10px;">
                            <h4 style="color: #ff6b9d; margin: 0 0 10px 0;">🎯 Prochaine Question Intelligente :</h4>
                            <p id="nextQuestionText" style="color: #fff; margin: 0; font-style: italic;"></p>
                        </div>
                    </div>
                </div>
                <div class="status-badge">SYSTÈME ACTIF</div>
            </div>
        </div>

        <!-- Section Chat avec pensées continues -->
        <div class="chat-section">
            <div class="chat-title">💬 Chat IA avec Pensées Continues</div>
            
            <input type="text" class="chat-input" id="messageInput" placeholder="Tapez votre message à LOUNA AI...">
            <button class="send-btn" onclick="sendMessage()">🚀 Envoyer Message</button>
            
            <div id="chatResponse" style="background: rgba(0, 0, 0, 0.3); padding: 20px; border-radius: 15px; margin-bottom: 20px; display: none;">
                <h3 style="color: #00d4aa; margin-bottom: 10px;">Réponse de LOUNA AI:</h3>
                <div id="responseText"></div>
            </div>

            <!-- 🌐 INTERFACE RÉPONSE CHATGPT -->
            <div id="chatgptResponseSection" style="background: rgba(52, 152, 219, 0.2); padding: 20px; border-radius: 15px; margin-bottom: 20px; display: none; border: 2px solid #3498db;">
                <h3 style="color: #3498db; margin-bottom: 15px;">🌐 Réponse ChatGPT</h3>
                <div id="chatgptCurrentQuestion" style="background: rgba(255, 255, 255, 0.1); padding: 10px; border-radius: 8px; margin-bottom: 15px; font-style: italic;">
                    Question en attente...
                </div>
                <textarea id="chatgptResponseInput" placeholder="Collez ici la réponse de ChatGPT..." style="width: 100%; height: 120px; padding: 15px; border: none; border-radius: 10px; background: rgba(255, 255, 255, 0.1); color: white; font-size: 1em; resize: vertical;"></textarea>
                <button onclick="submitChatGPTResponse()" style="background: linear-gradient(135deg, #3498db, #2980b9); color: white; border: none; padding: 12px 25px; border-radius: 20px; cursor: pointer; font-weight: 600; margin-top: 10px; width: 100%;">
                    📤 Envoyer Réponse ChatGPT
                </button>
            </div>

            <div class="thoughts-display">
                <h3 style="color: #ff6b9d; margin-bottom: 15px;">🌀 Pensées en Bande de Möbius de LOUNA AI:</h3>
                <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px; margin-bottom: 15px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <span style="color: #00d4aa; font-weight: bold;">État Möbius:</span>
                        <span id="mobiusStatus" style="color: #ff6b9d;">Initialisation...</span>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px; font-size: 0.9em;">
                        <div>Position: <span id="mobiusPosition" style="color: #00d4aa;">0%</span></div>
                        <div>Phase: <span id="mobiusPhase" style="color: #ff6b9d;">exploration</span></div>
                        <div>Direction: <span id="mobiusDirection" style="color: #f39c12;">→</span></div>
                        <div>Chaos: <span id="chaosLevel" style="color: #e74c3c;">30%</span></div>
                    </div>

                    <!-- 🧠 SANTÉ MENTALE -->
                    <div style="margin-top: 15px; padding: 10px; background: rgba(255, 255, 255, 0.05); border-radius: 8px;">
                        <div style="font-weight: bold; margin-bottom: 8px; color: #ff6b9d;">🧠 Santé Mentale</div>
                        <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 0.85em;">
                            <div>Fatigue: <span id="fatigueLevel" style="color: #f39c12;">0%</span></div>
                            <div>Stress: <span id="stressLevel" style="color: #e74c3c;">0%</span></div>
                            <div>État: <span id="mentalState" style="color: #00d4aa;">Actif</span></div>
                        </div>
                    </div>
                    <div style="margin-top: 10px;">
                        <div style="background: rgba(0, 0, 0, 0.3); height: 8px; border-radius: 4px; overflow: hidden;">
                            <div id="mobiusProgress" style="background: linear-gradient(90deg, #ff6b9d, #00d4aa, #ff6b9d); height: 100%; width: 0%; transition: width 0.5s ease;"></div>
                        </div>
                    </div>
                </div>
                <div id="thoughtsList">
                    <div class="thought-item">
                        <div class="thought-time">Chargement de la bande de Möbius...</div>
                        <div class="thought-content">🌀 Initialisation du système de pensées en bande de Möbius...</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques système -->
        <div class="chat-section">
            <div class="chat-title">📊 Métriques Système en Temps Réel</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-number" id="neurons">1,064,000</div>
                    <div class="metric-label">Neurones Actifs</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="synapses">7,448,000</div>
                    <div class="metric-label">Connexions Synaptiques</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="temperature">37.0°C</div>
                    <div class="metric-label">Température Thermique</div>
                </div>
                <div class="metric-card">
                    <div class="metric-number" id="qi">142</div>
                    <div class="metric-label">QI Total</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // VARIABLES GLOBALES POUR LA SYNTHÈSE VOCALE
        let voiceEnabled = false;
        let currentVoice = null;
        let lastSpokenThought = null;

        // NAVIGATION VERS L'ACCUEIL
        function goToHome() {
            window.location.href = '/interface-spectaculaire.html';
        }

        // INITIALISER LA SYNTHÈSE VOCALE
        function initializeVoice() {
            if ('speechSynthesis' in window) {
                const voices = speechSynthesis.getVoices();
                // Chercher une voix française féminine
                currentVoice = voices.find(voice =>
                    voice.lang.includes('fr') &&
                    (voice.name.toLowerCase().includes('female') || voice.name.toLowerCase().includes('femme'))
                ) || voices.find(voice => voice.lang.includes('fr')) || voices[0];

                console.log('🔊 Voix sélectionnée pour les pensées:', currentVoice?.name);
            }
        }

        // ACTIVER/DÉSACTIVER L'ÉCOUTE DES PENSÉES
        function toggleVoiceReflection() {
            voiceEnabled = !voiceEnabled;
            const btn = document.getElementById('voiceBtn');

            if (voiceEnabled) {
                btn.textContent = '🔇 Arrêter Écoute';
                btn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                console.log('🔊 Écoute des pensées ACTIVÉE');

                // Démarrer la réflexion continue sur le serveur
                fetch('/api/thoughts/control', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'start' })
                });
            } else {
                btn.textContent = '🔊 Écouter Pensées';
                btn.style.background = 'linear-gradient(135deg, #ff6b9d, #c44569)';
                console.log('🔇 Écoute des pensées DÉSACTIVÉE');

                // Arrêter toute synthèse en cours
                if ('speechSynthesis' in window) {
                    speechSynthesis.cancel();
                }
            }
        }

        // PARLER UNE PENSÉE
        function speakThought(thoughtContent) {
            if (!voiceEnabled || !('speechSynthesis' in window)) return;

            // Arrêter toute synthèse en cours
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(thoughtContent);

            if (currentVoice) {
                utterance.voice = currentVoice;
            }

            // Paramètres optimisés pour les pensées
            utterance.rate = 0.8; // Plus lent pour la réflexion
            utterance.pitch = 1.1; // Légèrement aigu
            utterance.volume = 0.7; // Volume modéré

            utterance.onstart = () => {
                console.log('🗣️ LOUNA pense à voix haute:', thoughtContent);
            };

            utterance.onerror = (error) => {
                console.error('❌ Erreur synthèse pensée:', error);
            };

            speechSynthesis.speak(utterance);
        }

        // RÉCUPÉRATION DES PENSÉES EN BANDE DE MÖBIUS AVEC SYNTHÈSE VOCALE
        async function loadContinuousThoughts() {
            try {
                const response = await fetch('/api/thoughts/continuous');
                const data = await response.json();

                if (data.success && data.thoughts && data.thoughts.length > 0) {
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = '';

                    // METTRE À JOUR L'ÉTAT DE LA BANDE DE MÖBIUS
                    if (data.mobiusState) {
                        const position = (data.mobiusState.position * 100).toFixed(1);
                        const phase = data.mobiusState.phase;
                        const direction = data.mobiusState.direction > 0 ? '→' : '←';

                        document.getElementById('mobiusPosition').textContent = position + '%';
                        document.getElementById('mobiusPhase').textContent = phase;
                        document.getElementById('mobiusDirection').textContent = direction;
                        document.getElementById('mobiusProgress').style.width = position + '%';

                        // Mettre à jour le niveau de chaos si disponible
                        if (data.chaosLevel !== undefined) {
                            document.getElementById('chaosLevel').textContent = (data.chaosLevel * 100).toFixed(0) + '%';
                        }

                        // 🌐 METTRE À JOUR LE STATUT CHATGPT
                        updateChatGPTStatus();

                        // 🌐 VÉRIFIER SI ON ATTEND UNE RÉPONSE CHATGPT
                        checkForChatGPTWaiting(data.thoughts);
                    }

                    // 🧠 METTRE À JOUR LA SANTÉ MENTALE
                    if (data.brainHealth) {
                        const health = data.brainHealth;
                        document.getElementById('fatigueLevel').textContent = (health.fatigue * 100).toFixed(0) + '%';
                        document.getElementById('stressLevel').textContent = (health.stress * 100).toFixed(0) + '%';

                        let mentalState = 'Actif';
                        let stateColor = '#00d4aa';

                        if (health.isDreaming) {
                            mentalState = '🌙 Rêve';
                            stateColor = '#9b59b6';
                        } else if (health.isResting) {
                            mentalState = '😴 Repos';
                            stateColor = '#3498db';
                        } else if (health.needsRest) {
                            mentalState = '😴 Fatigué';
                            stateColor = '#f39c12';
                        } else if (data.userInteractionActive) {
                            mentalState = '🎯 Focus';
                            stateColor = '#e74c3c';
                        } else if (data.thinkingMode === 'creative_chaos') {
                            mentalState = '🎨 Créatif';
                            stateColor = '#ff6b9d';
                        }

                        const stateElement = document.getElementById('mentalState');
                        stateElement.textContent = mentalState;
                        stateElement.style.color = stateColor;

                        // Statut coloré selon la phase
                        const phaseColors = {
                            'exploration': '🔍 Exploration',
                            'analysis': '🧮 Analyse',
                            'synthesis': '⚗️ Synthèse',
                            'reflection': '🪞 Réflexion',
                            'integration': '🔗 Intégration',
                            'transformation': '🦋 Transformation',
                            'emergence': '✨ Émergence',
                            'convergence': '🎯 Convergence'
                        };
                        document.getElementById('mobiusStatus').textContent = phaseColors[phase] || phase;
                    }

                    // Vérifier s'il y a une nouvelle pensée à dire
                    const latestThought = data.thoughts[data.thoughts.length - 1];
                    if (voiceEnabled && latestThought && latestThought.id !== lastSpokenThought) {
                        speakThought(latestThought.content);
                        lastSpokenThought = latestThought.id;
                    }

                    data.thoughts.slice(-8).forEach(thought => {
                        const thoughtDiv = document.createElement('div');
                        thoughtDiv.className = 'thought-item';

                        // Ajouter un indicateur si la pensée a été parlée
                        const spokenIndicator = (thought.id === lastSpokenThought && voiceEnabled) ? '🔊 ' : '';

                        // Icône selon la phase Möbius (incluant les phases artistiques)
                        const phaseIcon = thought.mobiusData ? {
                            'exploration': '🔍',
                            'analysis': '🧮',
                            'synthesis': '⚗️',
                            'reflection': '🪞',
                            'integration': '🔗',
                            'transformation': '🦋',
                            'emergence': '✨',
                            'convergence': '🎯',
                            'artistic_chaos': '🎨',
                            'emotional_flow': '💖',
                            'creative_madness': '🎭',
                            'poetic_synthesis': '📚',
                            'visual_dreams': '🌈',
                            'musical_thoughts': '🎵',
                            'narrative_weaving': '🎬',
                            'abstract_beauty': '🖼️',
                            'creative_chaos': '🎪'
                        }[thought.mobiusData.phase] || '🌀' : '🧠';

                        // Icônes spéciales pour les nouveaux types
                        if (thought.type === 'auto_dialogue') phaseIcon = '🤖';
                        if (thought.type === 'rest_initiation') phaseIcon = '😴';
                        if (thought.type === 'dream') phaseIcon = '🌙';
                        if (thought.type === 'wake_up') phaseIcon = '☀️';
                        if (thought.type === 'creative_chaos') phaseIcon = '🎪';
                        if (thought.type === 'chatgpt_dialogue_start') phaseIcon = '🌐';
                        if (thought.type === 'chatgpt_question') phaseIcon = '❓';
                        if (thought.type === 'chatgpt_followup_question') phaseIcon = '🔄';
                        if (thought.type === 'chatgpt_waiting_response') phaseIcon = '⏰';
                        if (thought.type === 'chatgpt_response_analysis') phaseIcon = '🧠';
                        if (thought.type === 'chatgpt_dialogue_end') phaseIcon = '🏁';

                        thoughtDiv.innerHTML = `
                            <div class="thought-time">[${thought.time}] ${spokenIndicator}${phaseIcon} ${thought.type}</div>
                            ${thought.internalQuestion ? `<div style="background: rgba(255, 107, 157, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #ff6b9d;">
                                <strong>❓ Question interne:</strong> ${thought.internalQuestion}
                            </div>` : ''}
                            ${thought.triggerType ? `<div style="background: rgba(255, 165, 0, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #ffa500;">
                                <strong>🔥 Déclencheur:</strong> ${thought.triggerType.replace(/_/g, ' ')}
                            </div>` : ''}
                            ${thought.question && thought.response ? `<div style="background: rgba(52, 152, 219, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #3498db;">
                                <strong>🤖 Auto-dialogue:</strong><br>
                                <strong>Q:</strong> ${thought.question}<br>
                                <strong>R:</strong> ${thought.response}
                            </div>` : ''}
                            ${thought.dreamType ? `<div style="background: rgba(155, 89, 182, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #9b59b6;">
                                <strong>🌙 Rêve ${thought.dreamCycle}:</strong> ${thought.dreamType.replace(/_/g, ' ')}
                            </div>` : ''}
                            ${thought.restType ? `<div style="background: rgba(52, 152, 219, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #3498db;">
                                <strong>😴 Repos:</strong> ${thought.restType} - ${thought.duration}h
                            </div>` : ''}
                            ${thought.dreamCount !== undefined ? `<div style="background: rgba(241, 196, 15, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #f1c40f;">
                                <strong>☀️ Réveil:</strong> ${thought.dreamCount} rêves vécus
                            </div>` : ''}
                            ${thought.chatgptQuestion ? `<div style="background: rgba(52, 152, 219, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #3498db;">
                                <strong>🌐 Question ChatGPT:</strong> ${thought.chatgptQuestion}
                            </div>` : ''}
                            ${thought.dialogueStats ? `<div style="background: rgba(46, 204, 113, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #2ecc71;">
                                <strong>🏁 Dialogue terminé:</strong> ${thought.dialogueStats.questionsAsked} questions en ${thought.dialogueStats.duration.toFixed(1)} min
                            </div>` : ''}
                            ${thought.waitingForResponse ? `<div style="background: rgba(241, 196, 15, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #f1c40f;">
                                <strong>⏰ En attente:</strong> Réponse ChatGPT attendue
                            </div>` : ''}
                            ${thought.isFollowUp ? `<div style="background: rgba(52, 152, 219, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #3498db;">
                                <strong>🔄 Question de suivi:</strong> Basée sur la réponse précédente
                            </div>` : ''}
                            ${thought.chatgptResponse ? `<div style="background: rgba(46, 204, 113, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #2ecc71;">
                                <strong>💬 Réponse ChatGPT:</strong> ${thought.chatgptResponse.substring(0, 100)}${thought.chatgptResponse.length > 100 ? '...' : ''}
                            </div>` : ''}
                            ${thought.followUpQuestion ? `<div style="background: rgba(155, 89, 182, 0.2); padding: 8px; border-radius: 8px; margin: 8px 0; border-left: 3px solid #9b59b6;">
                                <strong>🔄 Prochaine question:</strong> ${thought.followUpQuestion.substring(0, 100)}${thought.followUpQuestion.length > 100 ? '...' : ''}
                            </div>` : ''}
                            <div class="thought-content">${thought.content}</div>
                            ${thought.mobiusData ? `<div style="font-size: 0.8em; opacity: 0.7; margin-top: 5px;">
                                🌀 Position: ${(thought.mobiusData.position * 100).toFixed(1)}% | Phase: ${thought.mobiusData.phase} | Direction: ${thought.mobiusData.direction > 0 ? '→' : '←'}
                            </div>` : ''}
                            ${thought.thermalData ? `<div style="font-size: 0.8em; opacity: 0.7; margin-top: 3px;">
                                🌡️ Mémoire thermique: ${thought.thermalData.memoryTemp}°C | Entrées: ${thought.thermalData.totalEntries} | Efficacité: ${thought.thermalData.efficiency}%
                            </div>` : ''}
                            ${thought.stats ? `<div style="font-size: 0.8em; opacity: 0.7; margin-top: 3px;">
                                🧠 Neurones: ${thought.stats.neurons} | Temp: ${thought.stats.temperature}°C | QI: ${thought.stats.qi}
                            </div>` : ''}
                        `;
                        thoughtsList.appendChild(thoughtDiv);
                    });

                    // Mettre à jour le statut de réflexion
                    if (data.isReflecting) {
                        document.querySelector('.chat-title').textContent = '💬 Chat IA avec Bande de Möbius Active 🌀';
                    }
                } else {
                    // Afficher un message si aucune pensée réelle
                    const thoughtsList = document.getElementById('thoughtsList');
                    thoughtsList.innerHTML = `
                        <div class="thought-item">
                            <div class="thought-time">Bande de Möbius en cours...</div>
                            <div class="thought-content">🌀 LOUNA AI pense en bande de Möbius - cycle infini sans début ni fin. Activez l'écoute pour entendre ses pensées !</div>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Erreur chargement pensées Möbius:', error);
            }
        }

        // ENVOI DE MESSAGE
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            
            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('chatResponse').style.display = 'block';
                    document.getElementById('responseText').textContent = data.response;
                    
                    // Afficher les réflexions d'agent
                    if (data.agentReflections) {
                        const thoughtsList = document.getElementById('thoughtsList');
                        data.agentReflections.forEach(reflection => {
                            const thoughtDiv = document.createElement('div');
                            thoughtDiv.className = 'thought-item';
                            thoughtDiv.innerHTML = `
                                <div class="thought-time">[${reflection.time}] ${reflection.type}</div>
                                <div class="thought-content">${reflection.content}</div>
                            `;
                            thoughtsList.appendChild(thoughtDiv);
                        });
                    }
                }
                
                input.value = '';
            } catch (error) {
                console.error('Erreur envoi message:', error);
            }
        }

        // MISE À JOUR DES MÉTRIQUES
        async function updateMetrics() {
            try {
                const response = await fetch('/api/metrics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('neurons').textContent = data.brain?.activeNeurons?.toLocaleString() || '1,064,000';
                    document.getElementById('synapses').textContent = data.brain?.synapticConnections?.toLocaleString() || '7,448,000';
                    document.getElementById('temperature').textContent = (data.brain?.temperature || 37.0).toFixed(1) + '°C';
                    // Calculer un QI réaliste basé sur les performances
                    const baseQI = 100; // QI de base
                    const neuronBonus = Math.min(20, (data.brain?.activeNeurons || 1064000) / 100000); // Bonus neurones
                    const tempBonus = Math.max(0, Math.min(15, 40 - (data.brain?.temperature || 37))); // Bonus température optimale
                    const memoryBonus = Math.min(10, (data.brain?.memoryEfficiency || 0.85) * 10); // Bonus mémoire

                    const calculatedQI = Math.floor(baseQI + neuronBonus + tempBonus + memoryBonus);
                    const finalQI = Math.max(85, Math.min(165, calculatedQI)); // Limiter entre 85 et 165

                    document.getElementById('qi').textContent = finalQI;
                }
            } catch (error) {
                console.error('Erreur métriques:', error);
            }
        }

        // INITIALISATION
        document.addEventListener('DOMContentLoaded', () => {
            // Initialiser la synthèse vocale
            initializeVoice();

            // Charger les pensées et métriques
            loadContinuousThoughts();
            updateMetrics();

            // Actualisation automatique plus fréquente pour les pensées
            setInterval(loadContinuousThoughts, 3000); // Toutes les 3 secondes
            setInterval(updateMetrics, 10000);

            // Envoi avec Entrée
            document.getElementById('messageInput').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') sendMessage();
            });

            // Réinitialiser la voix quand les voix sont chargées
            if ('speechSynthesis' in window) {
                speechSynthesis.onvoiceschanged = initializeVoice;
            }
        });

        // 🌐 FONCTION POUR DÉMARRER LE DIALOGUE CHATGPT
        async function startChatGPTDialogue() {
            const btn = document.getElementById('chatgptBtn');

            try {
                btn.textContent = '⏳ Démarrage...';
                btn.disabled = true;

                const response = await fetch('/api/start-chatgpt-dialogue', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    btn.textContent = '🌐 Dialogue en cours...';
                    btn.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
                    console.log('🌐 Dialogue ChatGPT démarré !');

                    // Surveiller le statut du dialogue
                    monitorChatGPTDialogue();

                    // 🧠 AFFICHER LA ZONE D'APPRENTISSAGE
                    document.getElementById('chatgptLearningZone').style.display = 'block';

                    // Réactiver le bouton après 5 minutes
                    setTimeout(() => {
                        btn.textContent = '🌐 Dialoguer ChatGPT';
                        btn.style.background = 'linear-gradient(135deg, #00d4aa, #01a3a4)';
                        btn.disabled = false;
                        // Masquer la zone d'apprentissage
                        document.getElementById('chatgptLearningZone').style.display = 'none';
                    }, 5 * 60 * 1000); // 5 minutes

                } else {
                    btn.textContent = '🌐 Dialoguer ChatGPT';
                    btn.disabled = false;
                    alert('Erreur: ' + data.message);
                }

            } catch (error) {
                console.error('❌ Erreur dialogue ChatGPT:', error);
                btn.textContent = '🌐 Dialoguer ChatGPT';
                btn.disabled = false;
                alert('Erreur de connexion');
            }
        }

        // 🌐 SURVEILLER LE STATUT DU DIALOGUE CHATGPT
        function monitorChatGPTDialogue() {
            const interval = setInterval(async () => {
                try {
                    const response = await fetch('/api/chatgpt-dialogue-status');
                    const data = await response.json();

                    if (!data.isActive) {
                        clearInterval(interval);
                        const btn = document.getElementById('chatgptBtn');
                        btn.textContent = '🌐 Dialoguer ChatGPT';
                        btn.style.background = 'linear-gradient(135deg, #00d4aa, #01a3a4)';
                        btn.disabled = false;
                        console.log(`🌐 Dialogue terminé - ${data.questionsAsked} questions posées`);
                    }

                } catch (error) {
                    console.error('❌ Erreur surveillance dialogue:', error);
                    clearInterval(interval);
                }
            }, 5000); // Vérifier toutes les 5 secondes
        }

        // 🌐 METTRE À JOUR LE STATUT CHATGPT
        async function updateChatGPTStatus() {
            try {
                const response = await fetch('/api/chatgpt-dialogue-status');
                const data = await response.json();

                if (data.success && data.isActive) {
                    const duration = Math.floor(data.duration);
                    const minutes = Math.floor(duration / 60);
                    const seconds = duration % 60;

                    console.log(`🌐 ChatGPT actif: ${data.questionsAsked} questions en ${minutes}:${seconds.toString().padStart(2, '0')}`);
                }

            } catch (error) {
                // Erreur silencieuse pour ne pas polluer les logs
            }
        }

        // 🌐 VÉRIFIER SI ON ATTEND UNE RÉPONSE CHATGPT
        function checkForChatGPTWaiting(thoughts) {
            const waitingThought = thoughts.find(t => t.type === 'chatgpt_waiting_response');
            const chatgptSection = document.getElementById('chatgptResponseSection');

            if (waitingThought) {
                // Afficher l'interface de réponse
                chatgptSection.style.display = 'block';
                document.getElementById('chatgptCurrentQuestion').textContent =
                    `Question posée: ${waitingThought.chatgptQuestion}`;
            } else {
                // Masquer l'interface si pas d'attente
                const hasActiveDialogue = thoughts.some(t =>
                    t.type === 'chatgpt_question' ||
                    t.type === 'chatgpt_dialogue_start'
                );

                if (!hasActiveDialogue) {
                    chatgptSection.style.display = 'none';
                }
            }
        }

        // 🧠 SOUMETTRE LA RÉPONSE CHATGPT POUR APPRENTISSAGE ADAPTATIF
        async function submitChatGPTResponse() {
            const responseText = document.getElementById('chatgptResponseInput').value.trim();

            if (!responseText) {
                alert('Veuillez coller une réponse de ChatGPT');
                return;
            }

            try {
                const response = await fetch('/api/chatgpt-response', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ response: responseText })
                });

                const data = await response.json();

                if (data.success) {
                    // 🎉 AFFICHER LES RÉSULTATS D'APPRENTISSAGE
                    displayLearningResults(data.learning, data.nextQuestion);

                    // 🗑️ EFFACER LA ZONE DE TEXTE
                    document.getElementById('chatgptResponseInput').value = '';

                    console.log('🧠 LOUNA a appris et généré une nouvelle question !');
                    console.log('📊 Sujets:', data.learning.topics);
                    console.log('🎯 Prochaine question:', data.nextQuestion.substring(0, 80) + '...');
                } else {
                    alert('❌ Erreur: ' + data.message);
                }

            } catch (error) {
                console.error('❌ Erreur apprentissage:', error);
                alert('❌ Erreur de connexion');
            }
        }

        // 📊 AFFICHER LES RÉSULTATS D'APPRENTISSAGE
        function displayLearningResults(learning, nextQuestion) {
            const resultDiv = document.getElementById('learningResult');
            const detailsDiv = document.getElementById('learningDetails');
            const nextQuestionDiv = document.getElementById('nextQuestionText');

            // 📊 AFFICHER LES DÉTAILS D'APPRENTISSAGE
            detailsDiv.innerHTML = `
                <p><strong>🎯 Sujets identifiés:</strong> ${learning.topics.join(', ')}</p>
                <p><strong>🔑 Mots-clés:</strong> ${learning.keywords.join(', ')}</p>
                <p><strong>📈 Niveau d'apprentissage:</strong> ${(learning.learningLevel * 100).toFixed(1)}%</p>
                <p><strong>🧠 Complexité:</strong> ${learning.analysis.complexity}</p>
                <p><strong>💭 Sentiment:</strong> ${learning.analysis.sentiment}</p>
            `;

            // 🎯 AFFICHER LA PROCHAINE QUESTION
            nextQuestionDiv.textContent = nextQuestion;

            // 🎉 AFFICHER LE RÉSULTAT
            resultDiv.style.display = 'block';
        }

        // 🗑️ EFFACER LA RÉPONSE CHATGPT
        function clearChatGPTResponse() {
            document.getElementById('chatgptResponseInput').value = '';
            document.getElementById('learningResult').style.display = 'none';
        }
    </script>
</body>
</html>
