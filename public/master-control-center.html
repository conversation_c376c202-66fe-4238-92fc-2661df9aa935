<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎛️ LOUNA AI - Centre de Contrôle Maître</title>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
            overflow-x: auto;
        }

        .container {
            max-width: 1800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 3em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header .subtitle {
            font-size: 1.2em;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }

        .status-bar {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
        }

        .status-item {
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(0, 0, 0, 0.3);
            padding: 10px 20px;
            border-radius: 15px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-active {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }

        .status-inactive {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }

        .status-warning {
            background: #f39c12;
            box-shadow: 0 0 10px #f39c12;
        }

        .systems-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .system-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .system-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .system-card.critical {
            border-color: #e74c3c;
            box-shadow: 0 0 20px rgba(231, 76, 60, 0.3);
        }

        .system-card.warning {
            border-color: #f39c12;
            box-shadow: 0 0 20px rgba(243, 156, 18, 0.3);
        }

        .system-card.active {
            border-color: #00d4aa;
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
        }

        .system-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .system-title {
            font-size: 1.4em;
            font-weight: 700;
            color: #ff6b9d;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .system-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .system-metrics {
            margin-bottom: 20px;
        }

        .metric-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .metric-label {
            color: rgba(255, 255, 255, 0.8);
        }

        .metric-value {
            color: #00d4aa;
            font-weight: 600;
        }

        .metric-value.warning {
            color: #f39c12;
        }

        .metric-value.critical {
            color: #e74c3c;
        }

        .system-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .action-btn {
            flex: 1;
            min-width: 100px;
            padding: 8px 12px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            color: white;
        }

        .action-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            color: white;
        }

        .action-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .action-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .global-controls {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .global-controls h3 {
            color: #ff6b9d;
            margin-bottom: 20px;
            font-size: 1.4em;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .control-btn.emergency {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            font-size: 1.1em;
            padding: 20px;
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .alert-banner {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 10px;
            animation: pulse 2s infinite;
        }

        .alert-banner.show {
            display: flex;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #00ff00);
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .progress-fill.warning {
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .progress-fill.critical {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        @media (max-width: 768px) {
            .systems-grid {
                grid-template-columns: 1fr;
            }

            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .status-bar {
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <button class="nav-btn" onclick="navigateHome()">🏠 Accueil</button>
        <button class="nav-btn" onclick="navigateChat()">💬 Chat</button>
        <button class="nav-btn" onclick="navigateBrain()">🧠 Cerveau</button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🎛️ Centre de Contrôle Maître</h1>
            <div class="subtitle">Supervision complète de tous les systèmes LOUNA AI</div>

            <!-- Barre de statut globale -->
            <div class="status-bar">
                <div class="status-item">
                    <span class="status-indicator status-active" id="globalStatus"></span>
                    <span>Système Global</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active" id="deepseekStatus"></span>
                    <span>DeepSeek R1 8B</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active" id="memoryStatus"></span>
                    <span>Mémoire Thermique</span>
                </div>
                <div class="status-item">
                    <span class="status-indicator status-active" id="performanceStatus"></span>
                    <span>Performance</span>
                </div>
            </div>
        </div>

        <!-- Alerte globale -->
        <div class="alert-banner" id="alertBanner">
            <span>🚨</span>
            <span id="alertMessage">Alerte système détectée</span>
        </div>

        <!-- Contrôles globaux -->
        <div class="global-controls">
            <h3>🎛️ Contrôles Globaux</h3>
            <div class="controls-grid">
                <button class="control-btn" onclick="refreshAllSystems()">🔄 Actualiser Tout</button>
                <button class="control-btn" onclick="optimizeAllSystems()">⚡ Optimiser Tout</button>
                <button class="control-btn" onclick="backupAllData()">💾 Sauvegarder Tout</button>
                <button class="control-btn" onclick="generateGlobalReport()">📊 Rapport Global</button>
                <button class="control-btn emergency" onclick="emergencyShutdown()">🚨 ARRÊT D'URGENCE</button>
            </div>
        </div>

        <!-- Grille des systèmes -->
        <div class="systems-grid">
            <!-- DeepSeek R1 8B -->
            <div class="system-card active" id="deepseekCard">
                <div class="system-header">
                    <div class="system-title">
                        🤖 DeepSeek R1 8B
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-active" id="deepseekIndicator"></span>
                        <span id="deepseekStatusText">Connecté</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Temps de réponse:</span>
                        <span class="metric-value" id="deepseekResponseTime">1582ms</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Qualité:</span>
                        <span class="metric-value" id="deepseekQuality">95%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Réflexions:</span>
                        <span class="metric-value" id="deepseekReflections">Actives</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Agent gardien:</span>
                        <span class="metric-value" id="deepseekGuardian">Actif</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn success" onclick="testDeepSeek()">🔗 Tester</button>
                    <button class="action-btn warning" onclick="disconnectDeepSeek()">⏸️ Déconnecter</button>
                    <button class="action-btn danger" onclick="emergencyStopDeepSeek()">🚨 Urgence</button>
                </div>
            </div>

            <!-- Mémoire Thermique -->
            <div class="system-card active" id="memoryCard">
                <div class="system-header">
                    <div class="system-title">
                        🧠 Mémoire Thermique
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-active" id="memoryIndicator"></span>
                        <span id="memoryStatusText">Opérationnelle</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Neurones actifs:</span>
                        <span class="metric-value" id="memoryNeurons">1,064,000</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Température:</span>
                        <span class="metric-value" id="memoryTemperature">37.0°C</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Efficacité:</span>
                        <span class="metric-value" id="memoryEfficiency">95%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Dernière sauvegarde:</span>
                        <span class="metric-value" id="memoryLastBackup">2s</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn success" onclick="backupMemory()">💾 Sauvegarder</button>
                    <button class="action-btn primary" onclick="optimizeMemory()">⚡ Optimiser</button>
                    <button class="action-btn warning" onclick="clearMemoryCache()">🧹 Nettoyer</button>
                </div>
            </div>

            <!-- Système de Plugins -->
            <div class="system-card active" id="pluginsCard">
                <div class="system-header">
                    <div class="system-title">
                        🔌 Gestionnaire Plugins
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-active" id="pluginsIndicator"></span>
                        <span id="pluginsStatusText">4 Actifs</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Plugins totaux:</span>
                        <span class="metric-value" id="pluginsTotal">4</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Plugins actifs:</span>
                        <span class="metric-value" id="pluginsActive">3</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Hooks actifs:</span>
                        <span class="metric-value" id="pluginsHooks">8</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Performance:</span>
                        <span class="metric-value" id="pluginsPerformance">Excellente</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn primary" onclick="openPluginsManager()">🔌 Gérer</button>
                    <button class="action-btn success" onclick="refreshPlugins()">🔄 Actualiser</button>
                    <button class="action-btn warning" onclick="disableAllPlugins()">⏸️ Désactiver</button>
                </div>
            </div>

            <!-- Moniteur Performance -->
            <div class="system-card warning" id="performanceCard">
                <div class="system-header">
                    <div class="system-title">
                        📊 Moniteur Performance
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-warning" id="performanceIndicator"></span>
                        <span id="performanceStatusText">Alertes Mémoire</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">CPU:</span>
                        <span class="metric-value warning" id="performanceCPU">75%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Mémoire:</span>
                        <span class="metric-value critical" id="performanceMemory">85%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Disque:</span>
                        <span class="metric-value" id="performanceDisk">60%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">FPS Interface:</span>
                        <span class="metric-value" id="performanceFPS">58</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn primary" onclick="openPerformanceDashboard()">📊 Dashboard</button>
                    <button class="action-btn success" onclick="optimizePerformance()">⚡ Optimiser</button>
                    <button class="action-btn danger" onclick="emergencyOptimization()">🚨 Urgence</button>
                </div>
            </div>

            <!-- Synchronisation Cloud -->
            <div class="system-card" id="cloudCard">
                <div class="system-header">
                    <div class="system-title">
                        ☁️ Synchronisation Cloud
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-inactive" id="cloudIndicator"></span>
                        <span id="cloudStatusText">Désactivée</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Dernière sync:</span>
                        <span class="metric-value" id="cloudLastSync">Jamais</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Fichiers sync:</span>
                        <span class="metric-value" id="cloudFiles">0</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Taille données:</span>
                        <span class="metric-value" id="cloudSize">0 MB</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Statut:</span>
                        <span class="metric-value" id="cloudStatus">Inactif</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn success" onclick="enableCloudSync()">☁️ Activer</button>
                    <button class="action-btn primary" onclick="configureCloud()">⚙️ Configurer</button>
                    <button class="action-btn warning" onclick="forceSync()">🔄 Forcer Sync</button>
                </div>
            </div>

            <!-- Système Vocal -->
            <div class="system-card" id="voiceCard">
                <div class="system-header">
                    <div class="system-title">
                        🎤 Système Vocal
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-inactive" id="voiceIndicator"></span>
                        <span id="voiceStatusText">Désactivé</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Reconnaissance:</span>
                        <span class="metric-value" id="voiceRecognition">Inactive</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Synthèse:</span>
                        <span class="metric-value" id="voiceSynthesis">Inactive</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Commandes:</span>
                        <span class="metric-value" id="voiceCommands">12</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Langue:</span>
                        <span class="metric-value" id="voiceLanguage">Français</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn success" onclick="enableVoice()">🎤 Activer</button>
                    <button class="action-btn primary" onclick="testVoice()">🗣️ Tester</button>
                    <button class="action-btn warning" onclick="configureVoice()">⚙️ Configurer</button>
                </div>
            </div>

            <!-- Machine Learning -->
            <div class="system-card active" id="mlCard">
                <div class="system-header">
                    <div class="system-title">
                        🧠 Machine Learning
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-active" id="mlIndicator"></span>
                        <span id="mlStatusText">Entraînement</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Modèles actifs:</span>
                        <span class="metric-value" id="mlModels">4</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Accuracy moyenne:</span>
                        <span class="metric-value" id="mlAccuracy">87%</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Points de données:</span>
                        <span class="metric-value" id="mlDataPoints">15,420</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Prédictions:</span>
                        <span class="metric-value" id="mlPredictions">2,847</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn primary" onclick="trainModels()">🎓 Entraîner</button>
                    <button class="action-btn success" onclick="viewMLMetrics()">📊 Métriques</button>
                    <button class="action-btn warning" onclick="resetMLData()">🔄 Reset</button>
                </div>
            </div>

            <!-- Mise à Jour Système -->
            <div class="system-card" id="updaterCard">
                <div class="system-header">
                    <div class="system-title">
                        🔄 Système Mise à Jour
                    </div>
                    <div class="system-status">
                        <span class="status-indicator status-active" id="updaterIndicator"></span>
                        <span id="updaterStatusText">À jour</span>
                    </div>
                </div>

                <div class="system-metrics">
                    <div class="metric-row">
                        <span class="metric-label">Version actuelle:</span>
                        <span class="metric-value" id="updaterVersion">v3.0.0</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Dernière vérif:</span>
                        <span class="metric-value" id="updaterLastCheck">5 min</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Canal:</span>
                        <span class="metric-value" id="updaterChannel">Stable</span>
                    </div>
                    <div class="metric-row">
                        <span class="metric-label">Auto-update:</span>
                        <span class="metric-value" id="updaterAuto">Activé</span>
                    </div>
                </div>

                <div class="system-actions">
                    <button class="action-btn primary" onclick="checkUpdates()">🔍 Vérifier</button>
                    <button class="action-btn success" onclick="configureUpdater()">⚙️ Configurer</button>
                    <button class="action-btn warning" onclick="forceUpdate()">⬇️ Forcer MAJ</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;
        let updateInterval = null;
        let systemsData = {};

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎛️ Centre de Contrôle Maître chargé');

            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            initializeMasterControl();
            startRealTimeUpdates();
        });

        // Navigation
        function navigateHome() {
            window.location.href = '/electron-optimized-interface.html';
        }

        function navigateChat() {
            window.location.href = '/louna-interface-nouvelle.html';
        }

        function navigateBrain() {
            window.location.href = '/brain-3d-spectacular.html';
        }

        // Initialiser le centre de contrôle
        async function initializeMasterControl() {
            await updateAllSystems();
            checkSystemAlerts();
        }

        // Mettre à jour tous les systèmes
        async function updateAllSystems() {
            try {
                if (electronAPI) {
                    // Récupérer les données de tous les systèmes
                    const results = await Promise.allSettled([
                        electronAPI.invoke('deepseek-status'),
                        electronAPI.invoke('get-thermal-memory-status'),
                        electronAPI.invoke('get-plugins-list'),
                        electronAPI.invoke('get-performance-metrics'),
                        electronAPI.invoke('get-cloud-sync-status'),
                        electronAPI.invoke('get-voice-status'),
                        electronAPI.invoke('get-ml-metrics'),
                        electronAPI.invoke('get-updater-status')
                    ]);

                    // Traiter les résultats
                    updateDeepSeekStatus(results[0].value);
                    updateMemoryStatus(results[1].value);
                    updatePluginsStatus(results[2].value);
                    updatePerformanceStatus(results[3].value);
                    updateCloudStatus(results[4].value);
                    updateVoiceStatus(results[5].value);
                    updateMLStatus(results[6].value);
                    updateUpdaterStatus(results[7].value);
                } else {
                    // Mode simulation
                    updateMockData();
                }

                updateGlobalStatus();
            } catch (error) {
                console.error('Erreur mise à jour systèmes:', error);
                showAlert('Erreur de communication avec les systèmes');
            }
        }

        // Mettre à jour le statut DeepSeek
        function updateDeepSeekStatus(data) {
            if (!data || !data.success) return;

            const status = data.status;
            const indicator = document.getElementById('deepseekIndicator');
            const statusText = document.getElementById('deepseekStatusText');
            const card = document.getElementById('deepseekCard');

            if (status.connected) {
                indicator.className = 'status-indicator status-active';
                statusText.textContent = 'Connecté';
                card.className = 'system-card active';
            } else {
                indicator.className = 'status-indicator status-inactive';
                statusText.textContent = 'Déconnecté';
                card.className = 'system-card';
            }

            document.getElementById('deepseekResponseTime').textContent = (status.responseTime || 1582) + 'ms';
            document.getElementById('deepseekQuality').textContent = Math.round((status.quality || 0.95) * 100) + '%';
        }

        // Mettre à jour le statut mémoire
        function updateMemoryStatus(data) {
            if (!data || !data.success) return;

            const memory = data.memory;
            document.getElementById('memoryNeurons').textContent = (memory.neurons || 1064000).toLocaleString();
            document.getElementById('memoryTemperature').textContent = (memory.temperature || 37.0) + '°C';
            document.getElementById('memoryEfficiency').textContent = Math.round((memory.efficiency || 0.95) * 100) + '%';
        }

        // Mettre à jour le statut plugins
        function updatePluginsStatus(data) {
            if (!data || !data.success) return;

            const stats = data.statistics;
            document.getElementById('pluginsTotal').textContent = stats.total || 4;
            document.getElementById('pluginsActive').textContent = stats.enabled || 3;
            document.getElementById('pluginsHooks').textContent = stats.hooks || 8;
            document.getElementById('pluginsStatusText').textContent = `${stats.enabled || 3} Actifs`;
        }

        // Mettre à jour le statut performance
        function updatePerformanceStatus(data) {
            if (!data || !data.success) return;

            const metrics = data.metrics;
            const cpuElement = document.getElementById('performanceCPU');
            const memoryElement = document.getElementById('performanceMemory');
            const card = document.getElementById('performanceCard');

            const cpu = metrics.cpu || 75;
            const memory = metrics.memory || 85;

            cpuElement.textContent = Math.round(cpu) + '%';
            memoryElement.textContent = Math.round(memory) + '%';

            // Mettre à jour les classes selon les seuils
            cpuElement.className = cpu > 80 ? 'metric-value critical' : cpu > 60 ? 'metric-value warning' : 'metric-value';
            memoryElement.className = memory > 80 ? 'metric-value critical' : memory > 60 ? 'metric-value warning' : 'metric-value';

            // Mettre à jour la carte
            if (cpu > 80 || memory > 80) {
                card.className = 'system-card critical';
                document.getElementById('performanceStatusText').textContent = 'Critique';
                document.getElementById('performanceIndicator').className = 'status-indicator status-critical';
            } else if (cpu > 60 || memory > 60) {
                card.className = 'system-card warning';
                document.getElementById('performanceStatusText').textContent = 'Alertes';
                document.getElementById('performanceIndicator').className = 'status-indicator status-warning';
            } else {
                card.className = 'system-card active';
                document.getElementById('performanceStatusText').textContent = 'Normal';
                document.getElementById('performanceIndicator').className = 'status-indicator status-active';
            }

            document.getElementById('performanceDisk').textContent = Math.round(metrics.disk || 60) + '%';
            document.getElementById('performanceFPS').textContent = Math.round(metrics.fps || 58);
        }

        // Mettre à jour le statut cloud
        function updateCloudStatus(data) {
            const indicator = document.getElementById('cloudIndicator');
            const statusText = document.getElementById('cloudStatusText');

            if (data && data.success && data.enabled) {
                indicator.className = 'status-indicator status-active';
                statusText.textContent = 'Actif';
                document.getElementById('cloudCard').className = 'system-card active';
            } else {
                indicator.className = 'status-indicator status-inactive';
                statusText.textContent = 'Désactivé';
                document.getElementById('cloudCard').className = 'system-card';
            }
        }

        // Mettre à jour le statut vocal
        function updateVoiceStatus(data) {
            const indicator = document.getElementById('voiceIndicator');
            const statusText = document.getElementById('voiceStatusText');

            if (data && data.success && data.enabled) {
                indicator.className = 'status-indicator status-active';
                statusText.textContent = 'Actif';
                document.getElementById('voiceCard').className = 'system-card active';
            } else {
                indicator.className = 'status-indicator status-inactive';
                statusText.textContent = 'Désactivé';
                document.getElementById('voiceCard').className = 'system-card';
            }
        }

        // Mettre à jour le statut ML
        function updateMLStatus(data) {
            if (!data || !data.success) return;

            const metrics = data.metrics;
            document.getElementById('mlModels').textContent = Object.keys(metrics.models || {}).length;
            document.getElementById('mlAccuracy').textContent = Math.round((metrics.averageAccuracy || 0.87) * 100) + '%';
            document.getElementById('mlDataPoints').textContent = (metrics.dataPoints || 15420).toLocaleString();
            document.getElementById('mlPredictions').textContent = (metrics.totalPredictions || 2847).toLocaleString();

            if (metrics.isTraining) {
                document.getElementById('mlStatusText').textContent = 'Entraînement';
            } else {
                document.getElementById('mlStatusText').textContent = 'Actif';
            }
        }

        // Mettre à jour le statut updater
        function updateUpdaterStatus(data) {
            if (!data || !data.success) return;

            const state = data.state;
            document.getElementById('updaterVersion').textContent = state.currentVersion || 'v3.0.0';
            document.getElementById('updaterChannel').textContent = state.channel || 'Stable';
            document.getElementById('updaterAuto').textContent = state.autoCheck ? 'Activé' : 'Désactivé';
        }

        // Mettre à jour avec des données simulées
        function updateMockData() {
            // Simulation des données pour développement
            updateDeepSeekStatus({
                success: true,
                status: { connected: true, responseTime: 1582, quality: 0.95 }
            });

            updateMemoryStatus({
                success: true,
                memory: { neurons: 1064000, temperature: 37.0, efficiency: 0.95 }
            });

            updatePluginsStatus({
                success: true,
                statistics: { total: 4, enabled: 3, hooks: 8 }
            });

            updatePerformanceStatus({
                success: true,
                metrics: { cpu: 75, memory: 85, disk: 60, fps: 58 }
            });
        }

        // Mettre à jour le statut global
        function updateGlobalStatus() {
            const globalIndicator = document.getElementById('globalStatus');
            const deepseekIndicator = document.getElementById('deepseekStatus');
            const memoryIndicator = document.getElementById('memoryStatus');
            const performanceIndicator = document.getElementById('performanceStatus');

            // Logique de statut global basée sur les sous-systèmes
            const hasErrors = document.querySelectorAll('.system-card.critical').length > 0;
            const hasWarnings = document.querySelectorAll('.system-card.warning').length > 0;

            if (hasErrors) {
                globalIndicator.className = 'status-indicator status-critical';
            } else if (hasWarnings) {
                globalIndicator.className = 'status-indicator status-warning';
            } else {
                globalIndicator.className = 'status-indicator status-active';
            }
        }

        // Vérifier les alertes système
        function checkSystemAlerts() {
            const criticalCards = document.querySelectorAll('.system-card.critical');

            if (criticalCards.length > 0) {
                showAlert(`${criticalCards.length} système(s) en état critique`);
            }
        }

        // Afficher une alerte
        function showAlert(message) {
            const banner = document.getElementById('alertBanner');
            const messageElement = document.getElementById('alertMessage');

            messageElement.textContent = message;
            banner.classList.add('show');

            setTimeout(() => {
                banner.classList.remove('show');
            }, 10000);
        }

        // Contrôles globaux
        async function refreshAllSystems() {
            showInfo('Actualisation de tous les systèmes...');
            await updateAllSystems();
            showSuccess('Tous les systèmes actualisés');
        }

        async function optimizeAllSystems() {
            showInfo('Optimisation globale en cours...');

            try {
                if (electronAPI) {
                    await Promise.all([
                        electronAPI.invoke('optimize-memory'),
                        electronAPI.invoke('optimize-performance'),
                        electronAPI.invoke('optimize-ml-models')
                    ]);
                }

                showSuccess('Optimisation globale terminée');
                await updateAllSystems();
            } catch (error) {
                showError('Erreur optimisation: ' + error.message);
            }
        }

        async function backupAllData() {
            showInfo('Sauvegarde globale en cours...');

            try {
                if (electronAPI) {
                    await Promise.all([
                        electronAPI.invoke('thermal-memory-backup'),
                        electronAPI.invoke('backup-plugins-data'),
                        electronAPI.invoke('backup-ml-models')
                    ]);
                }

                showSuccess('Sauvegarde globale terminée');
            } catch (error) {
                showError('Erreur sauvegarde: ' + error.message);
            }
        }

        async function generateGlobalReport() {
            showInfo('Génération du rapport global...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('generate-global-report');
                    if (result.success) {
                        showSuccess('Rapport global généré: ' + result.reportPath);
                    }
                }
            } catch (error) {
                showError('Erreur génération rapport: ' + error.message);
            }
        }

        async function emergencyShutdown() {
            if (!confirm('⚠️ ARRÊT D\'URGENCE GLOBAL\n\nCeci va arrêter tous les systèmes LOUNA AI.\nÊtes-vous absolument sûr ?')) {
                return;
            }

            showAlert('🚨 ARRÊT D\'URGENCE ACTIVÉ');

            try {
                if (electronAPI) {
                    await electronAPI.invoke('emergency-shutdown-all');
                }

                setTimeout(() => {
                    if (electronAPI) {
                        electronAPI.invoke('quit-application');
                    }
                }, 3000);
            } catch (error) {
                showError('Erreur arrêt urgence: ' + error.message);
            }
        }

        // Actions spécifiques aux systèmes
        async function testDeepSeek() {
            showInfo('Test de connexion DeepSeek...');
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-chat', 'Test de connexion depuis le centre de contrôle');
                    if (result.success) {
                        showSuccess('Test DeepSeek réussi');
                    }
                }
            } catch (error) {
                showError('Erreur test DeepSeek: ' + error.message);
            }
        }

        async function disconnectDeepSeek() {
            if (confirm('Déconnecter DeepSeek R1 8B ?')) {
                try {
                    if (electronAPI) {
                        await electronAPI.invoke('deepseek-disconnect');
                        showSuccess('DeepSeek déconnecté');
                        await updateAllSystems();
                    }
                } catch (error) {
                    showError('Erreur déconnexion: ' + error.message);
                }
            }
        }

        async function emergencyStopDeepSeek() {
            if (confirm('⚠️ ARRÊT D\'URGENCE DEEPSEEK ?')) {
                try {
                    if (electronAPI) {
                        await electronAPI.invoke('deepseek-emergency-stop');
                        showAlert('Arrêt d\'urgence DeepSeek activé');
                        await updateAllSystems();
                    }
                } catch (error) {
                    showError('Erreur arrêt urgence: ' + error.message);
                }
            }
        }

        // Navigation vers interfaces spécialisées
        function openPluginsManager() {
            window.location.href = '/plugins-manager.html';
        }

        function openPerformanceDashboard() {
            window.location.href = '/performance-dashboard.html';
        }

        // Notifications
        function showSuccess(message) {
            console.log('✅', message);
        }

        function showError(message) {
            console.error('❌', message);
            alert('Erreur: ' + message);
        }

        function showInfo(message) {
            console.log('ℹ️', message);
        }

        // Démarrer les mises à jour temps réel
        function startRealTimeUpdates() {
            updateInterval = setInterval(() => {
                updateAllSystems();
            }, 5000); // Toutes les 5 secondes
        }

        // Nettoyage
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>