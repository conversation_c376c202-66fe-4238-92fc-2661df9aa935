<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🌐 MCP Electron - LOUNA AI Ultra-Autonome</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1 {
            font-size: 1.5em;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .mcp-status {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(0, 255, 0, 0.2);
            padding: 5px 12px;
            border-radius: 15px;
            border: 1px solid #00ff00;
            font-size: 0.9em;
        }

        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #00ff00;
            box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-container {
            height: calc(100vh - 70px);
            display: flex;
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .mcp-controls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .control-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .control-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .control-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            font-size: 0.8em;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .status-value {
            color: #00d4aa;
            font-weight: 600;
        }

        .status-value.warning {
            color: #f39c12;
        }

        .status-value.error {
            color: #e74c3c;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
        }

        .message {
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 10px;
            max-width: 80%;
            word-wrap: break-word;
        }

        .user-message {
            background: rgba(255, 107, 157, 0.2);
            border: 1px solid #ff6b9d;
            margin-left: auto;
            text-align: right;
        }

        .ai-message {
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
            margin-right: auto;
        }

        .system-message {
            background: rgba(243, 156, 18, 0.2);
            border: 1px solid #f39c12;
            margin: 0 auto;
            text-align: center;
            max-width: 60%;
        }

        .message-header {
            font-size: 0.8em;
            opacity: 0.8;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .message-content {
            line-height: 1.4;
        }

        .mcp-badge {
            background: rgba(0, 255, 0, 0.3);
            border: 1px solid #00ff00;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 0.7em;
            color: #00ff00;
        }

        .chat-input-area {
            background: rgba(0, 0, 0, 0.4);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 20px;
        }

        .quick-actions {
            display: flex;
            gap: 8px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .quick-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 5px 10px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 15px;
            border-radius: 20px;
            font-size: 1em;
            outline: none;
            font-family: inherit;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .chat-input:focus {
            border-color: #00d4aa;
            box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
        }

        .send-btn {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            border: none;
            color: white;
            padding: 12px 20px;
            border-radius: 20px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .send-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 212, 170, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 10px 15px;
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
            border-radius: 10px;
            margin-bottom: 15px;
            max-width: 200px;
        }

        .typing-dots {
            display: flex;
            gap: 4px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #00d4aa;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 200px;
            }
            
            .nav-buttons {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <h1>🌐 MCP Electron</h1>
            <div class="mcp-status">
                <span class="status-indicator" id="mcpIndicator"></span>
                <span id="mcpStatusText">Mode MCP</span>
            </div>
        </div>
        <div class="nav-buttons">
            <a href="/electron-optimized-interface.html" class="nav-btn">🏠 Accueil</a>
            <a href="/test-reactivation-simple.html" class="nav-btn">🧪 Tests</a>
            <a href="/master-control-center.html" class="nav-btn">🎛️ Contrôle</a>
        </div>
    </div>

    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3>🎛️ Contrôles MCP</h3>
            <div class="mcp-controls">
                <button class="control-btn success" onclick="startMCPDialogue()">🚀 Démarrer MCP</button>
                <button class="control-btn warning" onclick="testMCPConnection()">🧪 Tester MCP</button>
                <button class="control-btn" onclick="openChatGPT()">🌐 Ouvrir ChatGPT</button>
                <button class="control-btn danger" onclick="stopMCPDialogue()">⏹️ Arrêter MCP</button>
            </div>

            <h3>📊 Statut Système</h3>
            <div class="status-panel">
                <div class="status-item">
                    <span>DeepSeek:</span>
                    <span class="status-value" id="deepseekStatus">Connecté</span>
                </div>
                <div class="status-item">
                    <span>MCP:</span>
                    <span class="status-value" id="mcpStatus">Inactif</span>
                </div>
                <div class="status-item">
                    <span>Neurones:</span>
                    <span class="status-value" id="neuronsCount">1,064,000</span>
                </div>
                <div class="status-item">
                    <span>Température:</span>
                    <span class="status-value" id="temperature">37.0°C</span>
                </div>
                <div class="status-item">
                    <span>QI Total:</span>
                    <span class="status-value" id="qiTotal">450+</span>
                </div>
            </div>

            <h3>🔧 Actions Rapides</h3>
            <div class="mcp-controls">
                <button class="control-btn" onclick="reactivateAllSystems()">🔄 Réactiver Tout</button>
                <button class="control-btn" onclick="backupMemory()">💾 Sauvegarder</button>
                <button class="control-btn warning" onclick="emergencyStop()">🚨 Arrêt Urgence</button>
            </div>
        </div>

        <!-- Chat Area -->
        <div class="chat-area">
            <div class="chat-messages" id="chatMessages">
                <div class="message system-message">
                    <div class="message-header">
                        <span>🌐 Système MCP</span>
                        <span id="currentTime"></span>
                    </div>
                    <div class="message-content">
                        <strong>Mode MCP (Model Context Protocol) Initialisé</strong><br>
                        Interface Electron intégrée avec toutes les fonctionnalités LOUNA AI.<br>
                        Prêt pour dialogue autonome et tests de réactivation.
                    </div>
                </div>

                <div class="typing-indicator" id="typingIndicator">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="font-size: 0.8em;">LOUNA réfléchit</span>
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="chat-input-area">
                <div class="quick-actions">
                    <button class="quick-btn" onclick="sendQuickMessage('Test de connexion MCP')">🧪 Test MCP</button>
                    <button class="quick-btn" onclick="sendQuickMessage('Démarrer dialogue ChatGPT automatique')">🌐 ChatGPT Auto</button>
                    <button class="quick-btn" onclick="sendQuickMessage('Statut de tous les systèmes')">📊 Statut Global</button>
                    <button class="quick-btn" onclick="sendQuickMessage('Optimiser les performances')">⚡ Optimiser</button>
                </div>

                <div class="input-container">
                    <input type="text" 
                           class="chat-input" 
                           id="chatInput" 
                           placeholder="Message MCP... (Electron intégré)"
                           maxlength="500">
                    <button class="send-btn" id="sendBtn" onclick="sendMessage()">
                        ➤ Envoyer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;
        let isLoading = false;
        let mcpActive = false;
        let updateInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🌐 Interface MCP Electron chargée');

            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            // Initialiser l'interface
            initializeMCPInterface();

            // Écouter la touche Entrée
            document.getElementById('chatInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Démarrer les mises à jour
            startRealTimeUpdates();
        });

        // Initialiser l'interface MCP
        async function initializeMCPInterface() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);

            await updateSystemStatus();

            // Message de bienvenue
            addSystemMessage('Interface MCP Electron initialisée avec succès !');
        }

        // Mettre à jour l'heure
        function updateCurrentTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleTimeString('fr-FR');
        }

        // Mettre à jour le statut système
        async function updateSystemStatus() {
            try {
                if (electronAPI) {
                    // Récupérer les statuts via Electron
                    const [deepseekStatus, memoryStats, mcpStatus] = await Promise.allSettled([
                        electronAPI.invoke('deepseek-status'),
                        electronAPI.invoke('thermal-memory-stats'),
                        electronAPI.invoke('mcp-status')
                    ]);

                    // Mettre à jour DeepSeek
                    if (deepseekStatus.status === 'fulfilled' && deepseekStatus.value.success) {
                        document.getElementById('deepseekStatus').textContent = 'Connecté';
                        document.getElementById('deepseekStatus').className = 'status-value';
                    } else {
                        document.getElementById('deepseekStatus').textContent = 'Déconnecté';
                        document.getElementById('deepseekStatus').className = 'status-value error';
                    }

                    // Mettre à jour mémoire
                    if (memoryStats.status === 'fulfilled' && memoryStats.value.success) {
                        const stats = memoryStats.value;
                        document.getElementById('neuronsCount').textContent = (stats.totalEntries || 1064000).toLocaleString();
                        document.getElementById('temperature').textContent = (stats.temperature || 37.0) + '°C';
                    }

                    // Mettre à jour MCP
                    if (mcpStatus.status === 'fulfilled' && mcpStatus.value.success) {
                        const mcp = mcpStatus.value;
                        mcpActive = mcp.status === 'active';
                        document.getElementById('mcpStatus').textContent = mcpActive ? 'Actif' : 'Inactif';
                        document.getElementById('mcpStatus').className = mcpActive ? 'status-value' : 'status-value warning';

                        // Mettre à jour l'indicateur
                        const indicator = document.getElementById('mcpIndicator');
                        const statusText = document.getElementById('mcpStatusText');
                        if (mcpActive) {
                            indicator.style.background = '#00ff00';
                            statusText.textContent = 'MCP Actif';
                        } else {
                            indicator.style.background = '#f39c12';
                            statusText.textContent = 'MCP Inactif';
                        }
                    }
                } else {
                    // Mode simulation
                    document.getElementById('deepseekStatus').textContent = 'Connecté (Serveur)';
                    document.getElementById('mcpStatus').textContent = 'Test Mode';
                    document.getElementById('mcpStatus').className = 'status-value warning';
                }
            } catch (error) {
                console.error('Erreur mise à jour statut:', error);
            }
        }

        // Démarrer dialogue MCP
        async function startMCPDialogue() {
            try {
                addSystemMessage('🚀 Démarrage du dialogue MCP...');

                if (electronAPI) {
                    const result = await electronAPI.invoke('mcp-start-dialogue', {
                        mode: 'electron-interface',
                        capabilities: ['internet-search', 'real-time-data', 'secure-vpn']
                    });

                    if (result.success) {
                        mcpActive = true;
                        addSystemMessage(`✅ Dialogue MCP démarré - ID: ${result.dialogueId}`);
                        await updateSystemStatus();
                    } else {
                        addSystemMessage('❌ Erreur démarrage MCP: ' + result.error);
                    }
                } else {
                    // Mode serveur
                    const response = await fetch('/api/start-chatgpt-dialogue', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    const data = await response.json();
                    if (data.success) {
                        addSystemMessage('✅ Dialogue MCP démarré via serveur');
                    } else {
                        addSystemMessage('❌ Erreur: ' + data.message);
                    }
                }
            } catch (error) {
                addSystemMessage('❌ Erreur démarrage MCP: ' + error.message);
            }
        }

        // Tester connexion MCP
        async function testMCPConnection() {
            try {
                addSystemMessage('🧪 Test de connexion MCP...');

                if (electronAPI) {
                    const result = await electronAPI.invoke('mcp-status');
                    if (result.success) {
                        addSystemMessage(`✅ Test MCP réussi - Statut: ${result.status}`);
                    } else {
                        addSystemMessage('❌ Test MCP échoué');
                    }
                } else {
                    const response = await fetch('/api/chatgpt-dialogue-status');
                    const data = await response.json();
                    addSystemMessage(`✅ Test serveur - Actif: ${data.isActive}`);
                }
            } catch (error) {
                addSystemMessage('❌ Erreur test MCP: ' + error.message);
            }
        }

        // Fonctions de chat et utilitaires
        async function sendMessage() {
            if (isLoading) return;

            const input = document.getElementById('chatInput');
            const message = input.value.trim();

            if (!message) return;

            isLoading = true;
            input.value = '';

            // Ajouter le message utilisateur
            addUserMessage(message);

            // Afficher l'indicateur de frappe
            showTypingIndicator();

            try {
                if (electronAPI) {
                    // Via Electron
                    const result = await electronAPI.invoke('mcp-submit-response', {
                        message: message,
                        includeInternet: true
                    });

                    hideTypingIndicator();

                    if (result.success) {
                        addAIMessage(result.response);
                    } else {
                        addAIMessage('❌ Erreur: ' + result.error);
                    }
                } else {
                    // Via serveur
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            mcpMode: true,
                            includeInternet: true
                        })
                    });

                    const data = await response.json();

                    hideTypingIndicator();

                    if (data.success && data.response) {
                        addAIMessage(data.response);
                    } else {
                        addAIMessage('❌ Erreur: ' + (data.error || 'Réponse non disponible'));
                    }
                }

            } catch (error) {
                hideTypingIndicator();
                addAIMessage('❌ Erreur de connexion: ' + error.message);
            }

            isLoading = false;
        }

        function sendQuickMessage(message) {
            document.getElementById('chatInput').value = message;
            sendMessage();
        }

        function addUserMessage(text) {
            addMessage(text, 'user');
        }

        function addAIMessage(text) {
            addMessage(text, 'ai', true);
        }

        function addSystemMessage(text) {
            addMessage(text, 'system');
        }

        function addMessage(text, type, isMCP = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');

            let className = 'message ';
            if (type === 'user') {
                className += 'user-message';
            } else if (type === 'ai') {
                className += 'ai-message';
            } else {
                className += 'system-message';
            }

            messageDiv.className = className;

            const timestamp = new Date().toLocaleTimeString('fr-FR');
            const senderName = type === 'user' ? 'Vous' : type === 'ai' ? 'LOUNA AI' : 'Système';
            const mcpBadge = isMCP ? '<span class="mcp-badge">MCP</span>' : '';

            messageDiv.innerHTML = `
                <div class="message-header">
                    <span>${senderName}</span>
                    <span>${timestamp} ${mcpBadge}</span>
                </div>
                <div class="message-content">${text}</div>
            `;

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
            document.getElementById('chatMessages').scrollTop = document.getElementById('chatMessages').scrollHeight;
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        // Actions système
        async function openChatGPT() {
            try {
                addSystemMessage('🌐 Ouverture de ChatGPT...');

                if (electronAPI) {
                    // Via Electron
                    const { shell } = require('electron');
                    await shell.openExternal('https://chat.openai.com');
                    addSystemMessage('✅ ChatGPT ouvert dans le navigateur');
                } else {
                    // Via serveur
                    window.open('https://chat.openai.com', '_blank');
                    addSystemMessage('✅ ChatGPT ouvert');
                }
            } catch (error) {
                addSystemMessage('❌ Erreur ouverture ChatGPT: ' + error.message);
            }
        }

        async function stopMCPDialogue() {
            try {
                addSystemMessage('⏹️ Arrêt du dialogue MCP...');

                if (electronAPI) {
                    mcpActive = false;
                    await updateSystemStatus();
                    addSystemMessage('✅ Dialogue MCP arrêté');
                } else {
                    const response = await fetch('/api/stop-chatgpt-dialogue', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' }
                    });

                    const data = await response.json();
                    addSystemMessage(data.success ? '✅ Dialogue arrêté' : '❌ Erreur arrêt');
                }
            } catch (error) {
                addSystemMessage('❌ Erreur arrêt MCP: ' + error.message);
            }
        }

        async function reactivateAllSystems() {
            try {
                addSystemMessage('🔄 Réactivation de tous les systèmes...');

                if (electronAPI) {
                    const result = await electronAPI.invoke('reactivate-all-systems');
                    if (result.success) {
                        addSystemMessage('✅ Réactivation terminée');
                        await updateSystemStatus();
                    } else {
                        addSystemMessage('❌ Erreur réactivation: ' + result.error);
                    }
                } else {
                    addSystemMessage('⚠️ Réactivation nécessite Electron');
                }
            } catch (error) {
                addSystemMessage('❌ Erreur réactivation: ' + error.message);
            }
        }

        async function backupMemory() {
            try {
                addSystemMessage('💾 Sauvegarde de la mémoire...');

                if (electronAPI) {
                    const result = await electronAPI.invoke('thermal-memory-backup');
                    if (result.success) {
                        addSystemMessage('✅ Sauvegarde réussie');
                    } else {
                        addSystemMessage('❌ Erreur sauvegarde');
                    }
                } else {
                    addSystemMessage('⚠️ Sauvegarde nécessite Electron');
                }
            } catch (error) {
                addSystemMessage('❌ Erreur sauvegarde: ' + error.message);
            }
        }

        async function emergencyStop() {
            if (!confirm('⚠️ ARRÊT D\'URGENCE\n\nCeci va arrêter tous les systèmes.\nContinuer ?')) {
                return;
            }

            try {
                addSystemMessage('🚨 ARRÊT D\'URGENCE ACTIVÉ');

                if (electronAPI) {
                    await electronAPI.invoke('emergency-shutdown-all');
                    addSystemMessage('🚨 Arrêt d\'urgence en cours...');
                } else {
                    addSystemMessage('⚠️ Arrêt d\'urgence nécessite Electron');
                }
            } catch (error) {
                addSystemMessage('❌ Erreur arrêt urgence: ' + error.message);
            }
        }

        // Mises à jour temps réel
        function startRealTimeUpdates() {
            updateInterval = setInterval(() => {
                updateSystemStatus();
            }, 5000); // Toutes les 5 secondes
        }

        // Nettoyage
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>