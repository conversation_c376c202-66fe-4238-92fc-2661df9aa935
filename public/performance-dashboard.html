<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📊 LOUNA AI - Tableau de Bord Performance</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .metric-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .metric-title {
            font-size: 1.2em;
            font-weight: 700;
            color: #ff6b9d;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metric-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-good {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }

        .status-warning {
            background: #f39c12;
            box-shadow: 0 0 10px #f39c12;
        }

        .status-critical {
            background: #e74c3c;
            box-shadow: 0 0 10px #e74c3c;
        }

        .metric-value {
            font-size: 3em;
            font-weight: 700;
            color: #00d4aa;
            text-align: center;
            margin-bottom: 10px;
        }

        .metric-label {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
        }

        .metric-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .metric-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #00ff00);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .metric-bar-fill.warning {
            background: linear-gradient(90deg, #f39c12, #e67e22);
        }

        .metric-bar-fill.critical {
            background: linear-gradient(90deg, #e74c3c, #c0392b);
        }

        .metric-details {
            font-size: 0.9em;
            color: rgba(255, 255, 255, 0.6);
        }

        .charts-section {
            margin-bottom: 30px;
        }

        .chart-container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 25px;
        }

        .chart-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff6b9d;
            text-align: center;
        }

        .chart-placeholder {
            width: 100%;
            height: 300px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.6);
            font-size: 1.1em;
        }

        .system-info {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .system-info h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
        }

        .info-label {
            color: rgba(255, 255, 255, 0.8);
        }

        .info-value {
            color: #00d4aa;
            font-weight: 600;
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .controls-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff6b9d;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .control-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        .control-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
        }

        .control-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .alert-banner {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            display: none;
            align-items: center;
            gap: 10px;
        }

        .alert-banner.show {
            display: flex;
        }

        @media (max-width: 768px) {
            .metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <button class="nav-btn" onclick="navigateHome()">🏠 Accueil</button>
        <button class="nav-btn" onclick="navigatePlugins()">🔌 Plugins</button>
        <button class="nav-btn" onclick="navigateAdvanced()">🎛️ Contrôles</button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>📊 Tableau de Bord Performance</h1>
            <p>Surveillance temps réel des performances système LOUNA AI</p>
        </div>

        <!-- Alerte -->
        <div class="alert-banner" id="alertBanner">
            <span>⚠️</span>
            <span id="alertMessage">Alerte performance détectée</span>
        </div>

        <!-- Métriques principales -->
        <div class="metrics-grid">
            <!-- CPU -->
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">
                        🖥️ Processeur
                    </div>
                    <div class="metric-status status-good" id="cpuStatus"></div>
                </div>
                <div class="metric-value" id="cpuValue">0%</div>
                <div class="metric-label">Utilisation CPU</div>
                <div class="metric-bar">
                    <div class="metric-bar-fill" id="cpuBar" style="width: 0%"></div>
                </div>
                <div class="metric-details" id="cpuDetails">
                    4 cœurs • 2.8 GHz
                </div>
            </div>

            <!-- Mémoire -->
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">
                        💾 Mémoire
                    </div>
                    <div class="metric-status status-good" id="memoryStatus"></div>
                </div>
                <div class="metric-value" id="memoryValue">0%</div>
                <div class="metric-label">Utilisation RAM</div>
                <div class="metric-bar">
                    <div class="metric-bar-fill" id="memoryBar" style="width: 0%"></div>
                </div>
                <div class="metric-details" id="memoryDetails">
                    0 GB / 0 GB utilisés
                </div>
            </div>

            <!-- Disque -->
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">
                        💿 Stockage
                    </div>
                    <div class="metric-status status-good" id="diskStatus"></div>
                </div>
                <div class="metric-value" id="diskValue">0%</div>
                <div class="metric-label">Utilisation Disque</div>
                <div class="metric-bar">
                    <div class="metric-bar-fill" id="diskBar" style="width: 0%"></div>
                </div>
                <div class="metric-details" id="diskDetails">
                    0 GB / 0 GB utilisés
                </div>
            </div>

            <!-- Performance -->
            <div class="metric-card">
                <div class="metric-header">
                    <div class="metric-title">
                        🎯 Performance
                    </div>
                    <div class="metric-status status-good" id="performanceStatus"></div>
                </div>
                <div class="metric-value" id="fpsValue">60</div>
                <div class="metric-label">FPS Interface</div>
                <div class="metric-bar">
                    <div class="metric-bar-fill" id="fpsBar" style="width: 100%"></div>
                </div>
                <div class="metric-details" id="performanceDetails">
                    Rendu: 16.7ms
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-title">📈 Historique des Performances (1 heure)</div>
                <div class="chart-placeholder" id="performanceChart">
                    Graphique temps réel des performances
                    <br><small>Intégration Chart.js en cours...</small>
                </div>
            </div>
        </div>

        <!-- Informations système -->
        <div class="system-info">
            <h3>ℹ️ Informations Système</h3>
            <div class="info-grid">
                <div class="info-item">
                    <span class="info-label">Plateforme:</span>
                    <span class="info-value" id="platform">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Architecture:</span>
                    <span class="info-value" id="architecture">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Node.js:</span>
                    <span class="info-value" id="nodeVersion">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Electron:</span>
                    <span class="info-value" id="electronVersion">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Uptime:</span>
                    <span class="info-value" id="uptime">-</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Charge moyenne:</span>
                    <span class="info-value" id="loadAverage">-</span>
                </div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls-panel">
            <div class="controls-title">🎛️ Contrôles Performance</div>
            <div class="controls-grid">
                <button class="control-btn success" onclick="optimizeMemory()">🧹 Optimiser Mémoire</button>
                <button class="control-btn" onclick="generateReport()">📊 Générer Rapport</button>
                <button class="control-btn warning" onclick="adjustPerformanceMode()">⚡ Mode Performance</button>
                <button class="control-btn" onclick="configureThresholds()">⚙️ Configurer Seuils</button>
                <button class="control-btn danger" onclick="emergencyOptimization()">🚨 Optimisation Urgence</button>
                <button class="control-btn" onclick="exportMetrics()">📤 Exporter Métriques</button>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;
        let updateInterval = null;
        let performanceData = [];

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📊 Interface tableau de bord performance chargée');
            
            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            initializeDashboard();
            startRealTimeUpdates();
        });

        // Navigation
        function navigateHome() {
            window.location.href = '/electron-optimized-interface.html';
        }

        function navigatePlugins() {
            window.location.href = '/plugins-manager.html';
        }

        function navigateAdvanced() {
            window.location.href = '/advanced-controls.html';
        }

        // Initialiser le tableau de bord
        async function initializeDashboard() {
            await updateMetrics();
            await loadSystemInfo();
        }

        // Mettre à jour les métriques
        async function updateMetrics() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('get-performance-metrics');
                    if (result.success) {
                        updateMetricsDisplay(result.metrics);
                    }
                } else {
                    // Mode simulation
                    updateMockMetrics();
                }
            } catch (error) {
                console.error('Erreur mise à jour métriques:', error);
            }
        }

        // Mettre à jour avec des données simulées
        function updateMockMetrics() {
            const metrics = {
                cpu: {
                    usage: 15 + Math.random() * 20,
                    cores: 4,
                    model: 'Intel Core i7',
                    speed: 2800
                },
                memory: {
                    percentage: 45 + Math.random() * 20,
                    used: 7.2,
                    total: 16
                },
                disk: {
                    percentage: 60 + Math.random() * 10,
                    used: 300,
                    total: 500
                },
                performance: {
                    fps: 58 + Math.random() * 4,
                    renderTime: 14 + Math.random() * 4
                }
            };

            updateMetricsDisplay(metrics);
        }

        // Afficher les métriques
        function updateMetricsDisplay(metrics) {
            // CPU
            updateMetricCard('cpu', metrics.cpu.usage, '%', 
                `${metrics.cpu.cores} cœurs • ${(metrics.cpu.speed / 1000).toFixed(1)} GHz`);

            // Mémoire
            updateMetricCard('memory', metrics.memory.percentage, '%',
                `${metrics.memory.used.toFixed(1)} GB / ${metrics.memory.total} GB utilisés`);

            // Disque
            updateMetricCard('disk', metrics.disk.percentage, '%',
                `${metrics.disk.used} GB / ${metrics.disk.total} GB utilisés`);

            // Performance
            updateMetricCard('fps', metrics.performance.fps, '', 
                `Rendu: ${metrics.performance.renderTime.toFixed(1)}ms`, 'performance');

            // Ajouter aux données historiques
            performanceData.push({
                timestamp: Date.now(),
                cpu: metrics.cpu.usage,
                memory: metrics.memory.percentage,
                fps: metrics.performance.fps
            });

            // Limiter l'historique
            if (performanceData.length > 100) {
                performanceData.shift();
            }
        }

        // Mettre à jour une carte de métrique
        function updateMetricCard(type, value, unit, details, statusType = type) {
            const valueElement = document.getElementById(`${type}Value`);
            const barElement = document.getElementById(`${type}Bar`);
            const statusElement = document.getElementById(`${statusType}Status`);
            const detailsElement = document.getElementById(`${type}Details`);

            if (valueElement) {
                valueElement.textContent = Math.round(value) + unit;
            }

            if (barElement) {
                barElement.style.width = Math.min(value, 100) + '%';
                
                // Couleur selon le niveau
                barElement.className = 'metric-bar-fill';
                if (value > 80) {
                    barElement.classList.add('critical');
                } else if (value > 60) {
                    barElement.classList.add('warning');
                }
            }

            if (statusElement) {
                statusElement.className = 'metric-status';
                if (value > 80) {
                    statusElement.classList.add('status-critical');
                    showAlert(`${type.toUpperCase()} critique: ${Math.round(value)}${unit}`);
                } else if (value > 60) {
                    statusElement.classList.add('status-warning');
                } else {
                    statusElement.classList.add('status-good');
                }
            }

            if (detailsElement && details) {
                detailsElement.textContent = details;
            }
        }

        // Charger les informations système
        async function loadSystemInfo() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('get-system-info');
                    if (result.success) {
                        updateSystemInfo(result.info);
                    }
                } else {
                    // Mode simulation
                    updateSystemInfo({
                        platform: 'darwin',
                        arch: 'x64',
                        nodeVersion: 'v18.17.0',
                        electronVersion: '25.3.1',
                        uptime: 3600,
                        loadAverage: [1.2, 1.5, 1.8]
                    });
                }
            } catch (error) {
                console.error('Erreur chargement infos système:', error);
            }
        }

        // Mettre à jour les informations système
        function updateSystemInfo(info) {
            document.getElementById('platform').textContent = info.platform;
            document.getElementById('architecture').textContent = info.arch;
            document.getElementById('nodeVersion').textContent = info.nodeVersion;
            document.getElementById('electronVersion').textContent = info.electronVersion;
            document.getElementById('uptime').textContent = formatUptime(info.uptime);
            document.getElementById('loadAverage').textContent = 
                info.loadAverage.map(load => load.toFixed(2)).join(', ');
        }

        // Formater le temps de fonctionnement
        function formatUptime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            return `${hours}h ${minutes}m`;
        }

        // Afficher une alerte
        function showAlert(message) {
            const banner = document.getElementById('alertBanner');
            const messageElement = document.getElementById('alertMessage');
            
            messageElement.textContent = message;
            banner.classList.add('show');
            
            setTimeout(() => {
                banner.classList.remove('show');
            }, 5000);
        }

        // Contrôles
        async function optimizeMemory() {
            showInfo('Optimisation mémoire en cours...');
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('optimize-memory');
                    if (result.success) {
                        showSuccess('Mémoire optimisée');
                    }
                } else {
                    setTimeout(() => showSuccess('Mémoire optimisée (simulation)'), 1000);
                }
            } catch (error) {
                showError('Erreur optimisation: ' + error.message);
            }
        }

        async function generateReport() {
            showInfo('Génération du rapport...');
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('generate-performance-report');
                    if (result.success) {
                        showSuccess('Rapport généré: ' + result.reportPath);
                    }
                } else {
                    setTimeout(() => showSuccess('Rapport généré (simulation)'), 1000);
                }
            } catch (error) {
                showError('Erreur génération rapport: ' + error.message);
            }
        }

        function adjustPerformanceMode() {
            const mode = prompt('Mode performance:\n1. High\n2. Normal\n3. Battery\n\nEntrez 1, 2 ou 3:');
            const modes = { '1': 'high', '2': 'normal', '3': 'battery' };
            
            if (modes[mode]) {
                showSuccess(`Mode ${modes[mode]} activé`);
            }
        }

        function configureThresholds() {
            alert('Configuration des seuils\n\nFonctionnalité en développement...');
        }

        async function emergencyOptimization() {
            if (!confirm('Lancer l\'optimisation d\'urgence ?')) return;
            
            showInfo('Optimisation d\'urgence en cours...');
            await optimizeMemory();
            showSuccess('Optimisation d\'urgence terminée');
        }

        function exportMetrics() {
            const data = JSON.stringify(performanceData, null, 2);
            const blob = new Blob([data], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-metrics-${Date.now()}.json`;
            a.click();
            
            URL.revokeObjectURL(url);
            showSuccess('Métriques exportées');
        }

        // Notifications
        function showSuccess(message) {
            console.log('✅', message);
        }

        function showError(message) {
            console.error('❌', message);
            alert('Erreur: ' + message);
        }

        function showInfo(message) {
            console.log('ℹ️', message);
        }

        // Démarrer les mises à jour temps réel
        function startRealTimeUpdates() {
            updateInterval = setInterval(() => {
                updateMetrics();
            }, 2000); // Toutes les 2 secondes
        }

        // Nettoyage
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
