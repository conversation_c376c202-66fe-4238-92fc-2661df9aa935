<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔌 LOUNA AI - Gestionnaire de Plugins</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .plugins-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .plugin-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
        }

        .plugin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
        }

        .plugin-card.enabled {
            border-color: #00d4aa;
            box-shadow: 0 0 20px rgba(0, 212, 170, 0.3);
        }

        .plugin-card.disabled {
            opacity: 0.6;
        }

        .plugin-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .plugin-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #ff6b9d;
        }

        .plugin-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .status-enabled {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }

        .status-disabled {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }

        .plugin-info {
            margin-bottom: 15px;
        }

        .plugin-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 10px;
            line-height: 1.4;
        }

        .plugin-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.8em;
            color: rgba(255, 255, 255, 0.6);
        }

        .plugin-category {
            background: rgba(255, 107, 157, 0.3);
            padding: 4px 8px;
            border-radius: 8px;
            font-size: 0.7em;
            text-transform: uppercase;
            font-weight: 600;
        }

        .plugin-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .plugin-btn {
            flex: 1;
            padding: 10px;
            border: none;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .btn-toggle {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-toggle.enabled {
            background: linear-gradient(135deg, #00d4aa, #00b894);
        }

        .btn-toggle.disabled {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-config {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-remove {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .plugin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }

        .stats-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: 700;
            color: #00d4aa;
            margin-bottom: 5px;
        }

        .stat-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9em;
        }

        .controls-panel {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }

        .controls-title {
            font-size: 1.4em;
            font-weight: 700;
            margin-bottom: 20px;
            color: #ff6b9d;
        }

        .controls-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .control-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 20px;
            border-radius: 15px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .control-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .control-btn.primary {
            background: linear-gradient(135deg, #ff6b9d, #c44569);
            box-shadow: 0 4px 15px rgba(255, 107, 157, 0.3);
        }

        .control-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            box-shadow: 0 4px 15px rgba(0, 212, 170, 0.3);
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: rgba(255, 255, 255, 0.6);
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid #ff6b9d;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .plugins-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <button class="nav-btn" onclick="navigateHome()">🏠 Accueil</button>
        <button class="nav-btn" onclick="navigateAdvanced()">🎛️ Contrôles</button>
        <button class="nav-btn" onclick="navigateChat()">💬 Chat</button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🔌 Gestionnaire de Plugins</h1>
            <p>Gérez et configurez les extensions de LOUNA AI</p>
        </div>

        <!-- Statistiques -->
        <div class="stats-panel">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-value" id="totalPlugins">0</div>
                    <div class="stat-label">Total Plugins</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="enabledPlugins">0</div>
                    <div class="stat-label">Activés</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="disabledPlugins">0</div>
                    <div class="stat-label">Désactivés</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="totalHooks">0</div>
                    <div class="stat-label">Hooks Actifs</div>
                </div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="controls-panel">
            <div class="controls-title">Actions Globales</div>
            <div class="controls-grid">
                <button class="control-btn success" onclick="refreshPlugins()">🔄 Actualiser</button>
                <button class="control-btn primary" onclick="installPlugin()">📦 Installer Plugin</button>
                <button class="control-btn" onclick="enableAllPlugins()">✅ Activer Tous</button>
                <button class="control-btn" onclick="disableAllPlugins()">❌ Désactiver Tous</button>
            </div>
        </div>

        <!-- Liste des plugins -->
        <div id="pluginsContainer">
            <div class="loading">
                <div class="spinner"></div>
                <p>Chargement des plugins...</p>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;
        let plugins = [];
        let updateInterval = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔌 Interface gestionnaire de plugins chargée');
            
            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            loadPlugins();
            startAutoRefresh();
        });

        // Navigation
        function navigateHome() {
            window.location.href = '/electron-optimized-interface.html';
        }

        function navigateAdvanced() {
            window.location.href = '/advanced-controls.html';
        }

        function navigateChat() {
            window.location.href = '/louna-interface-nouvelle.html';
        }

        // Charger les plugins
        async function loadPlugins() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('get-plugins-list');
                    if (result.success) {
                        plugins = result.plugins;
                        updateStatistics(result.statistics);
                        renderPlugins();
                    } else {
                        showError('Erreur chargement plugins: ' + result.error);
                    }
                } else {
                    // Mode simulation pour développement
                    loadMockPlugins();
                }
            } catch (error) {
                showError('Erreur: ' + error.message);
            }
        }

        // Charger des plugins de simulation
        function loadMockPlugins() {
            plugins = [
                {
                    id: 'voice-synthesizer',
                    name: 'Synthétiseur Vocal Avancé',
                    description: 'Synthèse vocale avec voix naturelles multiples',
                    version: '1.0.0',
                    author: 'LOUNA Team',
                    category: 'audio',
                    type: 'default',
                    enabled: true,
                    initialized: true
                },
                {
                    id: 'brain-visualizer',
                    name: 'Visualiseur Cerveau 3D',
                    description: 'Visualisation 3D interactive du cerveau avec WebGL',
                    version: '1.0.0',
                    author: 'LOUNA Team',
                    category: 'visualization',
                    type: 'default',
                    enabled: true,
                    initialized: true
                },
                {
                    id: 'auto-backup',
                    name: 'Sauvegarde Automatique',
                    description: 'Système de sauvegarde automatique avec versioning',
                    version: '1.0.0',
                    author: 'LOUNA Team',
                    category: 'utility',
                    type: 'default',
                    enabled: false,
                    initialized: false
                },
                {
                    id: 'performance-monitor',
                    name: 'Moniteur Performance',
                    description: 'Surveillance avancée des performances système',
                    version: '1.0.0',
                    author: 'LOUNA Team',
                    category: 'monitoring',
                    type: 'default',
                    enabled: true,
                    initialized: true
                }
            ];

            updateStatistics({
                total: 4,
                enabled: 3,
                disabled: 1,
                hooks: 8
            });

            renderPlugins();
        }

        // Mettre à jour les statistiques
        function updateStatistics(stats) {
            document.getElementById('totalPlugins').textContent = stats.total || 0;
            document.getElementById('enabledPlugins').textContent = stats.enabled || 0;
            document.getElementById('disabledPlugins').textContent = stats.disabled || 0;
            document.getElementById('totalHooks').textContent = stats.hooks || 0;
        }

        // Afficher les plugins
        function renderPlugins() {
            const container = document.getElementById('pluginsContainer');
            
            if (plugins.length === 0) {
                container.innerHTML = `
                    <div class="loading">
                        <p>Aucun plugin disponible</p>
                    </div>
                `;
                return;
            }

            const pluginsHTML = plugins.map(plugin => `
                <div class="plugin-card ${plugin.enabled ? 'enabled' : 'disabled'}">
                    <div class="plugin-header">
                        <div class="plugin-title">${plugin.name}</div>
                        <div class="plugin-status">
                            <span class="status-indicator ${plugin.enabled ? 'status-enabled' : 'status-disabled'}"></span>
                            ${plugin.enabled ? 'Activé' : 'Désactivé'}
                        </div>
                    </div>
                    
                    <div class="plugin-info">
                        <div class="plugin-description">${plugin.description}</div>
                        <div class="plugin-meta">
                            <span>v${plugin.version} • ${plugin.author}</span>
                            <span class="plugin-category">${plugin.category}</span>
                        </div>
                    </div>
                    
                    <div class="plugin-actions">
                        <button class="plugin-btn btn-toggle ${plugin.enabled ? 'enabled' : 'disabled'}" 
                                onclick="togglePlugin('${plugin.id}')">
                            ${plugin.enabled ? '⏸️ Désactiver' : '▶️ Activer'}
                        </button>
                        <button class="plugin-btn btn-config" onclick="configurePlugin('${plugin.id}')">
                            ⚙️ Configurer
                        </button>
                        ${plugin.type === 'user' ? `
                            <button class="plugin-btn btn-remove" onclick="removePlugin('${plugin.id}')">
                                🗑️ Supprimer
                            </button>
                        ` : ''}
                    </div>
                </div>
            `).join('');

            container.innerHTML = `<div class="plugins-grid">${pluginsHTML}</div>`;
        }

        // Basculer un plugin
        async function togglePlugin(pluginId) {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('toggle-plugin', pluginId);
                    if (result.success) {
                        await loadPlugins(); // Recharger
                        showSuccess(`Plugin ${result.enabled ? 'activé' : 'désactivé'}`);
                    } else {
                        showError('Erreur: ' + result.error);
                    }
                } else {
                    // Mode simulation
                    const plugin = plugins.find(p => p.id === pluginId);
                    if (plugin) {
                        plugin.enabled = !plugin.enabled;
                        plugin.initialized = plugin.enabled;
                        renderPlugins();
                        showSuccess(`Plugin ${plugin.enabled ? 'activé' : 'désactivé'}`);
                    }
                }
            } catch (error) {
                showError('Erreur: ' + error.message);
            }
        }

        // Configurer un plugin
        function configurePlugin(pluginId) {
            const plugin = plugins.find(p => p.id === pluginId);
            if (plugin) {
                alert(`Configuration du plugin: ${plugin.name}\n\nFonctionnalité en développement...`);
            }
        }

        // Supprimer un plugin
        async function removePlugin(pluginId) {
            if (!confirm('Voulez-vous vraiment supprimer ce plugin ?')) {
                return;
            }

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('remove-plugin', pluginId);
                    if (result.success) {
                        await loadPlugins();
                        showSuccess('Plugin supprimé');
                    } else {
                        showError('Erreur: ' + result.error);
                    }
                } else {
                    // Mode simulation
                    plugins = plugins.filter(p => p.id !== pluginId);
                    renderPlugins();
                    showSuccess('Plugin supprimé');
                }
            } catch (error) {
                showError('Erreur: ' + error.message);
            }
        }

        // Actions globales
        async function refreshPlugins() {
            showInfo('Actualisation des plugins...');
            await loadPlugins();
            showSuccess('Plugins actualisés');
        }

        function installPlugin() {
            alert('Installation de plugin\n\nFonctionnalité en développement...\n\nVous pourrez bientôt installer des plugins depuis des fichiers .zip ou des dépôts Git.');
        }

        async function enableAllPlugins() {
            if (!confirm('Activer tous les plugins ?')) return;
            
            for (const plugin of plugins) {
                if (!plugin.enabled) {
                    await togglePlugin(plugin.id);
                }
            }
            showSuccess('Tous les plugins activés');
        }

        async function disableAllPlugins() {
            if (!confirm('Désactiver tous les plugins ?')) return;
            
            for (const plugin of plugins) {
                if (plugin.enabled) {
                    await togglePlugin(plugin.id);
                }
            }
            showSuccess('Tous les plugins désactivés');
        }

        // Notifications
        function showSuccess(message) {
            console.log('✅', message);
            // Intégrer avec le système de notifications
        }

        function showError(message) {
            console.error('❌', message);
            alert('Erreur: ' + message);
        }

        function showInfo(message) {
            console.log('ℹ️', message);
        }

        // Auto-refresh
        function startAutoRefresh() {
            updateInterval = setInterval(() => {
                loadPlugins();
            }, 30000); // Toutes les 30 secondes
        }

        // Nettoyage
        window.addEventListener('beforeunload', function() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }
        });
    </script>
</body>
</html>
