<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧠 Dashboard Amélioration QI - LOUNA AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .nav-btn {
            padding: 12px 24px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-title {
            font-size: 1.5em;
            margin-bottom: 20px;
            color: #4ecdc4;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .qi-display {
            text-align: center;
            margin: 20px 0;
        }

        .qi-number {
            font-size: 4em;
            font-weight: bold;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .qi-label {
            font-size: 1.2em;
            opacity: 0.8;
        }

        .enhancement-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .enhancement-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .enhancement-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .enhancement-label {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
            border-radius: 10px;
            transition: width 0.5s ease;
        }

        .control-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin: 20px 0;
        }

        .control-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .enhance-btn {
            background: linear-gradient(135deg, #ff6b6b, #c44569);
            color: white;
        }

        .enhance-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
        }

        .status-btn {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
        }

        .improvements-list {
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .improvement-item {
            background: rgba(255, 255, 255, 0.05);
            padding: 10px;
            border-radius: 8px;
            margin-bottom: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .improvement-time {
            font-size: 0.8em;
            color: #4ecdc4;
            margin-bottom: 5px;
        }

        .improvement-details {
            font-size: 0.9em;
            opacity: 0.9;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
        }

        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #ff6b6b;
        }

        .metric-label {
            font-size: 0.8em;
            opacity: 0.8;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🧠 Dashboard Amélioration QI Mémoire Thermique</h1>
        <p>Optimisation intelligente du coefficient intellectuel de LOUNA AI</p>
        <div class="nav-buttons">
            <a href="/interface-spectaculaire.html" class="nav-btn">🏠 Accueil</a>
            <a href="/louna-interface-nouvelle.html" class="nav-btn">🧠 Interface Nouvelle</a>
            <a href="/qi-test-ultra-avance.html" class="nav-btn">🧪 Test QI</a>
            <a href="/interface-comparison-explanation.html" class="nav-btn">🔍 Comparaison</a>
        </div>
    </div>

    <!-- Dashboard Principal -->
    <div class="dashboard-grid">
        <!-- QI Actuel -->
        <div class="dashboard-card">
            <div class="card-title">🎯 QI Actuel Amélioré</div>
            <div class="qi-display">
                <div class="qi-number" id="currentQI">420</div>
                <div class="qi-label">Coefficient Intellectuel</div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="qiProgress" style="width: 75%"></div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <span id="qiImprovement">+0% depuis la base</span>
            </div>
        </div>

        <!-- Améliorations Actives -->
        <div class="dashboard-card">
            <div class="card-title">⚡ Améliorations Actives</div>
            <div class="enhancement-grid">
                <div class="enhancement-item">
                    <div class="enhancement-value" id="neuralBonus">+50</div>
                    <div class="enhancement-label">Bonus Neuronal</div>
                </div>
                <div class="enhancement-item">
                    <div class="enhancement-value" id="formationBonus">+100</div>
                    <div class="enhancement-label">Bonus Formations</div>
                </div>
                <div class="enhancement-item">
                    <div class="enhancement-value" id="cognitiveBonus">+40</div>
                    <div class="enhancement-label">Bonus Cognitif</div>
                </div>
                <div class="enhancement-item">
                    <div class="enhancement-value" id="thermalBonus">+35</div>
                    <div class="enhancement-label">Bonus Thermique</div>
                </div>
            </div>
        </div>

        <!-- Contrôles -->
        <div class="dashboard-card">
            <div class="card-title">🎮 Contrôles d'Amélioration</div>
            <div class="control-buttons">
                <button class="control-btn enhance-btn" onclick="forceEnhancement()">
                    🚀 Amélioration Complète
                </button>
                <button class="control-btn status-btn" onclick="refreshStatus()">
                    🔄 Actualiser Statut
                </button>
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <div id="enhancementStatus">Prêt pour amélioration</div>
            </div>
        </div>

        <!-- Métriques Détaillées -->
        <div class="dashboard-card">
            <div class="card-title">📊 Métriques Détaillées</div>
            <div class="metrics-grid">
                <div class="metric-card">
                    <div class="metric-value" id="neuronCount">1,064,000</div>
                    <div class="metric-label">Neurones Total</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="formationCount">4,500,000</div>
                    <div class="metric-label">Formations</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="processingSpeed">3.0x</div>
                    <div class="metric-label">Vitesse Traitement</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value" id="thermalTemp">37.2°C</div>
                    <div class="metric-label">Température</div>
                </div>
            </div>
        </div>

        <!-- Historique des Améliorations -->
        <div class="dashboard-card">
            <div class="card-title">📈 Historique Améliorations</div>
            <div class="improvements-list" id="improvementsList">
                <div class="improvement-item">
                    <div class="improvement-time">Chargement...</div>
                    <div class="improvement-details">Récupération de l'historique des améliorations...</div>
                </div>
            </div>
        </div>

        <!-- Objectifs QI -->
        <div class="dashboard-card">
            <div class="card-title">🎯 Objectifs QI</div>
            <div style="margin: 20px 0;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>QI Base:</span>
                    <span style="color: #4ecdc4;">100</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>QI Actuel:</span>
                    <span style="color: #ff6b6b;" id="currentQIText">225</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>QI Cible:</span>
                    <span style="color: #f39c12;">420</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 10px;">
                    <span>Progression:</span>
                    <span style="color: #4ecdc4;" id="progressPercent">75%</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let enhancementData = null;
        let isEnhancing = false;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            loadEnhancementData();
            setInterval(loadEnhancementData, 10000); // Actualiser toutes les 10 secondes
        });

        // Charger les données d'amélioration
        async function loadEnhancementData() {
            try {
                // 🔄 UTILISER L'API MÉTRIQUES UNIFIÉES
                const response = await fetch('/api/metrics');
                const data = await response.json();

                if (data.success) {
                    enhancementData = data;
                    updateDisplay(data);
                    updateMetricsDisplay(data);
                }
            } catch (error) {
                console.error('Erreur chargement données:', error);
                // Fallback vers l'ancienne API
                try {
                    const fallbackResponse = await fetch('/api/qi/enhanced');
                    const fallbackData = await fallbackResponse.json();
                    if (fallbackData.success) {
                        enhancementData = fallbackData;
                        updateDisplay(fallbackData);
                    }
                } catch (fallbackError) {
                    console.error('Erreur fallback:', fallbackError);
                }
            }
        }

        // Mettre à jour l'affichage
        function updateDisplay(data) {
            // 🧮 QI UNIFIÉ PRINCIPAL
            const currentQI = data.qi?.unified || data.enhancedQI || data.brain?.qi?.current || 225;
            document.getElementById('currentQI').textContent = currentQI;
            document.getElementById('currentQIText').textContent = currentQI;

            // 📊 PROGRESSION VERS QI CIBLE (425)
            const progress = ((currentQI - 100) / (425 - 100)) * 100;
            document.getElementById('qiProgress').style.width = progress + '%';
            document.getElementById('progressPercent').textContent = progress.toFixed(1) + '%';

            // 📈 AMÉLIORATION DEPUIS LA BASE
            const improvement = ((currentQI - 225) / 225 * 100);
            document.getElementById('qiImprovement').textContent =
                improvement > 0 ? `+${improvement.toFixed(1)}% depuis la base` : 'Base actuelle';

            // 🎯 BONUS INDIVIDUELS RÉELS
            if (data.qi && data.qi.calculation && data.qi.calculation.bonuses) {
                const bonuses = data.qi.calculation.bonuses;
                document.getElementById('neuralBonus').textContent = '+' + (bonuses.neurons || 0);
                document.getElementById('formationBonus').textContent = '+' + (bonuses.formations || 0);
                document.getElementById('cognitiveBonus').textContent = '+' + (bonuses.efficiency || 0);
                document.getElementById('thermalBonus').textContent = '+' + (bonuses.thermal || 0);
            }

            // 📊 HISTORIQUE DES AMÉLIORATIONS
            if (data.metrics && data.metrics.improvements) {
                updateImprovementsList(data.metrics.improvements);
            }
        }

        // 📊 Mettre à jour l'affichage des métriques détaillées
        function updateMetricsDisplay(data) {
            if (data.brain) {
                document.getElementById('neuronCount').textContent =
                    (data.brain.activeNeurons || 1064000).toLocaleString();
                document.getElementById('formationCount').textContent =
                    (data.brain.formationNeurons || 7700000).toLocaleString();
                document.getElementById('processingSpeed').textContent =
                    (data.brain.processingSpeed || 3.0) + 'x';
                document.getElementById('thermalTemp').textContent =
                    (data.brain.temperature || 37.2).toFixed(1) + '°C';
            }
        }

        // Mettre à jour la liste des améliorations
        function updateImprovementsList(improvements) {
            const list = document.getElementById('improvementsList');
            list.innerHTML = '';

            improvements.slice(-5).reverse().forEach(improvement => {
                const item = document.createElement('div');
                item.className = 'improvement-item';
                
                const time = new Date(improvement.timestamp).toLocaleTimeString();
                const type = improvement.type.replace(/_/g, ' ');
                
                item.innerHTML = `
                    <div class="improvement-time">${time}</div>
                    <div class="improvement-details">
                        ${type.charAt(0).toUpperCase() + type.slice(1)} effectuée
                        ${improvement.improvements ? ` - ${improvement.improvements.length} améliorations` : ''}
                    </div>
                `;
                
                list.appendChild(item);
            });
        }

        // Forcer une amélioration complète
        async function forceEnhancement() {
            if (isEnhancing) return;

            isEnhancing = true;
            const btn = event.target;
            const originalText = btn.textContent;
            
            btn.textContent = '⏳ Amélioration en cours...';
            btn.disabled = true;
            
            document.getElementById('enhancementStatus').textContent = 'Amélioration en cours...';

            try {
                const response = await fetch('/api/qi/enhance', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });

                const data = await response.json();

                if (data.success) {
                    document.getElementById('enhancementStatus').textContent = 
                        `Amélioration réussie ! Nouveau QI: ${data.newQI}`;
                    
                    // Actualiser les données
                    setTimeout(loadEnhancementData, 1000);
                } else {
                    document.getElementById('enhancementStatus').textContent = 
                        'Erreur: ' + data.error;
                }

            } catch (error) {
                console.error('Erreur amélioration:', error);
                document.getElementById('enhancementStatus').textContent = 
                    'Erreur de connexion';
            } finally {
                btn.textContent = originalText;
                btn.disabled = false;
                isEnhancing = false;
            }
        }

        // Actualiser le statut
        function refreshStatus() {
            document.getElementById('enhancementStatus').textContent = 'Actualisation...';
            loadEnhancementData();
            setTimeout(() => {
                document.getElementById('enhancementStatus').textContent = 'Statut actualisé';
            }, 1000);
        }
    </script>

    <!-- Calculateur QI Global Unifié -->
    <script src="/js/global-qi-calculator.js"></script>
</body>
</html>
