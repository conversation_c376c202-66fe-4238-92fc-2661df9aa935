<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 LOUNA AI - Tests Système Complets</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .test-card.success {
            border-color: #00d4aa;
            box-shadow: 0 0 15px rgba(0, 212, 170, 0.3);
        }

        .test-card.error {
            border-color: #e74c3c;
            box-shadow: 0 0 15px rgba(231, 76, 60, 0.3);
        }

        .test-card.testing {
            border-color: #f39c12;
            box-shadow: 0 0 15px rgba(243, 156, 18, 0.3);
        }

        .test-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .test-title {
            font-size: 1.2em;
            font-weight: 700;
            color: #ff6b9d;
        }

        .test-status {
            padding: 5px 10px;
            border-radius: 10px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-pending {
            background: rgba(108, 117, 125, 0.3);
            color: #6c757d;
        }

        .status-testing {
            background: rgba(243, 156, 18, 0.3);
            color: #f39c12;
        }

        .status-success {
            background: rgba(0, 212, 170, 0.3);
            color: #00d4aa;
        }

        .status-error {
            background: rgba(231, 76, 60, 0.3);
            color: #e74c3c;
        }

        .test-description {
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 15px;
            font-size: 0.9em;
        }

        .test-result {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            min-height: 60px;
            overflow-y: auto;
            max-height: 120px;
        }

        .test-actions {
            display: flex;
            gap: 10px;
        }

        .test-btn {
            flex: 1;
            padding: 8px 12px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .test-btn.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .test-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
            color: white;
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .test-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        }

        .test-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .global-controls {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }

        .global-controls h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
        }

        .global-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .global-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .global-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .global-btn.emergency {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .log-output {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .global-actions {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <button class="nav-btn" onclick="navigateHome()">🏠 Accueil</button>
        <button class="nav-btn" onclick="navigateControl()">🎛️ Contrôle</button>
        <button class="nav-btn" onclick="navigateChat()">💬 Chat</button>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🧪 Tests Système Complets</h1>
            <div class="subtitle">Validation et réactivation de tous les systèmes LOUNA AI</div>
        </div>

        <!-- Contrôles globaux -->
        <div class="global-controls">
            <h3>🎛️ Contrôles Globaux de Test</h3>
            <div class="global-actions">
                <button class="global-btn" onclick="runAllTests()">🧪 Lancer Tous les Tests</button>
                <button class="global-btn" onclick="reactivateAllSystems()">🔄 Réactiver Tous les Systèmes</button>
                <button class="global-btn" onclick="generateTestReport()">📊 Rapport de Test</button>
                <button class="global-btn emergency" onclick="emergencyReset()">🚨 Reset d'Urgence</button>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" id="globalProgress"></div>
            </div>
        </div>

        <!-- Grille des tests -->
        <div class="test-grid">
            <!-- Test DeepSeek -->
            <div class="test-card" id="testDeepSeek">
                <div class="test-header">
                    <div class="test-title">🤖 DeepSeek R1 8B</div>
                    <div class="test-status status-pending" id="statusDeepSeek">En attente</div>
                </div>
                <div class="test-description">
                    Test de connexion, réflexions et qualité de réponse
                </div>
                <div class="test-result" id="resultDeepSeek">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testDeepSeek()">🧪 Tester</button>
                    <button class="test-btn success" onclick="reactivateDeepSeek()">🔄 Réactiver</button>
                    <button class="test-btn danger" onclick="disconnectDeepSeek()">⏸️ Déconnecter</button>
                </div>
            </div>

            <!-- Test Mémoire Thermique -->
            <div class="test-card" id="testMemory">
                <div class="test-header">
                    <div class="test-title">🧠 Mémoire Thermique</div>
                    <div class="test-status status-pending" id="statusMemory">En attente</div>
                </div>
                <div class="test-description">
                    Validation des neurones, température et sauvegarde
                </div>
                <div class="test-result" id="resultMemory">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testMemory()">🧪 Tester</button>
                    <button class="test-btn success" onclick="backupMemory()">💾 Sauvegarder</button>
                    <button class="test-btn danger" onclick="optimizeMemory()">⚡ Optimiser</button>
                </div>
            </div>

            <!-- Test Système Vocal -->
            <div class="test-card" id="testVoice">
                <div class="test-header">
                    <div class="test-title">🎤 Système Vocal</div>
                    <div class="test-status status-pending" id="statusVoice">En attente</div>
                </div>
                <div class="test-description">
                    Test reconnaissance vocale et synthèse
                </div>
                <div class="test-result" id="resultVoice">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testVoice()">🧪 Tester</button>
                    <button class="test-btn success" onclick="enableVoice()">🎤 Activer</button>
                    <button class="test-btn danger" onclick="disableVoice()">🔇 Désactiver</button>
                </div>
            </div>

            <!-- Test Machine Learning -->
            <div class="test-card" id="testML">
                <div class="test-header">
                    <div class="test-title">🧠 Machine Learning</div>
                    <div class="test-status status-pending" id="statusML">En attente</div>
                </div>
                <div class="test-description">
                    Validation des 4 modèles et entraînement
                </div>
                <div class="test-result" id="resultML">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testML()">🧪 Tester</button>
                    <button class="test-btn success" onclick="trainModels()">🎓 Entraîner</button>
                    <button class="test-btn danger" onclick="resetMLData()">🔄 Reset</button>
                </div>
            </div>

            <!-- Test Cloud Sync -->
            <div class="test-card" id="testCloud">
                <div class="test-header">
                    <div class="test-title">☁️ Synchronisation Cloud</div>
                    <div class="test-status status-pending" id="statusCloud">En attente</div>
                </div>
                <div class="test-description">
                    Test de connectivité et synchronisation
                </div>
                <div class="test-result" id="resultCloud">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testCloud()">🧪 Tester</button>
                    <button class="test-btn success" onclick="enableCloud()">☁️ Activer</button>
                    <button class="test-btn danger" onclick="forceSync()">🔄 Forcer Sync</button>
                </div>
            </div>

            <!-- Test Performance -->
            <div class="test-card" id="testPerformance">
                <div class="test-header">
                    <div class="test-title">📊 Moniteur Performance</div>
                    <div class="test-status status-pending" id="statusPerformance">En attente</div>
                </div>
                <div class="test-description">
                    Validation CPU, mémoire et optimisations
                </div>
                <div class="test-result" id="resultPerformance">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testPerformance()">🧪 Tester</button>
                    <button class="test-btn success" onclick="optimizePerformance()">⚡ Optimiser</button>
                    <button class="test-btn danger" onclick="clearCache()">🧹 Nettoyer</button>
                </div>
            </div>

            <!-- Test Plugins -->
            <div class="test-card" id="testPlugins">
                <div class="test-header">
                    <div class="test-title">🔌 Gestionnaire Plugins</div>
                    <div class="test-status status-pending" id="statusPlugins">En attente</div>
                </div>
                <div class="test-description">
                    Validation des 4 plugins et API
                </div>
                <div class="test-result" id="resultPlugins">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testPlugins()">🧪 Tester</button>
                    <button class="test-btn success" onclick="reloadPlugins()">🔄 Recharger</button>
                    <button class="test-btn danger" onclick="disablePlugins()">⏸️ Désactiver</button>
                </div>
            </div>

            <!-- Test MCP Chat -->
            <div class="test-card" id="testMCP">
                <div class="test-header">
                    <div class="test-title">🌐 Chat MCP</div>
                    <div class="test-status status-pending" id="statusMCP">En attente</div>
                </div>
                <div class="test-description">
                    Test du mode MCP et dialogue autonome
                </div>
                <div class="test-result" id="resultMCP">
                    Prêt pour le test...
                </div>
                <div class="test-actions">
                    <button class="test-btn primary" onclick="testMCP()">🧪 Tester</button>
                    <button class="test-btn success" onclick="startMCPDialogue()">🌐 Démarrer MCP</button>
                    <button class="test-btn danger" onclick="stopMCPDialogue()">⏹️ Arrêter MCP</button>
                </div>
            </div>
        </div>

        <!-- Log de sortie -->
        <div class="log-output" id="logOutput">
            <strong>📝 Journal des Tests :</strong><br>
            Prêt pour les tests système...
        </div>
    </div>
