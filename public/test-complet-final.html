<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Tests Complets - LOUNA AI Ultra-Autonome</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.5em;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            font-size: 0.8em;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .main-container {
            height: calc(100vh - 70px);
            display: flex;
        }

        .sidebar {
            width: 300px;
            background: rgba(0, 0, 0, 0.3);
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            overflow-y: auto;
        }

        .test-controls {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin-bottom: 10px;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .test-btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
        }

        .test-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .test-btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .test-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .stats-panel {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .stat-value {
            color: #00d4aa;
            font-weight: 600;
        }

        .stat-value.warning {
            color: #f39c12;
        }

        .stat-value.error {
            color: #e74c3c;
        }

        .test-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .test-output {
            flex: 1;
            background: rgba(0, 0, 0, 0.4);
            padding: 20px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px 10px;
            border-radius: 4px;
            border-left: 3px solid transparent;
        }

        .log-entry.info {
            border-left-color: #3498db;
            background: rgba(52, 152, 219, 0.1);
        }

        .log-entry.success {
            border-left-color: #00d4aa;
            background: rgba(0, 212, 170, 0.1);
        }

        .log-entry.error {
            border-left-color: #e74c3c;
            background: rgba(231, 76, 60, 0.1);
        }

        .log-entry.warning {
            border-left-color: #f39c12;
            background: rgba(243, 156, 18, 0.1);
        }

        .progress-bar {
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #00d4aa, #667eea);
            width: 0%;
            transition: width 0.3s ease;
        }

        .results-summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
        }

        .result-value {
            font-weight: 600;
        }

        .result-value.passed {
            color: #00d4aa;
        }

        .result-value.failed {
            color: #e74c3c;
        }

        .spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #00d4aa;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                height: 200px;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🧪 Tests Complets LOUNA AI</h1>
        <div class="nav-buttons">
            <a href="/electron-optimized-interface.html" class="nav-btn">🏠 Accueil</a>
            <a href="/test-reactivation-simple.html" class="nav-btn">🔄 Réactivation</a>
            <a href="/mcp-electron-interface.html" class="nav-btn">🌐 MCP</a>
            <a href="/master-control-center.html" class="nav-btn">🎛️ Contrôle</a>
        </div>
    </div>

    <div class="main-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <h3>🎛️ Contrôles de Test</h3>
            <div class="test-controls">
                <button class="test-btn success" onclick="runBasicTests()">🧪 Tests de Base</button>
                <button class="test-btn" onclick="runPerformanceTests()">⚡ Tests Performance</button>
                <button class="test-btn" onclick="runReactivationTests()">🔄 Tests Réactivation</button>
                <button class="test-btn" onclick="runMCPTests()">🌐 Tests MCP</button>
                <button class="test-btn warning" onclick="runStressTests()">💪 Tests de Stress</button>
                <button class="test-btn danger" onclick="runCompleteTests()">🚀 Tests Complets</button>
            </div>

            <h3>📊 Statistiques</h3>
            <div class="stats-panel">
                <div class="stat-item">
                    <span>Tests Exécutés:</span>
                    <span class="stat-value" id="testsRun">0</span>
                </div>
                <div class="stat-item">
                    <span>Réussis:</span>
                    <span class="stat-value passed" id="testsPassed">0</span>
                </div>
                <div class="stat-item">
                    <span>Échecs:</span>
                    <span class="stat-value failed" id="testsFailed">0</span>
                </div>
                <div class="stat-item">
                    <span>Taux Réussite:</span>
                    <span class="stat-value" id="successRate">0%</span>
                </div>
                <div class="stat-item">
                    <span>Durée Totale:</span>
                    <span class="stat-value" id="totalDuration">0ms</span>
                </div>
            </div>

            <h3>🔧 Actions Rapides</h3>
            <div class="test-controls">
                <button class="test-btn" onclick="clearOutput()">🧹 Effacer Logs</button>
                <button class="test-btn" onclick="optimizeMemory()">🧠 Optimiser Mémoire</button>
                <button class="test-btn" onclick="reactivateAll()">🔄 Réactiver Tout</button>
                <button class="test-btn warning" onclick="emergencyStop()">🚨 Arrêt Urgence</button>
            </div>
        </div>

        <!-- Test Area -->
        <div class="test-area">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="test-output" id="testOutput">
                <div class="log-entry info">
                    <strong>🧪 Système de Tests LOUNA AI Ultra-Autonome</strong><br>
                    Interface de test complète pour tous les systèmes.<br>
                    Sélectionnez un type de test dans le panneau de gauche pour commencer.
                </div>
            </div>

            <div class="results-summary hidden" id="resultsSummary">
                <h4>📊 Résumé des Tests</h4>
                <div class="result-item">
                    <span>Total:</span>
                    <span class="result-value" id="summaryTotal">0</span>
                </div>
                <div class="result-item">
                    <span>Réussis:</span>
                    <span class="result-value passed" id="summaryPassed">0</span>
                </div>
                <div class="result-item">
                    <span>Échecs:</span>
                    <span class="result-value failed" id="summaryFailed">0</span>
                </div>
                <div class="result-item">
                    <span>Taux de Réussite:</span>
                    <span class="result-value" id="summaryRate">0%</span>
                </div>
            </div>
        </div>
    </div>

    <script src="/test-all-systems.js"></script>
    <script>
        // Variables globales
        let currentTester = null;
        let isTestRunning = false;
        let testStartTime = 0;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Interface de tests complète chargée');
            addLog('info', 'Interface de tests initialisée et prête');
        });

        // Ajouter un log à l'affichage
        function addLog(type, message) {
            const output = document.getElementById('testOutput');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            
            const timestamp = new Date().toLocaleTimeString('fr-FR');
            logEntry.innerHTML = `<span style="opacity: 0.7">[${timestamp}]</span> ${message}`;
            
            output.appendChild(logEntry);
            output.scrollTop = output.scrollHeight;
        }

        // Mettre à jour les statistiques
        function updateStats(results) {
            document.getElementById('testsRun').textContent = results.total;
            document.getElementById('testsPassed').textContent = results.passed;
            document.getElementById('testsFailed').textContent = results.failed;
            
            const rate = results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0;
            document.getElementById('successRate').textContent = rate + '%';
            
            const duration = Date.now() - testStartTime;
            document.getElementById('totalDuration').textContent = duration + 'ms';
        }

        // Mettre à jour la barre de progression
        function updateProgress(current, total) {
            const percentage = total > 0 ? (current / total) * 100 : 0;
            document.getElementById('progressFill').style.width = percentage + '%';
        }

        // Désactiver/activer les boutons de test
        function setTestButtonsEnabled(enabled) {
            const buttons = document.querySelectorAll('.test-btn');
            buttons.forEach(btn => {
                btn.disabled = !enabled;
            });
            isTestRunning = !enabled;
        }

        // Effacer l'affichage
        function clearOutput() {
            document.getElementById('testOutput').innerHTML = '';
            document.getElementById('resultsSummary').classList.add('hidden');
            updateProgress(0, 100);
            addLog('info', 'Affichage effacé');
        }

        // Tests de base
        async function runBasicTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('info', '🚀 Démarrage des tests de base...');
            
            try {
                currentTester = new SystemTester();
                
                // Override des méthodes pour l'affichage
                const originalTest = currentTester.test.bind(currentTester);
                currentTester.test = async function(name, testFunction) {
                    addLog('info', `🧪 Test: ${name}...`);
                    await originalTest(name, testFunction);
                    
                    const lastResult = this.results.details[this.results.details.length - 1];
                    if (lastResult.status === 'PASSED') {
                        addLog('success', `✅ ${name} - RÉUSSI (${lastResult.duration}ms)`);
                    } else {
                        addLog('error', `❌ ${name} - ${lastResult.error || 'ÉCHEC'}`);
                    }
                    
                    updateStats(this.results);
                    updateProgress(this.results.total, 10); // Estimation
                };
                
                await currentTester.testBasicSystems();
                
                addLog('success', '✅ Tests de base terminés');
                showResultsSummary(currentTester.results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests de base: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Tests de performance
        async function runPerformanceTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('info', '⚡ Démarrage des tests de performance...');
            
            try {
                if (!currentTester) currentTester = new SystemTester();
                await currentTester.testPerformanceOptimization();
                
                addLog('success', '✅ Tests de performance terminés');
                showResultsSummary(currentTester.results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests performance: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Tests de réactivation
        async function runReactivationTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('info', '🔄 Démarrage des tests de réactivation...');
            
            try {
                if (!currentTester) currentTester = new SystemTester();
                await currentTester.testReactivationSystems();
                
                addLog('success', '✅ Tests de réactivation terminés');
                showResultsSummary(currentTester.results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests réactivation: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Tests MCP
        async function runMCPTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('info', '🌐 Démarrage des tests MCP...');
            
            try {
                if (!currentTester) currentTester = new SystemTester();
                await currentTester.testMCPSystems();
                
                addLog('success', '✅ Tests MCP terminés');
                showResultsSummary(currentTester.results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests MCP: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Tests de stress
        async function runStressTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('warning', '💪 Démarrage des tests de stress...');
            
            try {
                if (!currentTester) currentTester = new SystemTester();
                await currentTester.stressTestMemory();
                
                addLog('success', '✅ Tests de stress terminés');
                showResultsSummary(currentTester.results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests stress: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Tests complets
        async function runCompleteTests() {
            if (isTestRunning) return;
            
            setTestButtonsEnabled(false);
            testStartTime = Date.now();
            addLog('info', '🚀 Démarrage des tests complets...');
            
            try {
                currentTester = new SystemTester();
                
                // Override pour l'affichage en temps réel
                const originalTest = currentTester.test.bind(currentTester);
                currentTester.test = async function(name, testFunction) {
                    addLog('info', `🧪 Test: ${name}...`);
                    await originalTest(name, testFunction);
                    
                    const lastResult = this.results.details[this.results.details.length - 1];
                    if (lastResult.status === 'PASSED') {
                        addLog('success', `✅ ${name} - RÉUSSI (${lastResult.duration}ms)`);
                    } else {
                        addLog('error', `❌ ${name} - ${lastResult.error || 'ÉCHEC'}`);
                    }
                    
                    updateStats(this.results);
                    updateProgress(this.results.total, 20); // Estimation
                };
                
                const results = await currentTester.runCompleteTests();
                
                addLog('success', '🎉 Tous les tests terminés !');
                showResultsSummary(results);
                
            } catch (error) {
                addLog('error', `❌ Erreur tests complets: ${error.message}`);
            } finally {
                setTestButtonsEnabled(true);
            }
        }

        // Afficher le résumé des résultats
        function showResultsSummary(results) {
            const summary = document.getElementById('resultsSummary');
            
            document.getElementById('summaryTotal').textContent = results.total;
            document.getElementById('summaryPassed').textContent = results.passed;
            document.getElementById('summaryFailed').textContent = results.failed;
            
            const rate = results.total > 0 ? ((results.passed / results.total) * 100).toFixed(1) : 0;
            document.getElementById('summaryRate').textContent = rate + '%';
            
            summary.classList.remove('hidden');
        }

        // Actions rapides
        async function optimizeMemory() {
            addLog('info', '🧠 Optimisation mémoire...');
            try {
                if (typeof require !== 'undefined') {
                    const { ipcRenderer } = require('electron');
                    const result = await ipcRenderer.invoke('optimize-memory', 'normal');
                    if (result.success) {
                        addLog('success', '✅ Optimisation mémoire réussie');
                    } else {
                        addLog('error', '❌ Échec optimisation mémoire');
                    }
                } else {
                    addLog('warning', '⚠️ Optimisation nécessite Electron');
                }
            } catch (error) {
                addLog('error', `❌ Erreur optimisation: ${error.message}`);
            }
        }

        async function reactivateAll() {
            addLog('info', '🔄 Réactivation de tous les systèmes...');
            try {
                if (typeof require !== 'undefined') {
                    const { ipcRenderer } = require('electron');
                    const result = await ipcRenderer.invoke('reactivate-all-systems');
                    if (result.success) {
                        addLog('success', '✅ Réactivation réussie');
                    } else {
                        addLog('error', '❌ Échec réactivation');
                    }
                } else {
                    addLog('warning', '⚠️ Réactivation nécessite Electron');
                }
            } catch (error) {
                addLog('error', `❌ Erreur réactivation: ${error.message}`);
            }
        }

        async function emergencyStop() {
            if (!confirm('⚠️ ARRÊT D\'URGENCE\n\nCeci va arrêter tous les systèmes.\nContinuer ?')) {
                return;
            }
            
            addLog('warning', '🚨 ARRÊT D\'URGENCE ACTIVÉ');
            try {
                if (typeof require !== 'undefined') {
                    const { ipcRenderer } = require('electron');
                    const result = await ipcRenderer.invoke('emergency-shutdown-all');
                    addLog('warning', '🚨 Arrêt d\'urgence en cours...');
                } else {
                    addLog('warning', '⚠️ Arrêt d\'urgence nécessite Electron');
                }
            } catch (error) {
                addLog('error', `❌ Erreur arrêt urgence: ${error.message}`);
            }
        }
    </script>
</body>
</html>
