<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 Test Réactivation DeepSeek R1 8B</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .btn {
            background: linear-gradient(45deg, #00d4ff, #0984e3);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .btn.danger {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
        }
        
        .btn.danger:hover {
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #00d4ff;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info { background: rgba(0, 212, 255, 0.1); }
        .log-success { background: rgba(0, 255, 0, 0.1); }
        .log-warning { background: rgba(255, 193, 7, 0.1); }
        .log-error { background: rgba(255, 107, 107, 0.1); }
        
        .auth-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            width: 200px;
            margin: 10px;
        }
        
        .auth-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Test Réactivation DeepSeek R1 8B Ultra-Robuste</h1>
        
        <div class="test-section">
            <h3>🛡️ Contrôles de Sécurité</h3>
            <button class="btn" onclick="getSecurityStatus()">
                📊 Statut de Sécurité
            </button>
            <button class="btn danger" onclick="guardianDisconnect()">
                🛡️ Déconnexion Agent Gardien
            </button>
            <button class="btn danger" onclick="emergencyStop()">
                🚨 Arrêt d'Urgence
            </button>
        </div>
        
        <div class="test-section">
            <h3>🔄 Réactivation</h3>
            <input type="text" class="auth-input" id="authCode" placeholder="Code d'autorisation">
            <br>
            <button class="btn" onclick="reactivateConnection()">
                ✅ Réactiver Connexion
            </button>
            <button class="btn" onclick="testConnection()">
                🧪 Test Connexion
            </button>
        </div>
        
        <div class="status-display" id="statusDisplay">
            Statut : En attente...
        </div>
        
        <div class="log-container">
            <h3>📝 Journal des Tests</h3>
            <div id="testLog"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('testLog');
        let statusDisplay = document.getElementById('statusDisplay');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function updateStatus(message) {
            statusDisplay.innerHTML = message;
        }
        
        async function getSecurityStatus() {
            try {
                addLog('📊 Récupération du statut de sécurité...', 'info');
                
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-security-status');
                    
                    if (result.success) {
                        const report = result.report;
                        addLog('✅ Statut de sécurité récupéré', 'success');
                        
                        updateStatus(`
                            <strong>📊 Statut de Sécurité :</strong><br>
                            🛡️ Agent Gardien: ${report.guardianStatus.active ? '🟢 ACTIF' : '🔴 INACTIF'}<br>
                            🔗 Connexion: ${report.connectionStatus.connected ? '🟢 CONNECTÉ' : '🔴 DÉCONNECTÉ'}<br>
                            🚨 Urgence: ${report.connectionStatus.securityAgent.emergencyStop ? '🚨 ARRÊT D\'URGENCE' : '🟢 NORMAL'}<br>
                            📊 Requêtes: ${report.metrics.totalRequests} | Bloquées: ${report.metrics.blockedRequests}<br>
                            🚨 Arrêts d'urgence: ${report.metrics.emergencyStops}
                        `);
                        
                        addLog(`Agent Gardien: ${report.guardianStatus.active ? 'ACTIF' : 'INACTIF'}`, 'info');
                        addLog(`Connexion: ${report.connectionStatus.connected ? 'CONNECTÉ' : 'DÉCONNECTÉ'}`, 'info');
                        addLog(`Échecs consécutifs: ${report.connectionStatus.connectionHealth.consecutiveFailures}`, 'warning');
                        
                    } else {
                        addLog(`❌ Erreur: ${result.error}`, 'error');
                        updateStatus(`❌ Erreur: ${result.error}`);
                    }
                } else {
                    addLog('❌ API Electron non disponible', 'error');
                    updateStatus('❌ API Electron non disponible');
                }
                
            } catch (error) {
                addLog(`❌ Erreur statut: ${error.message}`, 'error');
                updateStatus(`❌ Erreur: ${error.message}`);
            }
        }
        
        async function guardianDisconnect() {
            try {
                addLog('🛡️ Demande de déconnexion par agent gardien...', 'warning');
                
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-guardian-disconnect', 'Déconnexion manuelle via interface de test');
                    
                    if (result.success) {
                        addLog('✅ Déconnexion par agent gardien réussie', 'success');
                        updateStatus('🛡️ Déconnexion par agent gardien effectuée');
                    } else {
                        addLog(`❌ Échec déconnexion: ${result.error}`, 'error');
                        updateStatus(`❌ Échec: ${result.error}`);
                    }
                } else {
                    addLog('❌ API Electron non disponible', 'error');
                }
                
            } catch (error) {
                addLog(`❌ Erreur déconnexion: ${error.message}`, 'error');
            }
        }
        
        async function emergencyStop() {
            try {
                addLog('🚨 DÉCLENCHEMENT ARRÊT D\'URGENCE...', 'error');
                
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-emergency-stop', 'Arrêt d\'urgence via interface de test');
                    
                    if (result.success) {
                        addLog('🚨 ARRÊT D\'URGENCE ACTIVÉ', 'error');
                        updateStatus('🚨 ARRÊT D\'URGENCE ACTIVÉ');
                    } else {
                        addLog(`❌ Échec arrêt d'urgence: ${result.error}`, 'error');
                        updateStatus(`❌ Échec: ${result.error}`);
                    }
                } else {
                    addLog('❌ API Electron non disponible', 'error');
                }
                
            } catch (error) {
                addLog(`❌ Erreur arrêt d'urgence: ${error.message}`, 'error');
            }
        }
        
        async function reactivateConnection() {
            try {
                const authCode = document.getElementById('authCode').value;
                addLog(`🔄 Tentative de réactivation avec code: ${authCode || 'aucun'}...`, 'info');
                
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-reactivate', authCode);
                    
                    if (result.success && result.reactivated) {
                        addLog('✅ CONNEXION RÉACTIVÉE AVEC SUCCÈS', 'success');
                        updateStatus('✅ CONNEXION RÉACTIVÉE');
                    } else {
                        addLog(`❌ Échec réactivation: ${result.error || 'Connexion non établie'}`, 'error');
                        updateStatus(`❌ Échec: ${result.error || 'Non réactivée'}`);
                    }
                } else {
                    addLog('❌ API Electron non disponible', 'error');
                }
                
            } catch (error) {
                addLog(`❌ Erreur réactivation: ${error.message}`, 'error');
            }
        }
        
        async function testConnection() {
            try {
                addLog('🧪 Test de connexion DeepSeek...', 'info');
                
                if (window.electronAPI) {
                    const result = await window.electronAPI.invoke('deepseek-chat', 'Test de connexion sécurisée. Réponds: OK');
                    
                    if (result.success) {
                        addLog(`✅ Test réussi: ${result.response.substring(0, 100)}...`, 'success');
                        updateStatus('✅ Test de connexion réussi');
                    } else {
                        addLog(`❌ Test échoué: ${result.error}`, 'error');
                        updateStatus(`❌ Test échoué: ${result.error}`);
                    }
                } else {
                    addLog('❌ API Electron non disponible', 'error');
                }
                
            } catch (error) {
                addLog(`❌ Erreur test: ${error.message}`, 'error');
            }
        }
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            addLog('🔄 Interface de test de réactivation initialisée', 'success');
            
            // Vérifier si l'API Electron est disponible
            if (window.electronAPI) {
                addLog('✅ API Electron détectée', 'success');
                // Récupérer le statut initial
                setTimeout(getSecurityStatus, 1000);
            } else {
                addLog('⚠️ API Electron non disponible - Mode simulation', 'warning');
                updateStatus('⚠️ Mode simulation - API Electron non disponible');
            }
        });
        
        // Mise à jour automatique du statut toutes les 10 secondes
        setInterval(() => {
            if (window.electronAPI) {
                getSecurityStatus();
            }
        }, 10000);
    </script>
</body>
</html>
