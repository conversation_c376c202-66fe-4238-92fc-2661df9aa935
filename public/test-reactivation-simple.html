<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Tests de Réactivation - LOUNA AI</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #ff6b9d, #c44569, #f8b500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .test-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-section h3 {
            color: #ff6b9d;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        .test-item {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .test-item h4 {
            color: #00d4aa;
            margin-bottom: 10px;
        }

        .test-result {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            min-height: 60px;
            overflow-y: auto;
            max-height: 120px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .btn.success {
            background: linear-gradient(135deg, #00d4aa, #00b894);
        }

        .btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn.warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success {
            background: #00ff00;
            box-shadow: 0 0 10px #00ff00;
        }

        .status-error {
            background: #ff0000;
            box-shadow: 0 0 10px #ff0000;
        }

        .status-warning {
            background: #f39c12;
            box-shadow: 0 0 10px #f39c12;
        }

        .status-pending {
            background: #6c757d;
        }

        .nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .global-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 20px;
        }

        .global-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.3s ease;
        }

        .global-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .global-btn.emergency {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-buttons {
                position: relative;
                top: auto;
                right: auto;
                justify-content: center;
                margin-bottom: 20px;
            }

            .global-actions {
                flex-direction: column;
                align-items: center;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <div class="nav-buttons">
        <a href="/electron-optimized-interface.html" class="nav-btn">🏠 Accueil</a>
        <a href="/master-control-center.html" class="nav-btn">🎛️ Contrôle</a>
        <a href="/mcp-chat-interface.html" class="nav-btn">🌐 MCP</a>
    </div>

    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1>🧪 Tests de Réactivation</h1>
            <div class="subtitle">Validation étape par étape de tous les systèmes LOUNA AI</div>
        </div>

        <!-- Actions globales -->
        <div class="global-actions">
            <button class="global-btn" onclick="testAllSystems()">🧪 Tester Tout</button>
            <button class="global-btn" onclick="reactivateAllSystems()">🔄 Réactiver Tout</button>
            <button class="global-btn emergency" onclick="emergencyReset()">🚨 Reset d'Urgence</button>
        </div>

        <!-- Tests DeepSeek -->
        <div class="test-section">
            <h3>🤖 Tests DeepSeek R1 8B</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="deepseek-status"></span>Connexion DeepSeek</h4>
                    <div class="test-result" id="deepseek-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testDeepSeek()">🧪 Tester Connexion</button>
                    <button class="btn success" onclick="reconnectDeepSeek()">🔄 Reconnecter</button>
                </div>
                
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="chat-status"></span>Test Chat</h4>
                    <div class="test-result" id="chat-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testChat()">💬 Tester Chat</button>
                </div>
            </div>
        </div>

        <!-- Tests MCP -->
        <div class="test-section">
            <h3>🌐 Tests Mode MCP</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="mcp-status"></span>Statut MCP</h4>
                    <div class="test-result" id="mcp-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testMCPStatus()">🌐 Tester MCP</button>
                    <button class="btn success" onclick="startMCPDialogue()">🚀 Démarrer MCP</button>
                </div>
                
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="mcp-chat-status"></span>Chat MCP</h4>
                    <div class="test-result" id="mcp-chat-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testMCPChat()">💬 Test Chat MCP</button>
                    <button class="btn warning" onclick="openMCPInterface()">🌐 Ouvrir Interface</button>
                </div>
            </div>
        </div>

        <!-- Tests Systèmes -->
        <div class="test-section">
            <h3>🔧 Tests Systèmes</h3>
            <div class="test-grid">
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="memory-status"></span>Mémoire Thermique</h4>
                    <div class="test-result" id="memory-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testMemory()">🧠 Tester Mémoire</button>
                    <button class="btn success" onclick="backupMemory()">💾 Sauvegarder</button>
                </div>
                
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="voice-status"></span>Système Vocal</h4>
                    <div class="test-result" id="voice-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testVoice()">🎤 Tester Vocal</button>
                    <button class="btn success" onclick="enableVoice()">🔊 Activer</button>
                </div>
                
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="ml-status"></span>Machine Learning</h4>
                    <div class="test-result" id="ml-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testML()">🧠 Tester ML</button>
                    <button class="btn success" onclick="trainModels()">🎓 Entraîner</button>
                </div>
                
                <div class="test-item">
                    <h4><span class="status-indicator status-pending" id="cloud-status"></span>Cloud Sync</h4>
                    <div class="test-result" id="cloud-result">Prêt pour le test...</div>
                    <button class="btn" onclick="testCloud()">☁️ Tester Cloud</button>
                    <button class="btn success" onclick="enableCloud()">🌐 Activer</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Variables globales
        let electronAPI = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🧪 Interface de test chargée');

            // Vérifier si on est dans Electron
            if (typeof require !== 'undefined') {
                try {
                    const { ipcRenderer } = require('electron');
                    electronAPI = ipcRenderer;
                    console.log('✅ API Electron disponible');
                } catch (error) {
                    console.warn('⚠️ API Electron non disponible:', error);
                }
            }

            // Démarrer les tests automatiques
            setTimeout(() => {
                testAllSystems();
            }, 1000);
        });

        // Mettre à jour le statut visuel
        function updateStatus(elementId, status, message) {
            const indicator = document.getElementById(elementId);
            const resultElement = document.getElementById(elementId.replace('-status', '-result'));

            if (indicator) {
                indicator.className = `status-indicator status-${status}`;
            }

            if (resultElement) {
                resultElement.textContent = message;
            }
        }

        // Test DeepSeek
        async function testDeepSeek() {
            updateStatus('deepseek-status', 'warning', 'Test en cours...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-status');
                    if (result.success) {
                        updateStatus('deepseek-status', 'success',
                            `✅ Connecté - Qualité: ${Math.round(result.quality * 100)}%`);
                    } else {
                        updateStatus('deepseek-status', 'error', '❌ Déconnecté');
                    }
                } else {
                    // Test via fetch
                    const response = await fetch('/api/deepseek-status');
                    const data = await response.json();

                    if (data.success && data.connected) {
                        updateStatus('deepseek-status', 'success', '✅ Connecté via serveur');
                    } else {
                        updateStatus('deepseek-status', 'error', '❌ Déconnecté');
                    }
                }
            } catch (error) {
                updateStatus('deepseek-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test Chat
        async function testChat() {
            updateStatus('chat-status', 'warning', 'Test chat en cours...');

            try {
                const testMessage = 'Test de connexion depuis l\'interface de test';

                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-chat', testMessage);
                    if (result.success) {
                        updateStatus('chat-status', 'success',
                            `✅ Chat OK - Temps: ${result.responseTime || 'N/A'}ms`);
                    } else {
                        updateStatus('chat-status', 'error', '❌ Erreur chat');
                    }
                } else {
                    const response = await fetch('/api/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: testMessage })
                    });

                    const data = await response.json();
                    if (data.success) {
                        updateStatus('chat-status', 'success', '✅ Chat OK via serveur');
                    } else {
                        updateStatus('chat-status', 'error', '❌ Erreur chat');
                    }
                }
            } catch (error) {
                updateStatus('chat-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test MCP Status
        async function testMCPStatus() {
            updateStatus('mcp-status', 'warning', 'Test MCP en cours...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('mcp-status');
                    if (result.success) {
                        updateStatus('mcp-status', 'success', `✅ MCP: ${result.status}`);
                    } else {
                        updateStatus('mcp-status', 'error', '❌ MCP indisponible');
                    }
                } else {
                    updateStatus('mcp-status', 'warning', '⚠️ Test via Electron requis');
                }
            } catch (error) {
                updateStatus('mcp-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Démarrer dialogue MCP
        async function startMCPDialogue() {
            updateStatus('mcp-status', 'warning', 'Démarrage MCP...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('mcp-start-dialogue', {
                        mode: 'test',
                        capabilities: ['internet-search', 'real-time-data']
                    });

                    if (result.success) {
                        updateStatus('mcp-status', 'success',
                            `✅ MCP démarré - ID: ${result.dialogueId}`);
                    } else {
                        updateStatus('mcp-status', 'error', '❌ Erreur démarrage MCP');
                    }
                } else {
                    updateStatus('mcp-status', 'warning', '⚠️ Electron requis pour MCP');
                }
            } catch (error) {
                updateStatus('mcp-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test Chat MCP
        async function testMCPChat() {
            updateStatus('mcp-chat-status', 'warning', 'Test chat MCP...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('mcp-submit-response', {
                        message: 'Test de chat MCP depuis l\'interface de test',
                        includeInternet: false
                    });

                    if (result.success) {
                        updateStatus('mcp-chat-status', 'success', '✅ Chat MCP fonctionnel');
                    } else {
                        updateStatus('mcp-chat-status', 'error', '❌ Erreur chat MCP');
                    }
                } else {
                    updateStatus('mcp-chat-status', 'warning', '⚠️ Electron requis');
                }
            } catch (error) {
                updateStatus('mcp-chat-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test Mémoire
        async function testMemory() {
            updateStatus('memory-status', 'warning', 'Test mémoire...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('thermal-memory-stats');
                    if (result.success) {
                        updateStatus('memory-status', 'success',
                            `✅ ${result.totalEntries || 0} neurones actifs`);
                    } else {
                        updateStatus('memory-status', 'error', '❌ Erreur mémoire');
                    }
                } else {
                    const response = await fetch('/api/thermal-memory-stats');
                    const data = await response.json();
                    if (data.success) {
                        updateStatus('memory-status', 'success', '✅ Mémoire OK');
                    } else {
                        updateStatus('memory-status', 'error', '❌ Erreur mémoire');
                    }
                }
            } catch (error) {
                updateStatus('memory-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test Vocal
        async function testVoice() {
            updateStatus('voice-status', 'warning', 'Test vocal...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('voice-control', { action: 'status' });
                    if (result.success) {
                        updateStatus('voice-status', 'success', '✅ Système vocal OK');
                    } else {
                        updateStatus('voice-status', 'warning', '⚠️ Vocal désactivé');
                    }
                } else {
                    updateStatus('voice-status', 'warning', '⚠️ Test via Electron requis');
                }
            } catch (error) {
                updateStatus('voice-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test ML
        async function testML() {
            updateStatus('ml-status', 'warning', 'Test ML...');

            try {
                // Simuler un test ML
                setTimeout(() => {
                    updateStatus('ml-status', 'success', '✅ 4 modèles ML actifs');
                }, 1000);
            } catch (error) {
                updateStatus('ml-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Test Cloud
        async function testCloud() {
            updateStatus('cloud-status', 'warning', 'Test cloud...');

            try {
                // Simuler un test cloud
                setTimeout(() => {
                    updateStatus('cloud-status', 'warning', '⚠️ Cloud désactivé (normal)');
                }, 1000);
            } catch (error) {
                updateStatus('cloud-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Reconnecter DeepSeek
        async function reconnectDeepSeek() {
            updateStatus('deepseek-status', 'warning', 'Reconnexion...');

            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('deepseek-reconnect');
                    if (result.success) {
                        updateStatus('deepseek-status', 'success', '✅ Reconnecté');
                        // Re-tester après reconnexion
                        setTimeout(testDeepSeek, 1000);
                    } else {
                        updateStatus('deepseek-status', 'error', '❌ Erreur reconnexion');
                    }
                } else {
                    updateStatus('deepseek-status', 'warning', '⚠️ Electron requis');
                }
            } catch (error) {
                updateStatus('deepseek-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Sauvegarder mémoire
        async function backupMemory() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('thermal-memory-backup');
                    if (result.success) {
                        alert('✅ Sauvegarde mémoire réussie');
                    } else {
                        alert('❌ Erreur sauvegarde');
                    }
                } else {
                    alert('⚠️ Electron requis pour sauvegarde');
                }
            } catch (error) {
                alert(`❌ Erreur: ${error.message}`);
            }
        }

        // Activer vocal
        async function enableVoice() {
            try {
                if (electronAPI) {
                    const result = await electronAPI.invoke('voice-control', {
                        action: 'enable'
                    });
                    if (result.success) {
                        updateStatus('voice-status', 'success', '✅ Vocal activé');
                    } else {
                        updateStatus('voice-status', 'error', '❌ Erreur activation');
                    }
                } else {
                    updateStatus('voice-status', 'warning', '⚠️ Electron requis');
                }
            } catch (error) {
                updateStatus('voice-status', 'error', `❌ Erreur: ${error.message}`);
            }
        }

        // Entraîner modèles
        async function trainModels() {
            alert('🎓 Entraînement des modèles ML démarré en arrière-plan');
        }

        // Activer cloud
        async function enableCloud() {
            updateStatus('cloud-status', 'warning', '⚠️ Cloud désactivé par défaut (sécurité)');
        }

        // Ouvrir interface MCP
        function openMCPInterface() {
            window.location.href = '/mcp-chat-interface.html';
        }

        // Tester tous les systèmes
        async function testAllSystems() {
            console.log('🧪 Démarrage tests complets...');

            // Tests séquentiels avec délais
            await testDeepSeek();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testChat();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testMCPStatus();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testMemory();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testVoice();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testML();
            await new Promise(resolve => setTimeout(resolve, 500));

            await testCloud();

            console.log('✅ Tests complets terminés');
        }

        // Réactiver tous les systèmes
        async function reactivateAllSystems() {
            if (!confirm('Réactiver tous les systèmes LOUNA AI ?')) {
                return;
            }

            console.log('🔄 Réactivation de tous les systèmes...');

            try {
                await reconnectDeepSeek();
                await enableVoice();

                // Re-tester après réactivation
                setTimeout(testAllSystems, 2000);

                alert('✅ Réactivation terminée - Tests relancés');
            } catch (error) {
                alert(`❌ Erreur réactivation: ${error.message}`);
            }
        }

        // Reset d'urgence
        async function emergencyReset() {
            if (!confirm('⚠️ RESET D\'URGENCE\n\nCeci va redémarrer tous les systèmes.\nContinuer ?')) {
                return;
            }

            try {
                if (electronAPI) {
                    await electronAPI.invoke('system-restart');
                    alert('🔄 Redémarrage système en cours...');
                } else {
                    alert('⚠️ Reset d\'urgence nécessite Electron');
                }
            } catch (error) {
                alert(`❌ Erreur reset: ${error.message}`);
            }
        }
    </script>
</body>
</html>
