
/* 🎨 LOUNA AI - Thème: LOUNA Default */
:root {
    /* Couleurs */
    --color-primary: #ff6b9d;
    --color-secondary: #00d4aa;
    --color-background: linear-gradient(135deg, #1a1a2e 0%, #16213e 25%, #0f3460 50%, #533483 75%, #7209b7 100%);
    --color-surface: rgba(255, 255, 255, 0.1);
    --color-text: #ffffff;
    --color-text-secondary: rgba(255, 255, 255, 0.8);
    --color-accent: #f39c12;
    --color-success: #00ff00;
    --color-warning: #f39c12;
    --color-error: #e74c3c;
    --color-info: #3498db;

    /* Polices */
    --font-primary: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-mono: 'Fira Code', 'Consolas', monospace;

    /* Effets */
    --effect-blur: 15px;
    --effect-border-radius: 20px;
    --effect-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    --effect-glow-intensity: 0.5;

    /* Animations */
    --animation-duration: 300ms;
    --animation-easing: ease-in-out;
    --animations-enabled: 1;
}

/* Application du thème */
body {
    background: var(--color-background);
    color: var(--color-text);
    font-family: var(--font-primary);
    transition: all var(--animation-duration) var(--animation-easing);
}

.theme-surface {
    background: var(--color-surface);
    backdrop-filter: blur(var(--effect-blur));
    border-radius: var(--effect-border-radius);
    box-shadow: var(--effect-shadow);
}

.theme-primary {
    color: var(--color-primary);
}

.theme-secondary {
    color: var(--color-secondary);
}

.theme-accent {
    color: var(--color-accent);
}

.theme-glow {
    box-shadow: 0 0 20px var(--color-primary);
    filter: brightness(calc(1 + var(--effect-glow-intensity)));
}

/* Animations conditionnelles */
.theme-animated {
    transition: all var(--animation-duration) var(--animation-easing);
}

@media (prefers-reduced-motion: reduce) {
    :root {
        --animations-enabled: 0;
        --animation-duration: 0ms;
    }
}
