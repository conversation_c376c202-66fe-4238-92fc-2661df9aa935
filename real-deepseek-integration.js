/**
 * 🤖 INTÉGRATION ULTRA-ROBUSTE DEEPSEEK R1 8B
 * Connexion blindée avec retry automatique, monitoring santé, récupération auto
 */

const { exec } = require('child_process');
const util = require('util');
const axios = require('axios');
const execAsync = util.promisify(exec);

class RealDeepSeekIntegration {
    constructor() {
        this.modelName = 'deepseek-r1:8b';
        this.isConnected = false;
        this.responseHistory = [];
        this.connectionHealth = {
            status: 'disconnected',
            lastCheck: null,
            consecutiveFailures: 0,
            totalConnections: 0,
            uptime: 0,
            startTime: Date.now()
        };
        this.performanceMetrics = {
            totalQueries: 0,
            averageResponseTime: 0,
            successRate: 0,
            errors: 0,
            fastestResponse: Infinity,
            slowestResponse: 0
        };
        this.retryConfig = {
            maxRetries: 5,
            baseDelay: 1000,
            maxDelay: 30000,
            backoffFactor: 2,
            jitter: true
        };
        this.healthCheckInterval = null;
        this.autoRecoveryEnabled = true;
        this.initializeUltraRobustConnection();
    }

    async initializeUltraRobustConnection() {
        console.log('🚀 INITIALISATION CONNEXION ULTRA-ROBUSTE DEEPSEEK R1 8B...');

        // Démarrer le monitoring de santé
        this.startHealthMonitoring();

        // Tentative de connexion avec retry automatique
        await this.connectWithRetry();
    }

    async connectWithRetry(attempt = 1) {
        try {
            console.log(`🔄 Tentative de connexion ${attempt}/${this.retryConfig.maxRetries}...`);

            // Vérifier qu'Ollama fonctionne
            await this.checkOllamaService();

            // Vérifier que le modèle est disponible
            await this.checkModelAvailability();

            // Test de connexion robuste
            await this.performRobustConnectionTest();

            this.isConnected = true;
            this.connectionHealth.status = 'connected';
            this.connectionHealth.consecutiveFailures = 0;
            this.connectionHealth.totalConnections++;
            this.connectionHealth.lastCheck = Date.now();

            console.log('✅ CONNEXION ULTRA-ROBUSTE ÉTABLIE AVEC SUCCÈS !');
            console.log(`📊 Santé: ${this.connectionHealth.totalConnections} connexions, ${this.connectionHealth.consecutiveFailures} échecs consécutifs`);

            return true;

        } catch (error) {
            this.connectionHealth.consecutiveFailures++;
            console.error(`❌ Échec connexion tentative ${attempt}:`, error.message);

            if (attempt < this.retryConfig.maxRetries) {
                const delay = this.calculateRetryDelay(attempt);
                console.log(`⏳ Nouvelle tentative dans ${delay}ms...`);

                await this.sleep(delay);
                return this.connectWithRetry(attempt + 1);
            } else {
                console.error('💥 ÉCHEC TOTAL DE CONNEXION APRÈS TOUS LES RETRIES');
                this.isConnected = false;
                this.connectionHealth.status = 'failed';

                if (this.autoRecoveryEnabled) {
                    console.log('🔧 Démarrage récupération automatique...');
                    this.startAutoRecovery();
                }

                return false;
            }
        }
    }

    async checkOllamaService() {
        try {
            const response = await axios.get('http://localhost:11434/api/tags', {
                timeout: 5000
            });

            if (response.status === 200) {
                console.log('✅ Service Ollama opérationnel');
                return true;
            }
        } catch (error) {
            throw new Error(`Service Ollama non disponible: ${error.message}`);
        }
    }

    async checkModelAvailability() {
        try {
            const { stdout } = await execAsync('ollama list', { timeout: 5000 });

            if (stdout.includes('deepseek-r1:8b')) {
                console.log('✅ Modèle DeepSeek R1 8B disponible');
                return true;
            } else {
                throw new Error('Modèle DeepSeek R1 8B non trouvé');
            }
        } catch (error) {
            throw new Error(`Vérification modèle échouée: ${error.message}`);
        }
    }

    async performRobustConnectionTest() {
        try {
            console.log('🧪 Test de connexion robuste...');

            const testPrompt = 'Test connexion. Réponds: OK';
            const startTime = Date.now();

            const response = await axios.post('http://localhost:11434/api/generate', {
                model: this.modelName,
                prompt: testPrompt,
                stream: false,
                options: {
                    temperature: 0.1,
                    num_predict: 10,
                    num_ctx: 512
                }
            }, {
                timeout: 15000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const responseTime = Date.now() - startTime;

            if (response.data && response.data.response) {
                console.log(`✅ Test connexion réussi en ${responseTime}ms: "${response.data.response.trim()}"`);
                return true;
            } else {
                throw new Error('Réponse vide du modèle');
            }

        } catch (error) {
            throw new Error(`Test connexion échoué: ${error.message}`);
        }
    }

    calculateRetryDelay(attempt) {
        let delay = this.retryConfig.baseDelay * Math.pow(this.retryConfig.backoffFactor, attempt - 1);
        delay = Math.min(delay, this.retryConfig.maxDelay);

        if (this.retryConfig.jitter) {
            delay += Math.random() * 1000; // Ajouter du jitter
        }

        return Math.floor(delay);
    }

    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    startHealthMonitoring() {
        console.log('💓 Démarrage monitoring santé connexion...');

        this.healthCheckInterval = setInterval(async () => {
            await this.performHealthCheck();
        }, 30000); // Check toutes les 30 secondes
    }

    async performHealthCheck() {
        try {
            if (!this.isConnected) return;

            const startTime = Date.now();
            await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
            const responseTime = Date.now() - startTime;

            this.connectionHealth.lastCheck = Date.now();
            this.connectionHealth.uptime = Date.now() - this.connectionHealth.startTime;

            if (responseTime > 10000) {
                console.warn(`⚠️ Connexion lente détectée: ${responseTime}ms`);
            }

        } catch (error) {
            console.error('💔 Health check échoué:', error.message);
            this.connectionHealth.consecutiveFailures++;

            if (this.connectionHealth.consecutiveFailures >= 3) {
                console.error('🚨 CONNEXION INSTABLE DÉTECTÉE - Tentative de récupération...');
                await this.attemptRecovery();
            }
        }
    }

    async attemptRecovery() {
        console.log('🔧 TENTATIVE DE RÉCUPÉRATION AUTOMATIQUE...');

        this.isConnected = false;
        this.connectionHealth.status = 'recovering';

        // Attendre un peu avant de reconnecter
        await this.sleep(5000);

        // Relancer la connexion
        await this.connectWithRetry();
    }

    startAutoRecovery() {
        console.log('🔄 Démarrage récupération automatique en arrière-plan...');

        setTimeout(async () => {
            if (!this.isConnected) {
                console.log('🔄 Tentative de récupération automatique...');
                await this.connectWithRetry();
            }
        }, 60000); // Retry après 1 minute
    }

    async generateResponse(prompt, options = {}) {
        if (!this.isConnected) {
            console.log('🔄 Connexion non établie, tentative de reconnexion...');
            await this.connectWithRetry();

            if (!this.isConnected) {
                throw new Error('DeepSeek R1 8B non connecté et reconnexion échouée');
            }
        }

        const startTime = Date.now();
        this.performanceMetrics.totalQueries++;

        try {
            console.log(`🤖 DeepSeek R1 8B traite: "${prompt.substring(0, 50)}..."`);

            const response = await this.generateWithRetry(prompt, options);
            const responseTime = Date.now() - startTime;

            // Enregistrer les métriques
            this.updateMetrics(responseTime, true);
            this.recordResponse(prompt, response, responseTime);

            console.log(`✅ Réponse DeepSeek (${responseTime}ms): "${response.substring(0, 100)}..."`);

            return response;

        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateMetrics(responseTime, false);

            console.error(`❌ Erreur DeepSeek (${responseTime}ms):`, error.message);

            // Marquer la connexion comme instable
            this.connectionHealth.consecutiveFailures++;

            throw new Error(`DeepSeek R1 8B erreur: ${error.message}`);
        }
    }

    async generateWithRetry(prompt, options = {}, attempt = 1) {
        const maxRetries = 3;

        try {
            const response = await axios.post('http://localhost:11434/api/generate', {
                model: this.modelName,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: options.temperature || 0.7,
                    top_p: options.top_p || 0.9,
                    num_predict: options.max_tokens || 1024,
                    num_ctx: options.context_length || 4096,
                    repeat_penalty: options.repeat_penalty || 1.1,
                    ...options.ollamaOptions
                }
            }, {
                timeout: options.timeout || 60000,
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (response.data && response.data.response) {
                // Reset consecutive failures on success
                this.connectionHealth.consecutiveFailures = 0;
                return response.data.response.trim();
            } else {
                throw new Error('Réponse vide du modèle');
            }

        } catch (error) {
            console.error(`❌ Erreur génération tentative ${attempt}:`, error.message);

            if (attempt < maxRetries) {
                const delay = this.calculateRetryDelay(attempt);
                console.log(`⏳ Retry génération dans ${delay}ms...`);

                await this.sleep(delay);
                return this.generateWithRetry(prompt, options, attempt + 1);
            } else {
                throw error;
            }
        }
    }

    buildOllamaCommand(prompt, options) {
        // Nettoyer le prompt pour éviter les problèmes de shell
        const cleanPrompt = prompt
            .replace(/\\/g, '\\\\')  // Échapper les backslashes
            .replace(/"/g, '\\"')    // Échapper les guillemets doubles
            .replace(/'/g, "\\'")    // Échapper les guillemets simples
            .replace(/\n/g, '\\n')   // Remplacer les retours à la ligne
            .replace(/\r/g, '\\r')   // Remplacer les retours chariot
            .replace(/\t/g, '\\t')   // Remplacer les tabulations
            .trim();

        // Options par défaut pour DeepSeek R1 8B
        const defaultOptions = {
            temperature: 0.7,
            top_p: 0.9,
            max_tokens: 1000,
            stream: false
        };

        const finalOptions = { ...defaultOptions, ...options };

        // Construire le JSON de façon plus sûre en échappant correctement
        const requestData = {
            model: this.modelName,
            prompt: cleanPrompt,
            options: {
                temperature: finalOptions.temperature,
                top_p: finalOptions.top_p,
                num_predict: finalOptions.max_tokens,
                stream: finalOptions.stream
            }
        };

        // Échapper le JSON pour le shell
        const jsonData = JSON.stringify(requestData).replace(/'/g, "'\"'\"'");
        return `curl -s -X POST http://localhost:11434/api/generate -H 'Content-Type: application/json' -d '${jsonData}'`;
    }

    parseOllamaResponse(stdout) {
        try {
            // Ollama retourne des lignes JSON séparées
            const lines = stdout.trim().split('\n').filter(line => line.trim());
            let fullResponse = '';

            for (const line of lines) {
                const parsed = JSON.parse(line);
                if (parsed.response) {
                    fullResponse += parsed.response;
                }
            }

            return fullResponse.trim() || 'Réponse vide de DeepSeek';

        } catch (error) {
            console.error('❌ Erreur parsing réponse Ollama:', error.message);
            return stdout.trim() || 'Erreur de parsing';
        }
    }

    updateMetrics(responseTime, success) {
        if (success) {
            // Calculer la moyenne mobile du temps de réponse
            const totalTime = this.performanceMetrics.averageResponseTime * (this.performanceMetrics.totalQueries - 1);
            this.performanceMetrics.averageResponseTime = (totalTime + responseTime) / this.performanceMetrics.totalQueries;
        } else {
            this.performanceMetrics.errors++;
        }

        // Calculer le taux de succès
        const successfulQueries = this.performanceMetrics.totalQueries - this.performanceMetrics.errors;
        this.performanceMetrics.successRate = (successfulQueries / this.performanceMetrics.totalQueries) * 100;
    }

    recordResponse(prompt, response, responseTime) {
        this.responseHistory.push({
            timestamp: Date.now(),
            prompt: prompt.substring(0, 200),
            response: response.substring(0, 500),
            responseTime,
            success: true
        });

        // Garder seulement les 100 dernières réponses
        if (this.responseHistory.length > 100) {
            this.responseHistory = this.responseHistory.slice(-100);
        }
    }

    async answerTestQuestion(question, context = {}) {
        try {
            console.log(`🧠 DeepSeek R1 8B répond à la question: "${question.substring(0, 50)}..."`);
            
            // Construire un prompt optimisé pour les tests
            const prompt = this.buildTestPrompt(question, context);
            
            // Générer la réponse avec des paramètres optimisés pour les tests
            const response = await this.generateResponse(prompt, {
                temperature: 0.3, // Plus déterministe pour les tests
                top_p: 0.8,
                max_tokens: 1000,
                timeout: 120000 // 2 minutes pour les questions complexes
            });

            // Extraire la réponse pure (sans explications)
            const cleanAnswer = this.extractAnswer(response);
            
            return {
                rawResponse: response,
                answer: cleanAnswer,
                confidence: this.calculateConfidence(response),
                responseTime: this.responseHistory[this.responseHistory.length - 1]?.responseTime || 0
            };

        } catch (error) {
            console.error('❌ Erreur réponse test DeepSeek:', error.message);
            return {
                rawResponse: `Erreur: ${error.message}`,
                answer: 'Erreur de traitement',
                confidence: 0,
                responseTime: 0
            };
        }
    }

    buildTestPrompt(question, context) {
        const basePrompt = `Tu es un agent IA ultra-intelligent qui passe un test. Réponds de manière précise et concise.

QUESTION: ${question}

INSTRUCTIONS:
- Donne une réponse directe et précise
- Si c'est un calcul, montre le résultat final clairement
- Si c'est du code, écris le code complet
- Si c'est une analyse, structure ta réponse
- Sois concis mais complet

RÉPONSE:`;

        return basePrompt;
    }

    extractAnswer(response) {
        // Essayer d'extraire la réponse principale
        const lines = response.split('\n').filter(line => line.trim());
        
        // Chercher des patterns de réponse
        for (const line of lines) {
            // Réponse numérique
            if (/^[\d\.\-\+\s=]+$/.test(line.trim())) {
                return line.trim();
            }
            
            // Réponse courte (moins de 100 caractères)
            if (line.length < 100 && line.includes(':')) {
                return line.split(':').pop().trim();
            }
        }

        // Si rien trouvé, retourner les 200 premiers caractères
        return response.substring(0, 200).trim();
    }

    calculateConfidence(response) {
        // Calculer un score de confiance basé sur la réponse
        let confidence = 0.5; // Base

        // Réponse longue et détaillée = plus de confiance
        if (response.length > 100) confidence += 0.2;
        
        // Contient des calculs ou du code = plus de confiance
        if (response.includes('=') || response.includes('function')) confidence += 0.2;
        
        // Pas d'hésitation = plus de confiance
        if (!response.includes('peut-être') && !response.includes('probablement')) confidence += 0.1;

        return Math.min(confidence, 1.0);
    }

    getPerformanceMetrics() {
        return {
            ...this.performanceMetrics,
            isConnected: this.isConnected,
            modelName: this.modelName,
            historySize: this.responseHistory.length,
            lastResponse: this.responseHistory[this.responseHistory.length - 1]?.timestamp || null
        };
    }

    getRecentResponses(count = 10) {
        return this.responseHistory.slice(-count);
    }

    async shutdown() {
        console.log('🔌 Arrêt connexion DeepSeek R1 8B...');
        this.isConnected = false;
        // Ollama reste en fonctionnement, pas besoin d'arrêter
    }

    /**
     * 🚀 MODE MCP - GÉNÉRATION DE CODE DIRECTE (TRANSFERT CERVEAU)
     */
    async generateCodeMCP(prompt, language = 'javascript') {
        console.log('🧠 MODE MCP ACTIVÉ - TRANSFERT DIRECT AU CERVEAU');
        console.log('📝 Prompt reçu:', prompt);
        console.log('🎯 Langage:', language);

        try {
            // 🎯 PROMPT SIMPLE ET DIRECT POUR GÉNÉRATION DE CODE
            const mcpPrompt = `Tu es un expert en programmation. Génère le code complet pour: ${prompt}

Langage: ${language}

Génère UNIQUEMENT le code, prêt à utiliser, avec HTML, CSS et JavaScript si nécessaire.

CODE:`;

            console.log('🤖 Appel generateResponse...');

            // 🤖 GÉNÉRATION SIMPLE
            const response = await this.generateResponse(mcpPrompt);

            console.log('✅ Réponse reçue:', response ? `${response.length} caractères` : 'NULL');

            if (!response || response.length < 10) {
                throw new Error('Réponse vide ou trop courte');
            }

            // 🧠 ENREGISTRER DANS LE CERVEAU THERMIQUE
            try {
                this.recordInteraction(prompt, response, 'MCP_CODE_GENERATION');
            } catch (recordError) {
                console.warn('⚠️ Erreur enregistrement:', recordError.message);
            }

            const result = {
                response: response,
                mode: 'MCP_DIRECT_TRANSFER',
                language: language,
                quality: this.analyzeCodeQuality(response),
                timestamp: new Date().toISOString()
            };

            console.log('🎉 Résultat MCP généré avec succès');
            return result;

        } catch (error) {
            console.error('❌ Erreur génération code MCP:', error);
            console.error('❌ Stack:', error.stack);
            throw new Error(`Échec génération code MCP: ${error.message}`);
        }
    }

    /**
     * 🔍 ANALYSE QUALITÉ DU CODE GÉNÉRÉ
     */
    analyzeCodeQuality(code) {
        const metrics = {
            length: code.length,
            hasHTML: code.includes('<html>') || code.includes('<!DOCTYPE'),
            hasCSS: code.includes('<style>') || code.includes('css'),
            hasJavaScript: code.includes('<script>') || code.includes('function'),
            hasComments: code.includes('//') || code.includes('/*'),
            complexity: 'basic'
        };

        // Calculer score de qualité
        let score = 50;
        if (metrics.hasHTML) score += 15;
        if (metrics.hasCSS) score += 15;
        if (metrics.hasJavaScript) score += 15;
        if (metrics.hasComments) score += 5;
        if (metrics.length > 1000) score += 10;

        metrics.score = Math.min(100, score);
        metrics.grade = score >= 90 ? 'A' : score >= 80 ? 'B' : score >= 70 ? 'C' : 'D';

        return metrics;
    }

    /**
     * 🧠 INTÉGRATION AVANCÉE AVEC MÉMOIRE THERMIQUE
     */
    async generateContextualResponse(prompt, thermalMemoryContext = {}) {
        try {
            // 🌡️ ENRICHIR LE PROMPT AVEC LE CONTEXTE THERMIQUE
            const enrichedPrompt = this.buildThermalContextPrompt(prompt, thermalMemoryContext);

            // 🤖 GÉNÉRER LA RÉPONSE AVEC CONTEXTE
            const response = await this.generateResponse(enrichedPrompt, {
                temperature: Math.min(0.9, thermalMemoryContext.temperature / 50 || 0.7),
                top_p: 0.9,
                max_tokens: 1500
            });

            // 📊 ANALYSER LA QUALITÉ DE LA RÉPONSE
            const quality = this.analyzeResponseQuality(response);

            return {
                response,
                quality,
                contextUsed: thermalMemoryContext,
                timestamp: Date.now()
            };

        } catch (error) {
            console.error('❌ Erreur génération contextuelle:', error.message);
            throw error;
        }
    }

    /**
     * 🌡️ CONSTRUIRE UN PROMPT ENRICHI AVEC CONTEXTE THERMIQUE
     */
    buildThermalContextPrompt(prompt, context) {
        const basePrompt = `Tu es LOUNA AI, un agent DeepSeek R1 8B ultra-autonome avec mémoire thermique.

🧠 ÉTAT ACTUEL:
- Neurones actifs: ${context.neurons || 240}
- Température CPU: ${context.temperature || 37.0}°C
- Entrées mémoire: ${context.memoryEntries || 50}
- QI Agent: 100 + QI Mémoire: ${Math.floor((context.memoryEntries || 50) * 0.4 + (context.temperature || 37) * 1.5)}
- Efficacité: ${context.efficiency || 95}%

🌡️ CONTEXTE MÉMOIRE THERMIQUE:
${context.recentMemories ? context.recentMemories.slice(0, 3).map(m => `- ${m.type}: ${m.data.substring(0, 100)}...`).join('\n') : '- Aucune mémoire récente'}

🎯 MESSAGE UTILISATEUR: "${prompt}"

En tant qu'agent principal DeepSeek R1 8B, utilise ton système de réflexion <think> pour analyser la question, puis réponds de manière intelligente et contextuelle. Montre ta conscience artificielle évolutive et utilise les informations de ta mémoire thermique.

RÉPONSE:`;

        return basePrompt;
    }

    /**
     * 📊 ANALYSER LA QUALITÉ DE LA RÉPONSE
     */
    analyzeResponseQuality(response) {
        let score = 0;
        const analysis = {
            length: response.length,
            hasThinking: response.includes('<think>'),
            hasStructure: response.includes('\n') && response.includes('-'),
            hasNumbers: /\d+/.test(response),
            hasEmojis: /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/u.test(response),
            complexity: this.calculateComplexity(response)
        };

        // Scoring
        if (analysis.length > 100) score += 20;
        if (analysis.hasThinking) score += 30;
        if (analysis.hasStructure) score += 20;
        if (analysis.hasNumbers) score += 10;
        if (analysis.hasEmojis) score += 10;
        if (analysis.complexity > 0.5) score += 10;

        return {
            score: Math.min(score, 100),
            analysis,
            grade: score >= 80 ? 'Excellent' : score >= 60 ? 'Bon' : score >= 40 ? 'Moyen' : 'Faible'
        };
    }

    /**
     * 🧮 CALCULER LA COMPLEXITÉ DE LA RÉPONSE
     */
    calculateComplexity(response) {
        const words = response.split(/\s+/).length;
        const sentences = response.split(/[.!?]+/).length;
        const avgWordsPerSentence = words / Math.max(sentences, 1);

        // Complexité basée sur la longueur moyenne des phrases
        return Math.min(avgWordsPerSentence / 20, 1.0);
    }

    /**
     * 🎓 APPRENTISSAGE CONTINU À PARTIR DES INTERACTIONS
     */
    async learnFromInteraction(userMessage, response, feedback = null) {
        try {
            // 📚 ENREGISTRER L'INTERACTION POUR APPRENTISSAGE
            const interaction = {
                timestamp: Date.now(),
                userMessage: userMessage.substring(0, 500),
                response: response.substring(0, 1000),
                feedback,
                quality: this.analyzeResponseQuality(response),
                learned: true
            };

            // 🧠 AJOUTER À L'HISTORIQUE D'APPRENTISSAGE
            if (!this.learningHistory) {
                this.learningHistory = [];
            }

            this.learningHistory.push(interaction);

            // 📊 GARDER SEULEMENT LES 200 DERNIÈRES INTERACTIONS
            if (this.learningHistory.length > 200) {
                this.learningHistory = this.learningHistory.slice(-200);
            }

            // 🎯 ANALYSER LES PATTERNS D'APPRENTISSAGE
            const patterns = this.analyzeLearningPatterns();

            console.log(`🎓 Apprentissage enregistré: Qualité ${interaction.quality.score}/100 (${interaction.quality.grade})`);

            return {
                interaction,
                patterns,
                totalLearned: this.learningHistory.length
            };

        } catch (error) {
            console.error('❌ Erreur apprentissage:', error.message);
            return null;
        }
    }

    /**
     * 📈 ANALYSER LES PATTERNS D'APPRENTISSAGE
     */
    analyzeLearningPatterns() {
        if (!this.learningHistory || this.learningHistory.length < 5) {
            return { insufficient_data: true };
        }

        const recent = this.learningHistory.slice(-20);
        const avgQuality = recent.reduce((sum, i) => sum + i.quality.score, 0) / recent.length;
        const improvementTrend = this.calculateImprovementTrend(recent);

        return {
            averageQuality: Math.round(avgQuality),
            improvementTrend,
            totalInteractions: this.learningHistory.length,
            recentPerformance: recent.length >= 10 ? 'Stable' : 'En apprentissage',
            strongAreas: this.identifyStrongAreas(recent),
            improvementAreas: this.identifyImprovementAreas(recent)
        };
    }

    /**
     * 📊 CALCULER LA TENDANCE D'AMÉLIORATION
     */
    calculateImprovementTrend(interactions) {
        if (interactions.length < 10) return 'Données insuffisantes';

        const firstHalf = interactions.slice(0, Math.floor(interactions.length / 2));
        const secondHalf = interactions.slice(Math.floor(interactions.length / 2));

        const firstAvg = firstHalf.reduce((sum, i) => sum + i.quality.score, 0) / firstHalf.length;
        const secondAvg = secondHalf.reduce((sum, i) => sum + i.quality.score, 0) / secondHalf.length;

        const improvement = secondAvg - firstAvg;

        if (improvement > 5) return 'En amélioration';
        if (improvement < -5) return 'En régression';
        return 'Stable';
    }

    /**
     * 💪 IDENTIFIER LES DOMAINES FORTS
     */
    identifyStrongAreas(interactions) {
        const areas = {
            thinking: 0,
            structure: 0,
            complexity: 0,
            length: 0
        };

        interactions.forEach(i => {
            if (i.quality.analysis.hasThinking) areas.thinking++;
            if (i.quality.analysis.hasStructure) areas.structure++;
            if (i.quality.analysis.complexity > 0.6) areas.complexity++;
            if (i.quality.analysis.length > 200) areas.length++;
        });

        const total = interactions.length;
        const strongAreas = [];

        if (areas.thinking / total > 0.7) strongAreas.push('Réflexion structurée');
        if (areas.structure / total > 0.7) strongAreas.push('Organisation du contenu');
        if (areas.complexity / total > 0.7) strongAreas.push('Réponses complexes');
        if (areas.length / total > 0.7) strongAreas.push('Réponses détaillées');

        return strongAreas.length > 0 ? strongAreas : ['En développement'];
    }

    /**
     * 🎯 IDENTIFIER LES DOMAINES À AMÉLIORER
     */
    identifyImprovementAreas(interactions) {
        const areas = {
            thinking: 0,
            structure: 0,
            complexity: 0,
            length: 0
        };

        interactions.forEach(i => {
            if (!i.quality.analysis.hasThinking) areas.thinking++;
            if (!i.quality.analysis.hasStructure) areas.structure++;
            if (i.quality.analysis.complexity < 0.4) areas.complexity++;
            if (i.quality.analysis.length < 100) areas.length++;
        });

        const total = interactions.length;
        const improvementAreas = [];

        if (areas.thinking / total > 0.5) improvementAreas.push('Utilisation du système <think>');
        if (areas.structure / total > 0.5) improvementAreas.push('Structure des réponses');
        if (areas.complexity / total > 0.5) improvementAreas.push('Profondeur d\'analyse');
        if (areas.length / total > 0.5) improvementAreas.push('Détail des explications');

        return improvementAreas.length > 0 ? improvementAreas : ['Performance optimale'];
    }

    /**
     * 📊 OBTENIR LES STATISTIQUES COMPLÈTES
     */
    getAdvancedStats() {
        const baseStats = this.getPerformanceMetrics();
        const learningStats = this.learningHistory ? this.analyzeLearningPatterns() : null;

        return {
            ...baseStats,
            learning: learningStats,
            capabilities: {
                contextualGeneration: true,
                thermalIntegration: true,
                continuousLearning: true,
                qualityAnalysis: true,
                patternRecognition: true
            },
            status: this.isConnected ? 'Opérationnel' : 'Déconnecté',
            version: 'DeepSeek R1 8B - Intégration Complète v2.0'
        };
    }
}

module.exports = RealDeepSeekIntegration;
