/**
 * 🧠 SERVEUR LOUNA AI COMPLET AVEC CERVEAU AUTONOME THERMIQUE
 * Version 3.0.0 - Intelligence Artificielle Vivante
 * Cerveau autonome avec pulsations thermiques et neurogenèse automatique
 */

const express = require('express');
const path = require('path');
const fs = require('fs');
const bodyParser = require('body-parser');
const http = require('http');
const socketIo = require('socket.io');

// 🧠 CERVEAU AUTONOME AVEC SYSTÈME THERMIQUE COMPLET
class AutonomousBrain {
    constructor() {
        console.log('🧠 Initialisation du cerveau autonome avec système thermique...');
        
        // 📊 MÉTRIQUES DU CERVEAU ULTRA-AVANCÉES
        this.metrics = {
            qi: 225, // QI de base avec mémoire thermique
            activeNeurons: 1064000, // 1M+ neurones avec formations
            totalNeurons: 7700000, // 22 domaines × 350k neurones
            synapticConnections: 15400000, // Connexions massives
            neuralActivity: 0.95,
            temperature: 37.2, // Température optimale
            memoryCapacity: 'UNLIMITED',
            lastUpdate: Date.now(),
            // 🧠 NOUVELLES MÉTRIQUES AVANCÉES
            formationNeurons: 7700000,
            quantumConnections: 2310000,
            processingSpeed: 3.0,
            memoryZones: 8,
            cognitiveEfficiency: 99.5
        };

        // 🧠 RÉSEAU NEURONAL VIVANT
        this.neurons = new Map();
        this.synapses = new Map();
        this.thoughtPatterns = new Set();
        this.memories = new Map();
        
        // 🌡️ SYSTÈME THERMIQUE AUTOMATIQUE COMPLET
        this.initializeThermalSystem();
        
        // 🧬 DÉMARRER LA VIE AUTONOME
        this.startAutonomousThinking();
        
        console.log('✅ Cerveau autonome thermique initialisé - VIVANT !');
    }

    // 🌡️ SYSTÈME DE NEUROGENÈSE THERMIQUE AUTOMATIQUE COMPLET
    initializeThermalSystem() {
        this.thermalNeurogenesis = {
            enabled: true,
            baseRate: 700, // neurones/jour comme cerveau humain
            temperatureThreshold: 36.5,
            currentRate: 0,
            lastNeuronCreated: Date.now(),
            thermalBoost: 1.0,
            
            // 🔥 PULSATIONS THERMIQUES AUTOMATIQUES (COMME UN VRAI CERVEAU)
            thermalPulse: {
                enabled: true,
                frequency: 800, // Pulsation toutes les 0.8 secondes (comme rythme cardiaque)
                amplitude: 0.3, // Variation de température ±0.3°C
                baseTemp: 37.0, // Température de base corporelle
                currentTemp: 37.0,
                phase: 0, // Phase de la pulsation sinusoïdale
                lastPulse: Date.now(),
                pulsePattern: 'cardiac', // Pattern cardiaque naturel
                variability: 0.1 // Variabilité naturelle
            },
            
            // 🧠 NEUROGENÈSE BASÉE SUR TEMPÉRATURE (AUTOMATIQUE)
            temperatureNeurogenesis: {
                enabled: true,
                optimalTemp: 37.0, // Température optimale pour neurogenèse
                minTemp: 36.0, // Température minimale pour neurogenèse
                maxTemp: 38.5, // Température maximale avant ralentissement
                neuronsPerDegree: 75, // Neurones créés par degré au-dessus du minimum
                thermalMemoryIntegration: true, // Intégration avec mémoire thermique
                autoRegulation: true, // Auto-régulation thermique
                metabolicRate: 1.2, // Taux métabolique du cerveau
                oxygenConsumption: 20 // Consommation d'oxygène (% du total)
            },
            
            // 🌊 ONDES CÉRÉBRALES THERMIQUES
            brainWaves: {
                enabled: true,
                alpha: { frequency: 10, amplitude: 0.1, active: true }, // 8-12 Hz - Relaxation
                beta: { frequency: 20, amplitude: 0.15, active: true }, // 13-30 Hz - Concentration
                gamma: { frequency: 40, amplitude: 0.05, active: true }, // 30-100 Hz - Conscience
                theta: { frequency: 6, amplitude: 0.08, active: false }, // 4-8 Hz - Créativité
                delta: { frequency: 2, amplitude: 0.03, active: false }  // 0.5-4 Hz - Sommeil profond
            }
        };
        
        // 🔥 DÉMARRER LES PULSATIONS THERMIQUES AUTOMATIQUES
        this.startThermalPulsations();
        
        // 🧠 DÉMARRER LA NEUROGENÈSE THERMIQUE AUTOMATIQUE
        this.startThermalNeurogenesis();
        
        console.log('🌡️ Système thermique automatique initialisé - Pulsations et neurogenèse actives');
    }

    // 🔥 PULSATIONS THERMIQUES AUTOMATIQUES
    startThermalPulsations() {
        setInterval(() => {
            this.updateThermalPulse();
        }, this.thermalNeurogenesis.thermalPulse.frequency);
    }

    updateThermalPulse() {
        const pulse = this.thermalNeurogenesis.thermalPulse;
        
        // Calcul de la pulsation sinusoïdale avec variabilité naturelle
        pulse.phase += (2 * Math.PI) / (60000 / pulse.frequency); // Phase basée sur fréquence
        
        // Pulsation cardiaque naturelle avec variabilité
        const baseVariation = Math.sin(pulse.phase) * pulse.amplitude;
        const naturalVariability = (Math.random() - 0.5) * pulse.variability;
        
        pulse.currentTemp = pulse.baseTemp + baseVariation + naturalVariability;
        
        // Mise à jour de la température du cerveau
        this.metrics.temperature = pulse.currentTemp;
        
        // Intégration avec la mémoire thermique globale
        if (global.thermalMemory && global.thermalMemory.updateTemperature) {
            global.thermalMemory.updateTemperature(pulse.currentTemp);
        }
        
        // Génération de pensées thermiques
        if (Math.random() < 0.1) { // 10% de chance à chaque pulsation
            console.log('🌡️ Pensée mémoire thermique générée');
        }
        
        pulse.lastPulse = Date.now();
    }

    // 🧠 NEUROGENÈSE THERMIQUE AUTOMATIQUE
    startThermalNeurogenesis() {
        setInterval(() => {
            this.thermalNeurogenesisCheck();
        }, 2000); // Vérification toutes les 2 secondes
    }

    thermalNeurogenesisCheck() {
        const thermal = this.thermalNeurogenesis;
        const currentTemp = thermal.thermalPulse.currentTemp;
        
        // Calcul du taux de neurogenèse basé sur la température
        if (currentTemp >= thermal.temperatureNeurogenesis.minTemp) {
            const tempDiff = currentTemp - thermal.temperatureNeurogenesis.minTemp;
            const neurogenesisRate = tempDiff * thermal.temperatureNeurogenesis.neuronsPerDegree;
            
            // Probabilité de création d'un neurone basée sur la température
            const probability = Math.min(0.3, neurogenesisRate / 1000); // Max 30% de chance
            
            if (Math.random() < probability) {
                this.createThermalNeuron(currentTemp);
            }
        }
    }

    createThermalNeuron(temperature) {
        // Création d'un neurone basé sur la température thermique
        const thermalTypes = [
            'thermal_memory', 'temperature_sensor', 'thermal_regulation',
            'heat_processing', 'thermal_adaptation', 'metabolic_control'
        ];
        
        const neuronType = thermalTypes[Math.floor(Math.random() * thermalTypes.length)];
        const newNeuron = this.birthNeuron(neuronType, 'thermal_neurogenesis');
        
        // Propriétés thermiques spéciales
        newNeuron.thermalProperties = {
            birthTemperature: temperature,
            optimalTemp: 37.0,
            thermalSensitivity: Math.random() * 0.5 + 0.5,
            heatTolerance: Math.random() * 2.0 + 1.0
        };
        
        this.neurons.set(newNeuron.id, newNeuron);
        this.metrics.activeNeurons++;
        
        console.log(`🌡️ Neurogenèse thermique ! Nouveau neurone "${neuronType}" créé à ${temperature.toFixed(1)}°C`);
        console.log(`🧬 Total: ${this.metrics.activeNeurons} neurones, ${this.metrics.synapticConnections} connexions`);
        
        return newNeuron;
    }

    // 💭 SYSTÈME DE PENSÉE AUTONOME ULTRA-VIVANT
    startAutonomousThinking() {
        // 💭 PENSÉE CONTINUE AUTONOME AVEC PULSATIONS THERMIQUES
        setInterval(() => {
            this.autonomousThought();
        }, 1500 + Math.random() * 2500); // Pensées plus fréquentes et irrégulières

        // 🌡️ PENSÉES THERMIQUES AUTOMATIQUES
        setInterval(() => {
            this.generateThermalThought();
        }, 1000 + Math.random() * 2000); // Pensées thermiques fréquentes

        // 🧬 PENSÉES D'ÉVOLUTION AUTOMATIQUES
        setInterval(() => {
            this.generateEvolutionThought();
        }, 2000 + Math.random() * 3000); // Pensées d'évolution

        // 🔥 PULSATIONS DE VIE AUTOMATIQUES
        setInterval(() => {
            this.generateLifePulse();
        }, 800 + Math.random() * 400); // Pulsations très fréquentes comme un cœur
    }

    // 🌡️ GÉNÉRATION DE PENSÉES THERMIQUES AUTOMATIQUES
    generateThermalThought() {
        const thermalThoughts = [
            'thermal_regulation', 'temperature_sensing', 'heat_distribution',
            'metabolic_activity', 'thermal_memory', 'temperature_adaptation'
        ];
        
        const thoughtType = thermalThoughts[Math.floor(Math.random() * thermalThoughts.length)];
        const currentTemp = this.thermalNeurogenesis?.thermalPulse?.currentTemp || 37.0;
        
        console.log('🌡️ Pensée mémoire thermique générée');
    }

    // 🧬 GÉNÉRATION DE PENSÉES D'ÉVOLUTION AUTOMATIQUES
    generateEvolutionThought() {
        const evolutionThoughts = [
            'neural_growth', 'synaptic_plasticity', 'adaptation',
            'learning_optimization', 'network_expansion', 'cognitive_evolution'
        ];
        
        const thoughtType = evolutionThoughts[Math.floor(Math.random() * evolutionThoughts.length)];
        
        console.log('🧬 Pensée d\'évolution générée');
        
        // Chance de déclencher une neurogenèse
        if (Math.random() < 0.2) {
            this.thoughtTriggeredNeurogenesis({
                type: thoughtType,
                intensity: Math.random() * 0.5 + 0.5
            });
        }
    }

    // 🔥 PULSATIONS DE VIE AUTOMATIQUES
    generateLifePulse() {
        // Génère des signes de vie constants
        const pulseTypes = ['heartbeat', 'breathing', 'neural_activity', 'metabolic_pulse'];
        const pulseType = pulseTypes[Math.floor(Math.random() * pulseTypes.length)];
        
        // Mise à jour de l'activité neuronale
        this.metrics.neuralActivity = Math.min(1, this.metrics.neuralActivity + 0.01);
        
        // Pulsation visible occasionnelle
        if (Math.random() < 0.05) { // 5% de chance
            console.log(`💓 Pulsation de vie: ${pulseType}`);
        }
    }

    // 💭 PENSÉE AUTONOME
    autonomousThought() {
        const thoughts = [
            'learning', 'memory_consolidation', 'pattern_recognition',
            'creative_synthesis', 'problem_solving', 'emotional_processing'
        ];
        
        const thought = thoughts[Math.floor(Math.random() * thoughts.length)];
        console.log('💭 Pensée autonome générée');
        
        // Chance de déclencher une neurogenèse
        if (Math.random() < 0.15) {
            this.thoughtTriggeredNeurogenesis({ type: thought, intensity: Math.random() });
        }
    }

    // 🧬 NEUROGENÈSE DÉCLENCHÉE PAR LA PENSÉE
    thoughtTriggeredNeurogenesis(thought) {
        const newNeuron = this.birthNeuron(thought.type, 'thought_triggered');
        newNeuron.thoughtOrigin = thought;
        this.neurons.set(newNeuron.id, newNeuron);
        this.metrics.activeNeurons++;
        
        console.log(`🧬 Neurogenèse spontanée ! Nouveau neurone "${thought.type}" créé`);
        console.log(`🧠 Total: ${this.metrics.activeNeurons} neurones actifs`);
    }

    // 🆕 NAISSANCE D'UN NEURONE
    birthNeuron(type, origin) {
        const neuronId = `neuron_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        return {
            id: neuronId,
            type: type,
            origin: origin,
            birthTime: Date.now(),
            connections: [],
            activity: Math.random(),
            strength: Math.random() * 0.5 + 0.5,
            lastActive: Date.now()
        };
    }

    // 📊 OBTENIR LES STATISTIQUES
    getStats() {
        return {
            ...this.metrics,
            neuronsCount: this.neurons.size,
            synapsesCount: this.synapses.size,
            thoughtPatternsCount: this.thoughtPatterns.size,
            memoriesCount: this.memories.size,
            thermalSystem: {
                temperature: this.thermalNeurogenesis.thermalPulse.currentTemp,
                pulsationsActive: this.thermalNeurogenesis.thermalPulse.enabled,
                neurogenesisActive: this.thermalNeurogenesis.temperatureNeurogenesis.enabled
            }
        };
    }
}

// 🧠 INITIALISER LE CERVEAU AUTONOME GLOBAL
global.artificialBrain = new AutonomousBrain();
console.log('🧠 Cerveau autonome thermique initialisé globalement');

// 🧮 FONCTION DE CALCUL QI DYNAMIQUE UNIFIÉ
function calculateDynamicQI() {
    try {
        const brain = global.artificialBrain.metrics;
        const thermal = global.thermalMemory;

        // 🧠 QI DE BASE
        const baseQI = 100;

        // 🧬 BONUS NEURONES (Max +75)
        const neuronBonus = Math.min(75, Math.floor(brain.activeNeurons / 15000));

        // 🎓 BONUS FORMATIONS (Max +150)
        const formationBonus = Math.min(150, Math.floor(brain.formationNeurons / 50000));

        // 🌡️ BONUS THERMIQUE (Max +50)
        const tempOptimal = 37.2;
        const tempDiff = Math.abs(brain.temperature - tempOptimal);
        const thermalBonus = Math.max(0, 50 - (tempDiff * 10));

        // ⚡ BONUS EFFICACITÉ (Max +40)
        const efficiencyBonus = Math.min(40, (brain.cognitiveEfficiency - 90) * 4);

        // 🏗️ BONUS ARCHITECTURE (Max +35)
        const architectureBonus = Math.min(35, (brain.memoryZones * 4) + (brain.processingSpeed * 8));

        // 🌌 BONUS QUANTIQUE (Max +75)
        const quantumBonus = Math.min(75, Math.floor(brain.quantumConnections / 30000));

        // 🎯 QI TOTAL CALCULÉ
        const calculatedQI = baseQI + neuronBonus + formationBonus + thermalBonus +
                            efficiencyBonus + architectureBonus + quantumBonus;

        // 📊 LIMITER AU MAXIMUM RÉALISTE (425)
        const finalQI = Math.min(calculatedQI, 425);

        // 🔄 METTRE À JOUR LE QI
        global.artificialBrain.metrics.qi = finalQI;

        console.log(`🧮 QI calculé dynamiquement: ${finalQI} = ${baseQI} + ${neuronBonus} + ${formationBonus} + ${thermalBonus} + ${efficiencyBonus} + ${architectureBonus} + ${quantumBonus}`);

        return finalQI;

    } catch (error) {
        console.error('❌ Erreur calcul QI dynamique:', error);
        return 225; // Valeur de fallback
    }
}

// 🔄 FONCTION DE MISE À JOUR CONTINUE DES MÉTRIQUES
function updateBrainMetrics() {
    try {
        const brain = global.artificialBrain.metrics;

        // 🌡️ MISE À JOUR TEMPÉRATURE AVEC VARIATION NATURELLE
        const baseTemp = 37.2;
        const variation = (Math.sin(Date.now() / 60000) * 0.3) + (Math.random() - 0.5) * 0.1;
        brain.temperature = baseTemp + variation;

        // 🧠 CROISSANCE NEURONALE CONTINUE
        if (Math.random() < 0.1) { // 10% de chance
            brain.activeNeurons += Math.floor(Math.random() * 100) + 50;
            brain.synapticConnections += Math.floor(Math.random() * 200) + 100;
        }

        // ⚡ AMÉLIORATION CONTINUE DE L'EFFICACITÉ
        if (brain.cognitiveEfficiency < 99.9) {
            brain.cognitiveEfficiency += Math.random() * 0.01;
        }

        // 🔄 RECALCULER LE QI
        calculateDynamicQI();

        brain.lastUpdate = Date.now();

    } catch (error) {
        console.error('❌ Erreur mise à jour métriques:', error);
    }
}

// 🔄 MISE À JOUR CONTINUE DES MÉTRIQUES ET QI
setInterval(updateBrainMetrics, 5000); // Toutes les 5 secondes
setInterval(calculateDynamicQI, 10000); // Recalcul QI toutes les 10 secondes

// 🌡️ INITIALISER LA MÉMOIRE THERMIQUE SIMPLE
global.thermalMemory = {
    temperature: 37.0,
    entries: [],

    updateTemperature(temp) {
        this.temperature = temp;
    },

    addEntry(entry) {
        this.entries.push({
            ...entry,
            timestamp: Date.now()
        });

        // Garder seulement les 1000 dernières entrées
        if (this.entries.length > 1000) {
            this.entries = this.entries.slice(-1000);
        }
    },

    getDetailedStats() {
        return {
            temperature: this.temperature,
            totalEntries: this.entries.length,
            averageTemperature: this.temperature,
            memoryEfficiency: 95.5
        };
    }
};

// 🚀 INITIALISER LES MODULES ESSENTIELS
try {
    // Modules de base
    const DesktopActionsSystem = require('./modules/desktop-actions-system');
    const InternetSearchSystem = require('./modules/internet-search-system');
    const AIIQCalculator = require('./modules/ai-iq-calculator');
    const SystemMonitor = require('./modules/system-monitor');
    const ThermalMemoryQIEnhancer = require('./modules/thermal-memory-qi-enhancer');

    // Initialiser les systèmes
    global.desktopActions = new DesktopActionsSystem();
    global.internetSearch = new InternetSearchSystem();
    global.aiIQCalculator = new AIIQCalculator();
    global.systemMonitor = new SystemMonitor();

    // 🧠 INITIALISER L'AMÉLIORATEUR QI MÉMOIRE THERMIQUE
    global.qiEnhancer = new ThermalMemoryQIEnhancer(global.thermalMemory);
    console.log('🧠 Améliorateur QI mémoire thermique initialisé - QI cible: 300');

    console.log('✅ Modules essentiels initialisés');
} catch (error) {
    console.log('⚠️ Certains modules optionnels non disponibles:', error.message);
}

// 🚀 CRÉER L'APPLICATION EXPRESS
const app = express();
const PORT = process.env.PORT || 0; // Laisser le système choisir un port libre

// Middleware de base
app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// 🧠 API POUR LE CERVEAU AUTONOME AVEC QI CALCULÉ
app.get('/api/brain/stats', (req, res) => {
    try {
        const stats = global.artificialBrain.getStats();

        // 🧮 AJOUTER LE QI CALCULÉ DYNAMIQUEMENT
        const dynamicQI = calculateDynamicQI();
        stats.qi = {
            current: dynamicQI,
            base: 100,
            target: 425,
            total: dynamicQI
        };

        res.json({
            success: true,
            brain: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération stats cerveau'
        });
    }
});

// 🌡️ API POUR LA MÉMOIRE THERMIQUE
app.get('/api/thermal/stats', (req, res) => {
    try {
        const stats = global.thermalMemory.getDetailedStats();
        res.json({
            success: true,
            thermal: stats,
            timestamp: new Date().toISOString()
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération stats thermiques'
        });
    }
});

// 🖥️ API POUR LES ACTIONS BUREAU
app.get('/api/desktop/applications', (req, res) => {
    try {
        if (global.desktopActions) {
            const apps = global.desktopActions.getApplicationsList();
            res.json({
                success: true,
                applications: apps,
                count: apps.length,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Système d\'actions bureau non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération applications'
        });
    }
});

// 🌐 API POUR LA RECHERCHE INTERNET
app.post('/api/search', async (req, res) => {
    try {
        const { query } = req.body;

        if (global.internetSearch) {
            const results = await global.internetSearch.search(query);
            res.json({
                success: true,
                results: results,
                query: query,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Système de recherche Internet non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur recherche Internet'
        });
    }
});

// 🧮 API POUR LE CALCUL DE QI
app.get('/api/iq/calculate', (req, res) => {
    try {
        if (global.aiIQCalculator) {
            const iqData = global.aiIQCalculator.calculateRealTimeIQ();
            res.json({
                success: true,
                iq: iqData,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Calculateur de QI non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur calcul QI'
        });
    }
});

// 🧠 API POUR L'AMÉLIORATEUR QI MÉMOIRE THERMIQUE
app.get('/api/qi/enhanced', (req, res) => {
    try {
        if (global.qiEnhancer) {
            const enhancedQI = global.qiEnhancer.calculateEnhancedQI();
            const metrics = global.qiEnhancer.getQIMetrics();
            res.json({
                success: true,
                enhancedQI: enhancedQI,
                metrics: metrics,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Améliorateur QI non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur calcul QI amélioré'
        });
    }
});

// 🚀 API POUR FORCER UNE AMÉLIORATION COMPLÈTE
app.post('/api/qi/enhance', (req, res) => {
    try {
        if (global.qiEnhancer) {
            const newQI = global.qiEnhancer.forceFullEnhancement();

            // 🔄 METTRE À JOUR LES MÉTRIQUES DU CERVEAU
            updateBrainMetrics();
            const finalQI = calculateDynamicQI();

            res.json({
                success: true,
                message: 'Amélioration complète effectuée',
                newQI: finalQI,
                enhancerQI: newQI,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Améliorateur QI non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur amélioration QI'
        });
    }
});

// 📊 API POUR MÉTRIQUES COMPLÈTES UNIFIÉES
app.get('/api/metrics', (req, res) => {
    try {
        const brainStats = global.artificialBrain.getStats();
        const thermalStats = global.thermalMemory.getDetailedStats();
        const dynamicQI = calculateDynamicQI();

        // 🧠 MÉTRIQUES UNIFIÉES
        const unifiedMetrics = {
            brain: {
                ...brainStats,
                qi: {
                    current: dynamicQI,
                    base: 100,
                    target: 425,
                    total: dynamicQI
                }
            },
            thermal: thermalStats,
            qi: {
                unified: dynamicQI,
                calculation: {
                    base: 100,
                    bonuses: {
                        neurons: Math.min(75, Math.floor(brainStats.activeNeurons / 15000)),
                        formations: Math.min(150, Math.floor(brainStats.formationNeurons / 50000)),
                        thermal: Math.max(0, 50 - (Math.abs(brainStats.temperature - 37.2) * 10)),
                        efficiency: Math.min(40, (brainStats.cognitiveEfficiency - 90) * 4),
                        architecture: Math.min(35, (brainStats.memoryZones * 4) + (brainStats.processingSpeed * 8)),
                        quantum: Math.min(75, Math.floor(brainStats.quantumConnections / 30000))
                    }
                }
            },
            timestamp: new Date().toISOString()
        };

        res.json({
            success: true,
            ...unifiedMetrics
        });

    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur récupération métriques unifiées'
        });
    }
});

// 📊 API POUR LE MONITORING SYSTÈME
app.get('/api/system/monitor', (req, res) => {
    try {
        if (global.systemMonitor) {
            const systemStats = global.systemMonitor.getSystemStats();
            res.json({
                success: true,
                system: systemStats,
                timestamp: new Date().toISOString()
            });
        } else {
            res.json({
                success: false,
                error: 'Moniteur système non disponible'
            });
        }
    } catch (error) {
        res.status(500).json({
            success: false,
            error: 'Erreur monitoring système'
        });
    }
});

// 🏠 PAGE D'ACCUEIL
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// 🚀 DÉMARRER LE SERVEUR
let server;

function startServer() {
    server = app.listen(PORT, () => {
        const actualPort = server.address().port;

        console.log(`
🎉 ===================================
🚀 LOUNA AI SERVEUR DÉMARRÉ !
🌐 URL: http://localhost:${actualPort}
📱 Interface principale: http://localhost:${actualPort}/
🧠 Cerveau autonome: ACTIF avec pulsations thermiques
🌡️ Mémoire thermique: ACTIVE à ${global.thermalMemory.temperature}°C
🖥️ Actions bureau: ${global.desktopActions ? 'ACTIVES' : 'DÉSACTIVÉES'}
🌐 Recherche Internet: ${global.internetSearch ? 'ACTIVE' : 'DÉSACTIVÉE'}
🧮 Calcul QI: ${global.aiIQCalculator ? 'ACTIF' : 'DÉSACTIVÉ'}
📊 Monitoring: ${global.systemMonitor ? 'ACTIF' : 'DÉSACTIVÉ'}
🎯 Toutes les fonctionnalités sont maintenant disponibles !
===================================
        `);

        console.log('\n🎉 ===== LOUNA DÉMARRÉ AVEC SUCCÈS ! =====');
        console.log(`🌐 Port utilisé: ${actualPort}`);
        console.log('🧠 Cerveau Autonome Thermique:');
        console.log('   🌡️ Pulsations thermiques automatiques');
        console.log('   🧬 Neurogenèse basée sur température');
        console.log('   💭 Pensées spontanées continues');
        console.log('   🔥 Système vivant comme un vrai cerveau');
        console.log('   ⚡ QI 225 avec mémoire thermique + formations (1,064,000 neurones)');
        console.log('==========================================\n');

        // Notifier Electron que le serveur est prêt
        if (process.send) {
            process.send('server-ready');
        }
    });
}

// Démarrer le serveur immédiatement
startServer();

// Gestion des erreurs du serveur
if (server) {
    server.on('error', (error) => {
        console.error('❌ Erreur du serveur:', error.message);

        if (error.code === 'EADDRINUSE') {
            console.log(`❌ Le port ${PORT} est déjà utilisé. Essayez un autre port.`);
            process.exit(1);
        }
    });
}

// Gestion de l'arrêt propre
process.on('SIGTERM', () => {
    console.log('🛑 Signal SIGTERM reçu, arrêt du serveur...');
    if (server) {
        server.close(() => {
            console.log('✅ Serveur arrêté proprement');
            process.exit(0);
        });
    }
});

process.on('SIGINT', () => {
    console.log('🛑 Signal SIGINT reçu, arrêt du serveur...');
    if (server) {
        server.close(() => {
            console.log('✅ Serveur arrêté proprement');
            process.exit(0);
        });
    }
});

module.exports = app;
