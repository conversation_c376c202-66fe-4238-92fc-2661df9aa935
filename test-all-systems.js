/**
 * 🧪 SCRIPT DE TEST COMPLET - LOUNA AI
 * Test automatique de tous les systèmes et corrections
 */

const { ipcRenderer } = require('electron');

class SystemTester {
    constructor() {
        this.results = {
            passed: 0,
            failed: 0,
            total: 0,
            details: []
        };
        
        console.log('🧪 Démarrage des tests complets LOUNA AI...');
    }

    // Exécuter tous les tests
    async runAllTests() {
        console.log('\n🚀 === DÉBUT DES TESTS COMPLETS ===\n');

        // Tests des systèmes de base
        await this.testBasicSystems();
        
        // Tests des nouvelles fonctionnalités
        await this.testNewFeatures();
        
        // Tests de performance et optimisation
        await this.testPerformanceOptimization();
        
        // Tests de réactivation
        await this.testReactivationSystems();
        
        // Tests MCP
        await this.testMCPSystems();
        
        // Afficher les résultats
        this.displayResults();
    }

    // Test des systèmes de base
    async testBasicSystems() {
        console.log('📊 === TESTS SYSTÈMES DE BASE ===');
        
        await this.test('DeepSeek Status', async () => {
            const result = await ipcRenderer.invoke('deepseek-status');
            return result.success && result.connected;
        });

        await this.test('Thermal Memory Stats', async () => {
            const result = await ipcRenderer.invoke('thermal-memory-stats');
            return result.success && result.totalEntries >= 0;
        });

        await this.test('System Health', async () => {
            const result = await ipcRenderer.invoke('system-health');
            return result.success;
        });

        await this.test('Get State', async () => {
            const result = await ipcRenderer.invoke('get-state');
            return result.success && result.state;
        });
    }

    // Test des nouvelles fonctionnalités
    async testNewFeatures() {
        console.log('\n🆕 === TESTS NOUVELLES FONCTIONNALITÉS ===');
        
        await this.test('Memory Stats', async () => {
            const result = await ipcRenderer.invoke('memory-stats');
            return result.success && result.stats;
        });

        await this.test('Optimize Memory', async () => {
            const result = await ipcRenderer.invoke('optimize-memory', 'normal');
            return result.success;
        });

        await this.test('MCP Status', async () => {
            const result = await ipcRenderer.invoke('mcp-status');
            return result.success !== undefined; // Peut être true ou false
        });
    }

    // Test de performance et optimisation
    async testPerformanceOptimization() {
        console.log('\n⚡ === TESTS PERFORMANCE ET OPTIMISATION ===');
        
        await this.test('Memory Optimization Normal', async () => {
            const result = await ipcRenderer.invoke('optimize-memory', 'normal');
            return result.success && result.level === 'normal';
        });

        await this.test('Memory Optimization Warning', async () => {
            const result = await ipcRenderer.invoke('optimize-memory', 'warning');
            return result.success && result.level === 'warning';
        });

        await this.test('Memory Stats After Optimization', async () => {
            const result = await ipcRenderer.invoke('memory-stats');
            return result.success && result.stats && result.stats.optimizer;
        });
    }

    // Test des systèmes de réactivation
    async testReactivationSystems() {
        console.log('\n🔄 === TESTS SYSTÈMES DE RÉACTIVATION ===');
        
        await this.test('Reactivate All Systems', async () => {
            const result = await ipcRenderer.invoke('reactivate-all-systems');
            return result.success && result.results;
        });

        await this.test('System Health After Reactivation', async () => {
            // Attendre un peu après la réactivation
            await new Promise(resolve => setTimeout(resolve, 1000));
            const result = await ipcRenderer.invoke('system-health');
            return result.success;
        });
    }

    // Test des systèmes MCP
    async testMCPSystems() {
        console.log('\n🌐 === TESTS SYSTÈMES MCP ===');
        
        await this.test('MCP Status Check', async () => {
            const result = await ipcRenderer.invoke('mcp-status');
            return result !== undefined;
        });

        await this.test('MCP Start Dialogue', async () => {
            const result = await ipcRenderer.invoke('mcp-start-dialogue', {
                mode: 'test',
                capabilities: ['test']
            });
            return result !== undefined; // Peut réussir ou échouer selon l'état
        });
    }

    // Exécuter un test individuel
    async test(name, testFunction) {
        this.results.total++;
        
        try {
            console.log(`🧪 Test: ${name}...`);
            const startTime = Date.now();
            
            const success = await testFunction();
            const duration = Date.now() - startTime;
            
            if (success) {
                this.results.passed++;
                console.log(`✅ ${name} - RÉUSSI (${duration}ms)`);
                this.results.details.push({
                    name,
                    status: 'PASSED',
                    duration,
                    error: null
                });
            } else {
                this.results.failed++;
                console.log(`❌ ${name} - ÉCHEC (${duration}ms)`);
                this.results.details.push({
                    name,
                    status: 'FAILED',
                    duration,
                    error: 'Test returned false'
                });
            }
        } catch (error) {
            this.results.failed++;
            console.log(`❌ ${name} - ERREUR: ${error.message}`);
            this.results.details.push({
                name,
                status: 'ERROR',
                duration: 0,
                error: error.message
            });
        }
    }

    // Afficher les résultats finaux
    displayResults() {
        console.log('\n🎯 === RÉSULTATS FINAUX ===');
        console.log(`📊 Total: ${this.results.total} tests`);
        console.log(`✅ Réussis: ${this.results.passed}`);
        console.log(`❌ Échecs: ${this.results.failed}`);
        console.log(`📈 Taux de réussite: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
        
        if (this.results.failed > 0) {
            console.log('\n❌ TESTS ÉCHOUÉS:');
            this.results.details
                .filter(test => test.status !== 'PASSED')
                .forEach(test => {
                    console.log(`  - ${test.name}: ${test.error || 'Échec'}`);
                });
        }
        
        console.log('\n🎉 === TESTS TERMINÉS ===');
        
        // Retourner les résultats pour utilisation externe
        return this.results;
    }

    // Test de stress mémoire
    async stressTestMemory() {
        console.log('\n💪 === TEST DE STRESS MÉMOIRE ===');
        
        // Créer une charge mémoire temporaire
        const largeArray = new Array(100000).fill('test data for memory stress');
        
        await this.test('Memory Under Stress', async () => {
            const result = await ipcRenderer.invoke('memory-stats');
            return result.success;
        });
        
        await this.test('Optimization Under Stress', async () => {
            const result = await ipcRenderer.invoke('optimize-memory', 'critical');
            return result.success;
        });
        
        // Nettoyer
        largeArray.length = 0;
        
        console.log('✅ Test de stress mémoire terminé');
    }

    // Test de connectivité réseau
    async testNetworkConnectivity() {
        console.log('\n🌐 === TEST CONNECTIVITÉ RÉSEAU ===');
        
        await this.test('Local Server Connectivity', async () => {
            try {
                const response = await fetch('http://localhost:52796/');
                return response.ok;
            } catch (error) {
                return false;
            }
        });
    }

    // Test complet avec stress
    async runCompleteTests() {
        await this.runAllTests();
        await this.stressTestMemory();
        await this.testNetworkConnectivity();
        
        return this.results;
    }
}

// Fonction d'export pour utilisation dans les interfaces
window.SystemTester = SystemTester;

// Auto-exécution si dans un contexte de test
if (typeof window !== 'undefined' && window.location && window.location.search.includes('autotest=true')) {
    document.addEventListener('DOMContentLoaded', async () => {
        const tester = new SystemTester();
        await tester.runCompleteTests();
    });
}

// Export pour Node.js
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SystemTester;
}

console.log('🧪 Script de test complet chargé et prêt !');
