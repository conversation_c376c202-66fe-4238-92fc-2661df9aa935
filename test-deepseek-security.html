<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Test Sécurité DeepSeek R1 8B</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a2e, #16213e, #0f3460);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            margin-bottom: 30px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .security-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .control-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .control-section h3 {
            color: #ff6b6b;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .btn.success {
            background: linear-gradient(45deg, #00d4ff, #0984e3);
        }
        
        .btn.success:hover {
            box-shadow: 0 5px 15px rgba(0, 212, 255, 0.4);
        }
        
        .btn.warning {
            background: linear-gradient(45deg, #fdcb6e, #e17055);
        }
        
        .btn.warning:hover {
            box-shadow: 0 5px 15px rgba(253, 203, 110, 0.4);
        }
        
        .status-display {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #00d4ff;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-value {
            font-weight: bold;
            color: #00d4ff;
        }
        
        .log-container {
            background: rgba(0, 0, 0, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info { background: rgba(0, 212, 255, 0.1); }
        .log-success { background: rgba(0, 255, 0, 0.1); }
        .log-warning { background: rgba(255, 193, 7, 0.1); }
        .log-error { background: rgba(255, 107, 107, 0.1); }
        
        .auth-input {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            color: white;
            width: 200px;
            margin: 10px;
        }
        
        .auth-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .metric-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #00d4ff;
            margin: 10px 0;
        }
        
        .metric-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Contrôle Sécurité DeepSeek R1 8B Ultra-Robuste</h1>
        
        <div class="security-panel">
            <div class="control-section">
                <h3>🔒 Contrôles de Déconnexion</h3>
                <button class="btn" onclick="guardianDisconnect()">
                    🛡️ Déconnexion Agent Gardien
                </button>
                <button class="btn" onclick="emergencyStop()">
                    🚨 Arrêt d'Urgence
                </button>
                <button class="btn warning" onclick="manualDisconnect()">
                    👤 Déconnexion Manuelle
                </button>
            </div>
            
            <div class="control-section">
                <h3>🔄 Contrôles de Reconnexion</h3>
                <input type="text" class="auth-input" id="authCode" placeholder="Code d'autorisation">
                <br>
                <button class="btn success" onclick="reactivateConnection()">
                    ✅ Réactiver Connexion
                </button>
                <button class="btn success" onclick="testConnection()">
                    🧪 Test Connexion
                </button>
            </div>
        </div>
        
        <div class="status-display">
            <h3>📊 Statut de Sécurité</h3>
            <div id="securityStatus">Chargement...</div>
        </div>
        
        <div class="metrics-grid" id="metricsGrid">
            <!-- Métriques dynamiques -->
        </div>
        
        <div class="log-container">
            <h3>📝 Journal de Sécurité</h3>
            <div id="securityLog"></div>
        </div>
    </div>

    <script>
        let logContainer = document.getElementById('securityLog');
        let statusContainer = document.getElementById('securityStatus');
        let metricsContainer = document.getElementById('metricsGrid');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        async function guardianDisconnect() {
            try {
                addLog('🛡️ Demande de déconnexion par agent gardien...', 'warning');
                const result = await window.electronAPI.invoke('deepseek-guardian-disconnect', 'Déconnexion demandée via interface de test');
                
                if (result.success) {
                    addLog('✅ Déconnexion par agent gardien réussie', 'success');
                } else {
                    addLog(`❌ Échec déconnexion: ${result.error}`, 'error');
                }
                
                updateSecurityStatus();
            } catch (error) {
                addLog(`❌ Erreur déconnexion gardien: ${error.message}`, 'error');
            }
        }
        
        async function emergencyStop() {
            try {
                addLog('🚨 DÉCLENCHEMENT ARRÊT D\'URGENCE...', 'error');
                const result = await window.electronAPI.invoke('deepseek-emergency-stop', 'Arrêt d\'urgence via interface de test');
                
                if (result.success) {
                    addLog('🚨 ARRÊT D\'URGENCE ACTIVÉ', 'error');
                } else {
                    addLog(`❌ Échec arrêt d'urgence: ${result.error}`, 'error');
                }
                
                updateSecurityStatus();
            } catch (error) {
                addLog(`❌ Erreur arrêt d'urgence: ${error.message}`, 'error');
            }
        }
        
        async function manualDisconnect() {
            try {
                addLog('👤 Déconnexion manuelle demandée...', 'warning');
                const result = await window.electronAPI.invoke('deepseek-guardian-disconnect', 'Déconnexion manuelle via interface');
                
                if (result.success) {
                    addLog('✅ Déconnexion manuelle réussie', 'success');
                } else {
                    addLog(`❌ Échec déconnexion manuelle: ${result.error}`, 'error');
                }
                
                updateSecurityStatus();
            } catch (error) {
                addLog(`❌ Erreur déconnexion manuelle: ${error.message}`, 'error');
            }
        }
        
        async function reactivateConnection() {
            try {
                const authCode = document.getElementById('authCode').value;
                addLog(`🔄 Tentative de réactivation avec code: ${authCode || 'aucun'}...`, 'info');
                
                const result = await window.electronAPI.invoke('deepseek-reactivate', authCode);
                
                if (result.success && result.reactivated) {
                    addLog('✅ CONNEXION RÉACTIVÉE AVEC SUCCÈS', 'success');
                } else {
                    addLog(`❌ Échec réactivation: ${result.error || 'Connexion non établie'}`, 'error');
                }
                
                updateSecurityStatus();
            } catch (error) {
                addLog(`❌ Erreur réactivation: ${error.message}`, 'error');
            }
        }
        
        async function testConnection() {
            try {
                addLog('🧪 Test de connexion DeepSeek...', 'info');
                const result = await window.electronAPI.invoke('deepseek-chat', 'Test de connexion sécurisée. Réponds: OK');
                
                if (result.success) {
                    addLog(`✅ Test réussi: ${result.response.substring(0, 100)}...`, 'success');
                } else {
                    addLog(`❌ Test échoué: ${result.error}`, 'error');
                }
            } catch (error) {
                addLog(`❌ Erreur test: ${error.message}`, 'error');
            }
        }
        
        async function updateSecurityStatus() {
            try {
                const result = await window.electronAPI.invoke('deepseek-security-status');
                
                if (result.success) {
                    const report = result.report;
                    
                    // Afficher le statut
                    statusContainer.innerHTML = `
                        <div class="status-item">
                            <span>Statut Agent Gardien:</span>
                            <span class="status-value">${report.guardianStatus.active ? '🟢 ACTIF' : '🔴 INACTIF'}</span>
                        </div>
                        <div class="status-item">
                            <span>Connexion DeepSeek:</span>
                            <span class="status-value">${report.connectionStatus.connected ? '🟢 CONNECTÉ' : '🔴 DÉCONNECTÉ'}</span>
                        </div>
                        <div class="status-item">
                            <span>Statut Sécurité:</span>
                            <span class="status-value">${report.connectionStatus.securityAgent.emergencyStop ? '🚨 ARRÊT D\'URGENCE' : '🟢 NORMAL'}</span>
                        </div>
                        <div class="status-item">
                            <span>Peut Reconnecter:</span>
                            <span class="status-value">${report.connectionStatus.canReconnect ? '✅ OUI' : '❌ NON'}</span>
                        </div>
                    `;
                    
                    // Afficher les métriques
                    metricsContainer.innerHTML = `
                        <div class="metric-card">
                            <div class="metric-value">${report.metrics.totalRequests}</div>
                            <div class="metric-label">Requêtes Totales</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${report.metrics.blockedRequests}</div>
                            <div class="metric-label">Requêtes Bloquées</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${report.metrics.emergencyStops}</div>
                            <div class="metric-label">Arrêts d'Urgence</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${report.connectionStatus.connectionHealth.consecutiveFailures}</div>
                            <div class="metric-label">Échecs Consécutifs</div>
                        </div>
                    `;
                    
                } else {
                    statusContainer.innerHTML = `<div class="status-item"><span>❌ Erreur: ${result.error}</span></div>`;
                }
            } catch (error) {
                addLog(`❌ Erreur mise à jour statut: ${error.message}`, 'error');
            }
        }
        
        // Mise à jour automatique du statut
        setInterval(updateSecurityStatus, 5000);
        
        // Initialisation
        document.addEventListener('DOMContentLoaded', () => {
            addLog('🛡️ Interface de contrôle sécurité initialisée', 'success');
            updateSecurityStatus();
        });
        
        // Simuler l'API Electron pour les tests
        if (!window.electronAPI) {
            window.electronAPI = {
                invoke: async (channel, ...args) => {
                    addLog(`🔧 Simulation API: ${channel}`, 'info');
                    return { success: false, error: 'Mode simulation - Electron non disponible' };
                }
            };
        }
    </script>
</body>
</html>
